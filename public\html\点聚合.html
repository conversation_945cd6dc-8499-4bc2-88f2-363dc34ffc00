<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>点聚合</title>
    <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css"/>
    <style>
        html, body, #container {
            height: 100%;
            width: 100%;
        }

        .input-card {
            width: 25rem;
        }

        .input-card .btn {
            width: 7rem;
            margin-right: .7rem;
        }

        .input-card .btn:last-child {
            margin-right: 0;
        }
    </style>
</head>
<body>
<div id="container" class="map" tabindex="0"></div>
<div class="input-card">
    <h4>聚合点效果切换</h4>
    <div class="input-item">
        <input type="button" class="btn" value="默认样式" id="add0" onclick='addCluster(0)'/>
        <input type="button" class="btn" value="自定义图标" id="add1" onclick='addCluster(1)'/>
        <input type="button" class="btn" value="完全自定义" id="add2" onclick='addCluster(2)'/>
    </div>
</div>
<script src="//a.amap.com/jsapi_demos/static/china.js"></script>


<script src="https://webapi.amap.com/maps?v=2.0&key=f321b651170b76b298d05d0914d8fe99&plugin=AMap.MarkerCluster"></script>

<script type="text/javascript">
    var cluster;

    var map = new AMap.Map("container", {
        center: [121.**************, 31.202250090600185],
        zoom: 15
    });

    var gridSize = 60
    // 数据中需包含经纬度信息字段 lnglat
    // var points = [
    // { lnglat: ["108.939621", "34.343147"] },
    // { lnglat: ["112.985037", "23.15046"] },
    // ]
    var count = points.length;

    var _renderClusterMarker = function (context) {
        var factor = Math.pow(context.count / count, 1 / 18);
        var div = document.createElement('div');
        var Hue = 180 - factor * 180;
        var bgColor = 'hsla(' + Hue + ',100%,40%,0.7)';
        var fontColor = 'hsla(' + Hue + ',100%,90%,1)';
        var borderColor = 'hsla(' + Hue + ',100%,40%,1)';
        var shadowColor = 'hsla(' + Hue + ',100%,90%,1)';
        div.style.backgroundColor = bgColor;
        var size = Math.round(30 + Math.pow(context.count / count, 1 / 5) * 20);
        div.style.width = div.style.height = size + 'px';
        div.style.border = 'solid 1px ' + borderColor;
        div.style.borderRadius = size / 2 + 'px';
        div.style.boxShadow = '0 0 5px ' + shadowColor;
        div.innerHTML = context.count;
        div.style.lineHeight = size + 'px';
        div.style.color = fontColor;
        div.style.fontSize = '14px';
        div.style.textAlign = 'center';
        context.marker.setOffset(new AMap.Pixel(-size / 2, -size / 2));
        context.marker.setContent(div)
    };
    var _renderMarker = function(context) {
        var content = '<div style="background-color: hsla(180, 100%, 50%, 0.3); height: 18px; width: 18px; border: 1px solid hsl(180, 100%, 40%); border-radius: 12px; box-shadow: hsl(180, 100%, 50%) 0px 0px 3px;"></div>';
        var offset = new AMap.Pixel(-9, -9);
        context.marker.setContent(content)
        context.marker.setOffset(offset)
    }

    addCluster(2);

    function addCluster(tag) {
        if (cluster) {
            cluster.setMap(null);
        }
        if (tag == 2) {//完全自定义
            cluster = new AMap.MarkerCluster(map, points, {
                gridSize: gridSize, // 设置网格像素大小
                renderClusterMarker: _renderClusterMarker, // 自定义聚合点样式
                // renderMarker: _renderMarker, // 自定义非聚合点样式
                renderMarker: _renderMarker, // 自定义非聚合点样式
            });
        }
        else if (tag == 1) {//自定义图标
            var sts = [
                {
                url: "//a.amap.com/jsapi_demos/static/images/blue.png",
                size: new AMap.Size(32, 32),
                offset: new AMap.Pixel(-16, -16)
            }, {
                url: "//a.amap.com/jsapi_demos/static/images/green.png",
                size: new AMap.Size(32, 32),
                offset: new AMap.Pixel(-16, -16)
            }, {
                url: "//a.amap.com/jsapi_demos/static/images/orange.png",
                size: new AMap.Size(36, 36),
                offset: new AMap.Pixel(-18, -18)
            }, {
                url: "//a.amap.com/jsapi_demos/static/images/red.png",
                size: new AMap.Size(48, 48),
                offset: new AMap.Pixel(-24, -24)
            }, {
                url: "//a.amap.com/jsapi_demos/static/images/darkRed.png",
                size: new AMap.Size(48, 48),
                offset: new AMap.Pixel(-24, -24)
            }];
            cluster = new AMap.MarkerCluster(map, points, {
                styles: sts,
                gridSize: gridSize
            });
        }
        else {//默认样式
            cluster = new AMap.MarkerCluster(map, points, {gridSize: gridSize});
        }
    }
</script>
</body>
</html>
