<template>
    <a-spin :spinning="confirmLoading">
        <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
            <a-row>
                <a-col :span="12">
                    <a-form-item label="设备分类" name="deviceCategory" :labelCol="labelCol" :wrapperCol="wrapperCol"
                                 v-bind="validateInfos.deviceCategory">
                        <JSelectMultiple @change="getSelectCategory" v-model:value="formState.deviceCategory"
                                         placeholder="请选择设备分类"
                                         :options="options" mode="default" :triggerChange="false" :disabled="editdisabled">
                        </JSelectMultiple>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="设备类型" name="deviceType" :labelCol="labelCol" :wrapperCol="wrapperCol" v-bind="validateInfos.deviceType">
                        <JSelectMultiple v-model:value="formState.deviceType" placeholder="请选择设备类型" :options="options2"
                                         mode="default"
                                         :triggerChange="false" :disabled="editdisabled"></JSelectMultiple>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="告警编码" name="alarmCode" :labelCol="labelCol" :wrapperCol="wrapperCol" v-bind="validateInfos.alarmCode">
                        <a-input placeholder="请输入告警编码" v-model:value="formState.alarmCode" :disabled="editdisabled"></a-input>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="告警级别" name="alarmLevel" :labelCol="labelCol" :wrapperCol="wrapperCol" v-bind="validateInfos.alarmLevel">
                        <JSelectMultiple v-model:value="formState.alarmLevel" placeholder="请选择告警级别" :options="options3"
                                         mode="default"
                                         :triggerChange="false"></JSelectMultiple>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item name="alarmDescription" label="告警描述" :labelCol="labelCols" :wrapperCol="wrapperCol" v-bind="validateInfos.alarmDescription">
                        <a-textarea v-model:value="formState.alarmDescription" placeholder="请输入告警描述"/>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item name="possibleCauses" label="可能原因" :labelCol="labelCols" :wrapperCol="wrapperCol" v-bind="validateInfos.possibleCauses">
                        <a-textarea v-model:value="formState.possibleCauses" placeholder="请输入可能原因"/>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item name="possibleConsequences" label="可能后果" :labelCol="labelCols"
                                 :wrapperCol="wrapperCol" v-bind="validateInfos.possibleConsequences">
                        <a-textarea v-model:value="formState.possibleConsequences" placeholder="请输入可能后果"/>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item name="proposalMeasures" label="建议措施" :labelCol="labelCols" :wrapperCol="wrapperCol" v-bind="validateInfos.proposalMeasures">
                        <a-textarea v-model:value="formState.proposalMeasures" placeholder="请输入建议措施"/>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="备注" :labelCol="labelCols" :wrapperCol="wrapperCol">
                        <a-textarea v-model:value="formState.remark" placeholder="请输入备注"/>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="是否启用" name="status" :labelCol="labelCol" :wrapperCol="wrapperCol" v-bind="validateInfos.status">
                        <JSwitch v-model:value="formState.status" :options="['1', '0']"
                                 :labelOptions="['是', '否']"></JSwitch>
                    </a-form-item>
                </a-col>
            </a-row>
        </a-form>
    </a-spin>
</template>

<script lang="ts" setup>
    import {ref, reactive, onMounted, defineEmits, nextTick, defineExpose} from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {Form} from 'ant-design-vue';
    import { useMessage } from '/@/hooks/web/useMessage';
    import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
    import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';

    const options = ref<any>([])
    const options2 = ref<any>([])
    const options3 = ref<any>([])
    const Api = reactive<any>({
        getDeviceCategoryList: '/sys/dtDeviceCategory/getDeviceCategoryList',
        getDeviceTypeList: '/sys/dtDeviceCategory/getDeviceTypeList',
        getAlarmLevel: '/sys/dict/getDictItemList',
        add: '/sys/dtAlarmBase/addAlarmBase',
        edit: '/sys/dtAlarmBase/editAlarmBase'
    });
    const emit = defineEmits(['ok'])
    const labelCol = ref<any>({xs: {span: 24}, sm: {span: 6}});
    const labelCols = ref<any>({xs: {span: 24}, sm: {span: 3}});
    const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 16}});
    const confirmLoading = ref<boolean>(false);
    const formState = reactive({
        deviceCategory: '',
        deviceType: '',
        alarmLevel: '',
        alarmCode: '',
        alarmDescription: '',
        possibleCauses: '',
        possibleConsequences: '',
        proposalMeasures: '',
        remark: '',
        status: '1'
    });
    //表单验证
    const validatorRules = {
        deviceCategory: [{required: true, message: '请选择设备分类', trigger: 'change'}],
        deviceType: [{required: true, message: '请选择设备类型', trigger: 'change'}],
        alarmLevel: [{required: true, message: '请选择告警级别', trigger: 'change'}],
        alarmCode: [{required: true, message: '请输入告警编码'}],
        alarmDescription: [{required: true, message: '请输入告警描述'}],
        possibleCauses: [{required: true, message: '请输入可能原因'}],
        possibleConsequences: [{required: true, message: '请输入可能后果'}],
        proposalMeasures: [{required: true, message: '请输入建议措施'}],
        status: [{required: true, message: '请选择是否启用'}],
    };
    const formRef = ref();
    const useForm = Form.useForm;
    //update-begin---author:wangshuai ---date:20220616  for：报表示例验证修改------------
    const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});
    const editdisabled = ref<boolean>(false);
    const { createMessage } = useMessage();
    onMounted(() => {
        defHttp.get({url: Api.getAlarmLevel, params: {dictCode: 'alarmLevel'}}).then((e) => {
            options3.value = e
        });
        defHttp.get({url: Api.getDeviceCategoryList}).then((e) => {
            e.forEach(i => {
                i.label = i.deviceCategory
                i.value = i.id
            });
            options.value = e
        });
    })
    function add() {
        edit({});
    }

    function edit(record) {
        nextTick(() => {
            resetFields();
            if(record.id){
                editdisabled.value = true
            }else{
                editdisabled.value = false
            }
            //赋值
            Object.assign(formState, record);
        });
    }

    const getSelectCategory = (e: Object): void => {
        options2.length = 0
        defHttp.get({url: Api.getDeviceTypeList, params: {parentId: e}}).then((res) => {
            if (res.length != 0) {
                res.forEach(i => {
                    i.label = i.deviceType
                    i.value = i.id
                });
                options2.value = res
            }
        });
    }

    async function submitForm() {
        await validate();
        confirmLoading.value = true;
        let httpurl = '';
        let method = '';
        let params = {
            alarmLevel: formState.alarmLevel,
            alarmDescription: formState.alarmDescription,
            possibleCauses: formState.possibleCauses,
            possibleConsequences: formState.possibleConsequences,
            proposalMeasures: formState.proposalMeasures,
            remark: formState.remark,
            status: formState.status
        };
        if (!formState.id) {
            httpurl += Api.add;
            method = 'post';
            params.deviceCategory=formState.deviceCategory
            params.deviceType=formState.deviceType
            params.alarmCode=formState.alarmCode
        } else {
            httpurl += Api.edit;
            method = 'put';
            params.id=formState.id
        }
        defHttp.request({url: httpurl, params: params, method: method,}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                createMessage.success(res.message);
                emit('ok');
            } else {
                createMessage.warning(res.message);
            }
        }) .finally(() => {
            confirmLoading.value = false;
        });
    }


    // defineEmits(['submitForm', 'add'])
    defineExpose({
        add,
        edit,
        submitForm,
    });
</script>

<style lang="less" scoped>
    .chooseUnit {
        color: rgb(0, 153, 255);
        cursor: pointer;
    }

    .antd-modal-form {
        padding: 24px 24px 24px 24px;
    }

    .uploadBoxs {
        width: 50%;
        height: 150px;
        cursor: pointer;
        border: 1px dashed #1296db;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        border-radius: 10px;

        img {
            width: 30px;
            height: 30px;
        }
    }
</style>
