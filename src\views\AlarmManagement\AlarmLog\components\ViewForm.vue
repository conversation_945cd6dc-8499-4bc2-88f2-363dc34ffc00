<template>
  <a-spin :spinning="confirmLoading">
    <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="12">
            <a-form-item label="告警类型"  :wrapperCol="wrapperCol" :labelCol="labelCol">
              <span v-if="formState.alarmType == '1'">疑似火警</span>
              <span v-if="formState.alarmType == '2'">故障</span>
              <span v-if="formState.alarmType == '3'">隐患</span>
              <span v-if="formState.alarmType == '4'">其他</span>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="设备类型"  :wrapperCol="wrapperCol" :labelCol="labelCol">
              <span>{{ formState.deviceTypeName }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备名称"  :wrapperCol="wrapperCol" :labelCol="labelCol">
              <span>{{ formState.deviceName }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备位置"  :wrapperCol="wrapperCol" :labelCol="labelCol">
              <span>{{ formState.deviceLocation }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态"  :wrapperCol="wrapperCol" :labelCol="labelCol">
              <span v-if="formState.alarmStatus == '1'">待处理</span>
              <span v-if="formState.alarmStatus == '2'">已派单</span>
              <span v-if="formState.alarmStatus == '3'">已处理</span>
              <span v-if="formState.alarmStatus == '4'">关闭</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="逾期状态"  :wrapperCol="wrapperCol" :labelCol="labelCol">
              <span v-if="formState.overdueFlag == '0'">否</span>
              <span v-if="formState.overdueFlag == '1'">是</span>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="告警内容"  :wrapperCol="wrapperCol1" :labelCol="labelCol1">
              <span>{{ formState.alarmDescription }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="处理内容"  :wrapperCol="wrapperCol1" :labelCol="labelCol1">
              <span>{{ formState.disposeResult }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="处理人"  :wrapperCol="wrapperCol" :labelCol="labelCol">
              <span>{{ formState.disposeRealname }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="处理时间"  :wrapperCol="wrapperCol" :labelCol="labelCol">
              <span>{{ formState.disposeTime }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="图片"  :wrapperCol="wrapperCol1" :labelCol="labelCol1">
              <!--<img :src="formState.disposePicture" style="width: 100px">-->
              <Image :width="200" :src="formState.disposePicture"/>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>

    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import {Form, Image} from 'ant-design-vue';
  import {defineComponent, ref, reactive, onMounted, defineProps, defineExpose, UnwrapRef, nextTick, defineEmits} from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import JFormContainer from '@/components/Form/src/jeecg/components/JFormContainer.vue';
  import DateTimeSelect from '/@/components/Form/src/jeecg/components/serchselect/DateTimeSelect.vue';
  const props = defineProps(["disabled"])
  const emits = defineEmits(['success', 'register', 'ok'])

  const confirmLoading = ref<boolean>(false);
  const labelCol = ref<any>({xs: {span: 24}, sm: {span: 8}});
  const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 16}});
  const labelCol1 = ref<any>({xs: {span: 24}, sm: {span: 4}});
  const wrapperCol1 = ref<any>({xs: {span: 24}, sm: {span: 20}});
  const validatorRules = {
  };

  const Api = reactive({
    add: '/sys/dtInspectPlan/addInspectPlan',
  });

  const formState = reactive({
    alarmType: '',
    deviceTypeName: '',
    deviceName: '',
    deviceLocation: '',
    alarmStatus: '',
    overdueFlag: '',
    alarmDescription: '',
    disposeResult: '',
    disposeRealname: '',
    disposeTime: '',
    disposePicture: '',
  });
  const useForm = Form.useForm;
  const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

  // 开始时间
  function onTimeSelect(e) {
  }


  onMounted(() => {
    //初始化字典选项
  });

  function add() {
    edit({});
  }

  function edit(record) {
    nextTick(() => {
      resetFields();
      Object.assign(formState, record);
      formState.disposePicture=formState.disposePicture?window._CONFIG['imagesUrl']+formState.disposePicture:""
    });
  }

  /**
   * 提交数据
   */
  function submitForm() {
    emits('ok');
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });

</script>

<style scoped>
  .antd-modal-form {
    padding: 24px 24px 24px 24px;
  }
</style>
