<template>
  <BasicModal :title="title" :width="width" :visible="visible" :height="600" @ok="handleOk" :okText="'确认'"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" @cancel="handleCancel" cancelText="关闭">
    <DeptForm ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></DeptForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, nextTick, defineExpose, defineEmits } from 'vue';
  import DeptForm from './DeptForm.vue';
  import { BasicModal } from '/@/components/Modal';

  const title = ref < string > ('新增');
  const width = ref < number > (1200);
  const visible = ref < boolean > (false);
  const disableSubmit = ref < boolean > (false);
  const realForm = ref();
  const emit = defineEmits(['register', 'ok']);

  function add() {
    title.value = '新增';
    visible.value = true;
    nextTick(() => {
      realForm.value.add();
    });
  }

  function edit(record) {
    title.value = '编辑';
    // title.value = disableSubmit.value ? '详情' : '编辑';
    visible.value = true;
    nextTick(() => {
      realForm.value.edit(record);
    });
  }

  function handleOk() {
    realForm.value.submitForm();
  }

  function submitCallback() {
    handleCancel();
    emit('ok');
  }

  function handleCancel() {
    visible.value = false;
  }

  defineExpose({
    add,
    edit,
    disableSubmit,
  });
</script>

<style lang="less" scoped>
</style>
