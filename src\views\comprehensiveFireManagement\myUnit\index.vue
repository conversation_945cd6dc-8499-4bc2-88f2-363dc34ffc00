<!-- 我的单位 myUnit -->
<template >
  <a-card :bordered="false" style="height: 100%;">
    <a-form slot="detail">
      <a-row :gutter="10">
        <a-col :space="24" class="tabs-container">
          <a-tabs v-model:activeKey="activeKeys" @change="handleTabClick" >
            <a-tab-pane key="1" tab="单位详情" >
              <a-col :span="24" >
                <BasicInformation ref="BasicInformation1" :unitId="unitId" height="70vh" overflow="auto"></BasicInformation>
              </a-col>
            </a-tab-pane>
            <a-tab-pane key="2" tab="联网系统" v-if="unitType=='2'">
              <a-col :span="24">
                <NetworkingSystem ref="NetworkingSystem1" :unitId="unitId" height="70vh" overflow="auto"></NetworkingSystem>
              </a-col>
            </a-tab-pane>
            <a-tab-pane key="3" tab="安全评分" v-if="unitType!=='2'">
              <a-col :span="24">
                <a-card>
                  <SafetyRating ref="SafetyRating1" :unitId="unitId" height="70vh" overflow="auto"></SafetyRating>
                </a-card>
              </a-col>
            </a-tab-pane>
            <a-tab-pane key="4" tab="历史告警" v-if="unitType!=='2'">
              <a-col :span="24">
                  <HistoryAlarm ref="HistoryAlarm1" :unitId="unitId" height="70vh" overflow="auto"></HistoryAlarm>
              </a-col>
            </a-tab-pane>
            <a-tab-pane key="5" tab="平面图" v-if="unitType!=='2'">
              <a-col :span="24">
                  <Plan ref="Plan1" :unitId="unitId" :unitName="unitName" height="70vh" overflow="auto"></Plan>
              </a-col>
            </a-tab-pane>
            <a-tab-pane key="6" tab="微型消防站" v-if="unitType!=='2'">
              <a-col :span="24">
                  <MicroFireStation ref="MicroFireStation1" :unitId="unitId" height="70vh" overflow="auto"></MicroFireStation>
              </a-col>
            </a-tab-pane>
            <a-tab-pane key="7" tab="车辆管理" v-if="unitType!=='2'">
              <a-col :span="24">
                  <VehicleManagement ref="VehicleManagement1" :unitId="unitId" height="70vh" overflow="auto"></VehicleManagement>
              </a-col>
            </a-tab-pane>
            <a-tab-pane key="8" tab="人员信息" v-if="unitType!=='2'">
              <a-col :span="24">
                  <PersonnelInformation ref="PersonnelInformation1" :unitId="unitId"
                                        :departId="departId" :orgCode="orgCode" height="70vh" overflow="auto"></PersonnelInformation>
              </a-col>
            </a-tab-pane>
            <a-tab-pane key="9" tab="监控视频" v-if="unitType!=='2'">
              <a-col :span="24">
                <a-card>
                  <SurveillanceVideo ref="SurveillanceVideo1" :unitId="unitId" height="70vh" overflow="auto"></SurveillanceVideo>
                </a-card>
              </a-col>
            </a-tab-pane>
            <a-tab-pane key="10" tab="预案管理" v-if="unitType!=='2'">
              <a-col :span="24">
                  <PlanManagement ref="PlanManagement1" :unitId="unitId" :departId="departId"
                                  :orgCode="orgCode" :unitName="unitName" height="70vh" overflow="auto"></PlanManagement>
              </a-col>
            </a-tab-pane>
            <a-tab-pane key="11" tab="物资管理" v-if="unitType =='2'">
              <a-col :span="24">
                  <MaterialManagement ref="MaterialManagement1" :unitId="unitId" height="70vh" overflow="auto"></MaterialManagement>
              </a-col>
            </a-tab-pane>
          </a-tabs>
        </a-col>
      </a-row>
    </a-form>
  </a-card>
</template>
<script lang="ts" setup>
  import {useUserStore} from '/@/store/modules/user';
  import {onMounted, ref, defineProps, nextTick, defineExpose, watch} from 'vue'
  import {defHttp} from '/@/utils/http/axios';
  import {getRegionName} from '/@/hooks/region/getRegion'
  import BasicInformation from './components/BasicInformation.vue'
  import NetworkingSystem from './components/NetworkingSystem.vue'
  import SafetyRating from './components/SafetyRating.vue'
  import HistoryAlarm from './components/HistoryAlarm.vue'
  import Plan from './components/Plan.vue'
  import MicroFireStation from './components/MicroFireStation.vue'
  import VehicleManagement from './components/VehicleManagement.vue'
  import PersonnelInformation from './components/PersonnelInformation.vue'
  import SurveillanceVideo from './components/SurveillanceVideo.vue'
  import PlanManagement from '../../report/FirePatrolInspection/planManagement/index.vue'
  import MaterialManagement from './components/MaterialManagement.vue'
  import {getToken, getLoginBackInfo, getAuthCache} from '/@/utils/auth';
  import {ROLES_KEY, TOKEN_KEY, USER_INFO_KEY, LOGIN_INFO_KEY, DB_DICT_DATA_KEY, TENANT_ID} from '/@/enums/cacheEnum';

  const props = defineProps(["unitId", "departId", "orgCode", "unitName", "unitType"])
  const confirmLoading = ref<boolean>(false);
  const formDisabled = ref<boolean>(false);
  const activeKeys = ref('1');
  const userStore = useUserStore();
  const unitId = ref(null);
  const departId = ref(null);
  const orgCode = ref(null);
  const unitName = ref(null);
  const unitType = ref(null);

  unitId.value = props.unitId ? props.unitId : userStore.getLoginInfo.unitInfo.id ? userStore.getLoginInfo.unitInfo.id : undefined;
  departId.value = props.departId ? props.departId : userStore.getLoginInfo.unitInfo.departId ? userStore.getLoginInfo.unitInfo.departId : undefined;
  orgCode.value = props.orgCode ? props.orgCode : userStore.getLoginInfo.unitInfo.orgCode ? userStore.getLoginInfo.unitInfo.orgCode : undefined;
  unitName.value = props.unitName ? props.unitName : userStore.getLoginInfo.unitInfo.unitName ? userStore.getLoginInfo.unitInfo.unitName : undefined;
  unitType.value = props.unitType ? props.unitType : userStore.getLoginInfo.unitInfo.unitType ? userStore.getLoginInfo.unitInfo.unitType : undefined;

  // onMounted(() => {
  //     activeKeys.value = "1";
  //     unitId.value = userStore.getLoginInfo.unitInfo.id;
  //     departId.value = userStore.getLoginInfo.unitInfo.departId;
  //     orgCode.value = userStore.getLoginInfo.unitInfo.orgCode;
  //     unitName.value = userStore.getLoginInfo.unitInfo.unitName;
  //     unitType.value = userStore.getLoginInfo.unitInfo.unitType;
  // });
  function handleTabClick(key) {
    activeKeys.value = key;
  }
</script>
<style lang="less" scoped>
  .ant-descriptions-title::before {
    content: '';
    display: inline-block;
    width: 6px; /* 宽度 */
    height: 12px; /* 高度 */
    background-color: #52A8F6; /* 背景颜色 */
    margin-right: 8px; /* 与文字的间距 */
    border-radius: 2px; /* 倒角大小 */
  }
  /deep/ .ant-card-body {
    /*padding: 24px;*/
    padding-top: 0;
    /*padding-bottom: 0;*/
  }
  .jeecg-modal-content > .scroll-container {
    padding-top: 0;
  }
</style>
