import { defHttp } from '/@/utils/http/axios';
const Api = {
    getUserList: '/sys/user/listAll', // 用户列表
    getSpecialPlanUnitModelList: '/sys/dtSpecialPlan/getSpecialPlanUnitModelList', // 接收单位
    getmatUnitIdLists: '/sys/dtMaintenanceContract/getPartBMaintenanceList', // 维保单位
    getmatUnitIdList: '/sys/dtUnit/pageList', // 维保单位
    getUnitIdList: '/sys/dtUnit/pageList', // 维保单位
    getDeviceCode: '/sys/dtDevice/pageList', // 获取设备编码
    getOrderCode: '/sys/dtOrderExecute/pageList', // 获取设备编码
    getAlarmLevel: '/sys/dict/getDictItemList', // 数据字典查询
    getBuildingList: '/sys/dtBuilding/getBuildingList', // 建筑物列表
    getFloor: '/sys/dtBuildingFloor/getDtBuildingFloorModelList', // 获取楼层
    getDeviceCategoryList: '/sys/dtDeviceCategory/getDeviceCategoryList', // 设备分类
    getDeviceTypeList: '/sys/dtDeviceCategory/getDeviceTypeList', // 设备类型
    getParamCode1: '/sys/dtParamTemplate/getParamListByDeviceCode', // 根据设备编码获取参数编码
    getParamCode2: '/sys/dtParamTemplate/getParamListByType', // 根据设备类型获取参数编码
    getInspectGroup: '/sys/dtInspectGroup/pageList', // 巡检组
    getBuildingByVideo: '/sys/dtBuilding/pageList', // 建筑物列表(视频)
    getVideoTypeList: '/sys/dtVideoType/pageList', // 建筑物列表(视频)
}
// 参数编码
function getParamCodeList1(params) {
    const getParamCodeList = defHttp.get({ url: Api.getParamCode1, params }).then((e) => {
        e.forEach(i => {
            i.label = i.paramName
            i.value = i.paramCode
        });
        return e
    });
    return getParamCodeList
}
function getParamCodeList2(params) {
    const getParamCodeList = defHttp.get({ url: Api.getParamCode2, params }).then((e) => {
        e.forEach(i => {
            i.label = i.paramCode
            i.value = i.id
        });
        return e
    });
    return getParamCodeList
}


// 用户列表
const userList = defHttp.get({ url: Api.getUserList, params: {} }).then((e) => {
    if (e != undefined) {
        e.records.forEach(i => {
            i.label = i.username
            i.value = i.id
        });
        return e.records
    }
});
const userList1 = defHttp.get({ url: Api.getUserList, params: {} }).then((e) => {
    if (e != undefined) {
        e.records.forEach(i => {
            i.label = i.realname
            i.value = i.id
        });
        return e.records
    }
});
// 用户列表
const userLists = defHttp.get({ url: Api.getUserList, params: {} }).then((e) => {
    if (e != undefined) {
        e.records.forEach(i => {
            i.label = i.realname
            i.value = i.username
        });
        return e.records
    }
});
// 巡检组
const inspectGroupList = defHttp.get({ url: Api.getInspectGroup, params: {} }).then((e) => {
    e.records.forEach(i => {
        i.label = i.groupName
        i.value = i.id
    });
    return e.records
});
// 获取维保单位
function getmatUnitIdList(params, sNum) {
    const deviceCodeList = defHttp.get({ url: Api.getmatUnitIdList, params }).then((e) => {
        let arr = []
        e.records.forEach(i => {
            if (i.unitType == sNum) {
                i.label = i.unitName
                i.value = i.id
                arr.push(i)
            }
        });
        return arr
    });
    return deviceCodeList
}
// 获取维保单位
function getmatUnitIdLists(params) {
    const deviceCodeList = defHttp.get({ url: Api.getmatUnitIdLists, params }).then((e) => {
        let arr = []
        e.forEach(i => {
            i.label = i.partybUnitName
            i.value = i.partybUnitId
            arr.push(i)
        });
        return arr
    });
    return deviceCodeList
}
// 获取所有单位
function getUnitIdList(params) {
    const deviceCodeList = defHttp.get({ url: Api.getUnitIdList, params }).then((e) => {
        let arr = []
        e.records.forEach(i => {
            if (i.unitType != '3') {
                i.label = i.unitName
                i.value = i.id
                arr.push(i)
            }
        });
        return arr
    });
    return deviceCodeList
}
// 获取接收单位
function getSpecialPlanUnitModelList(id) {
    const deviceCodeList = defHttp.get({ url: Api.getSpecialPlanUnitModelList, params: { planId: id } }).then((e) => {
        console.log(e);

        let arr = []
        e.forEach(i => {
            i.label = i.unitName
            i.value = i.id
            arr.push(i)
        });
        return arr
    });
    return deviceCodeList
}
// 获取设备编码
function getDeviceCode(params) {
    const deviceCodeList = defHttp.get({ url: Api.getDeviceCode, params }).then((e) => {
        e.records.forEach(i => {
            i.label = i.deviceCode
            i.value = i.id
        });
        return e.records
    });
    return deviceCodeList
}
function getDeviceCodeByCode(params) {
    const deviceCodeList = defHttp.get({ url: Api.getDeviceCode, params }).then((e) => {
        e.records.forEach(i => {
            i.label = i.deviceCode
            i.value = i.deviceCode
        });
        return e.records
    });
    return deviceCodeList
}
// 获取工单编码
function getOrderCode(params) {
    const deviceCodeList = defHttp.get({ url: Api.getOrderCode, params }).then((e) => {

        e.records.forEach(i => {
            i.label = i.orderCode
            i.value = i.id
        });
        return e.records
    });
    return deviceCodeList
}
// 数据字典查询
function getDictItemList(id: string) {
    const deviceTypeList = defHttp.get({ url: Api.getAlarmLevel, params: { dictCode: id } }).then((e) => {
        return e
    });
    return deviceTypeList
}
// 设备分类
const deviceCategoryList = defHttp.get({ url: Api.getDeviceCategoryList }).then((e) => {
    e.forEach(i => {
        i.label = i.deviceCategory
        i.value = i.id
    });
    return e
});
// 设备类型
function getDeviceTypeListById(id: string) {
    const deviceTypeList = defHttp.get({ url: Api.getDeviceTypeList, params: { parentId: id } }).then((e) => {
        if (e.length != 0) {
            e.forEach(i => {
                i.label = i.deviceType
                i.value = i.id
            });
            return e
        }
    });
    return deviceTypeList
}
// 建筑物列表
const buildingList = defHttp.get({ url: Api.getBuildingList }).then((e) => {
    e.forEach(i => {
        i.label = i.buildingName
        i.value = i.id
        i.title = i.buildingName
        i.key = i.id
    });
    return e
});
// 建筑物列表(条件)
function getBuildingByVideo(e) {
    let params = e
    const buildingLists = defHttp.get({ url: Api.getBuildingList, params }).then((e) => {
        e.forEach(i => {
            i.label = i.buildingName
            i.value = i.id
        });
        return e
    });
    return buildingLists
}

// 视频类型
function getVideoTypes(e) {
    let params = e
    const getVideoTypeList = defHttp.get({ url: Api.getVideoTypeList, params }).then((e) => {
        e.records.forEach(i => {
            i.label = i.typeName
            i.value = i.id
        });
        return e.records
    });
    return getVideoTypeList
}

// 建筑物楼层
function floorList(id: string) {
    const floorList = defHttp.get({ url: Api.getFloor, params: { buildingId: id } }).then((e) => {
        e.forEach(i => {
            i.label = i.floorName
            i.value = i.id
        });
        return e
    });
    return floorList
}


export {
    deviceCategoryList,
    getDeviceTypeListById,
    buildingList,
    floorList,
    getDictItemList,
    getDeviceCode,
    userList,
    getParamCodeList1,
    getParamCodeList2,
    getmatUnitIdList,
    getOrderCode,
    getUnitIdList,
    getSpecialPlanUnitModelList,
    getmatUnitIdLists,
    inspectGroupList,
    userLists,
    userList1,
    getBuildingByVideo,
    getVideoTypes,
    getDeviceCodeByCode
}
