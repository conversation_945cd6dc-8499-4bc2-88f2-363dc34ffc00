<template>
  <BasicModal
    :title="title"
    :width="width"
    :visible="visible"
    :height="600"
    @ok="handleOk"
    :okText="'确定'"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    :cancelText="cancelText">
    <ResponseRecordForm ref="realForm" @ok="submitCallback" :disabled="disableSubmit"/>
    <template #footer>
      <a-button @click="handleCancel">关闭</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
  import {ref, nextTick, defineExpose, defineEmits} from 'vue';
  import {BasicModal} from '/@/components/Modal';
  import {useParamStore} from '/@/store/modules/getParam';
  import ResponseRecordForm from './ResponseRecordForm.vue';

  const realForm = ref();

  const paramStore = useParamStore();
  const title = ref<string>('火灾响应');
  const cancelText = ref<string>('关闭');
  const width = ref<number>(800);
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const emit = defineEmits(['register', 'ok']);
  function add() {
    visible.value = true;
    nextTick(() => {
      realForm.value.add();
    });
  }

  function edit(record) {
    visible.value = true;
    nextTick(() => {
      realForm.value.edit(record);
    });
  }

  function handleOk() {
    realForm.value.submitForm();
  }

  function submitCallback() {
    handleCancel();
    emit('ok');
  }

  function handleCancel() {
    visible.value = false;
  }

  defineExpose({
    add,
    edit,
    disableSubmit,
  });
</script>

<style scoped>

</style>
