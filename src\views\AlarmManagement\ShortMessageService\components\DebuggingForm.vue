<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="24">
            <a-form-item label="手机号码" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="phone"
                         v-bind="validateInfos.phone">
              <a-input placeholder="请输入手机号码" v-model:value="formState.phone"/>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="message">
            <a-form-item label="测试结果" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="phone"
                         v-bind="validateInfos.phone">
              {{message}}
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import {Form} from 'ant-design-vue';
import {
  defineComponent,
  ref,
  reactive,
  onMounted,
  defineProps,
  defineExpose,
  UnwrapRef,
  nextTick,
  defineEmits
} from 'vue';
import {useMessage} from '/@/hooks/web/useMessage';
import {defHttp} from '/@/utils/http/axios';
import {useUserStore} from '/@/store/modules/user';
import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';

const userStore = useUserStore();
const props = defineProps(["disabled"])
const emits = defineEmits(['success', 'register', 'ok'])

const useForm = Form.useForm;
const confirmLoading = ref<boolean>(false);
const labelCol = ref<any>({xs: {span: 24}, sm: {span: 6}});
const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 18}});
const labelCol1 = ref<any>({xs: {span: 24}, sm: {span: 4}});
const wrapperCol1 = ref<any>({xs: {span: 24}, sm: {span: 20}});
const message = ref();
const {createMessage} = useMessage();
const Api = reactive({
  edit: '/business/dtMessageConfig/editMessageConfig',
});
const formState = reactive({
  id: '',
  phone: '',
});

//表单验证
const validatorRules = {
  phone: [
    {required: true, message: '请输入电话号码!'},
    { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式有误' }
  ],
};
const {
  resetFields,
  validate,
  validateInfos
} = useForm(formState, validatorRules, {immediate: false});

function add() {
  edit({});
}

function edit(record) {
  nextTick(() => {
    resetFields();
    const tmpData = {};
    Object.keys(formState).forEach((key) => {
      if (record.hasOwnProperty(key)) {
        tmpData[key] = record[key]
      }
    })
    Object.assign(formState, tmpData);
  });
}

/**
 * 提交数据
 */
async function submitForm() {
  await validate();
  confirmLoading.value = true;
  let httpurl = '';
  let method = '';
  //时间格式化
  let model = {
    id: formState.id,
    phone: formState.phone,
  };
  httpurl += Api.edit;
  method = 'put';
  defHttp.request({
    url: httpurl,
    params: model,
    method: method,
  }, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      message.value ="发送成功"
      createMessage.success(res.message);
      emits('ok');
    } else {
      message.value = "发送失败"
      createMessage.warning(res.message);
    }
  }).finally(() => {
    confirmLoading.value = false;
  });
}

defineExpose({
  add,
  edit,
  submitForm
});
</script>

<style scoped>
.antd-modal-form {
  padding: 24px 24px 24px 24px;
}
</style>
