<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
  <style>
    html,
    body,
    #container {
      width: 100%;
      height: 100%;
    }
  </style>
  <title>规划结果 + 驾车路线绘制</title>
  <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css" />
  <script type="text/javascript">
    window._AMapSecurityConfig = {
      securityJsCode: "df979d3f62e58e47f0988cd012b71417",
    };
  </script>
  <script src="https://webapi.amap.com/maps?v=2.0&key=f321b651170b76b298d05d0914d8fe99&plugin=AMap.Driving"></script>
  <script src="https://a.amap.com/jsapi_demos/static/demo-center/js/demoutils.js"></script>
</head>
<body>
<div id="container"></div>
<div id="panel"></div>
<script type="text/javascript">
  var map = new AMap.Map("container", {
    center: [116.397559, 39.89621],
    zoom: 14
  });


  // 构造路线导航类
  var driving = new AMap.Driving({
    panel: 'panel',
    policy: AMap.DrivingPolicy.LEAST_TIME // 最短时间策略
  })

  // 根据起终点经纬度规划驾车导航路线
  driving.search(new AMap.LngLat(116.379028, 39.865042), new AMap.LngLat(116.427281, 39.903719), function(status, result) {
    // result即是对应的驾车导航信息，相关数据结构文档请参考 https://lbs.amap.com/api/javascript-api/reference/route-search#m_DrivingResult
    if (status === 'complete') {
      if (result.routes && result.routes.length) {
        // 绘制第一条路线，也可以按需求绘制其它几条路线
        var path = parseRouteToPath(result.routes[0])
        var routeLine = new AMap.Polyline({
          path: path,
          isOutline: true,
          outlineColor: '#ffeeee',
          borderWeight: 2,
          strokeWeight: 5,
          strokeOpacity: 0.9,
          strokeColor: '#0091ff',
          lineJoin: 'round'
        })
        map.add(routeLine);
      }
    } else {
    }
  });

  // 解析DrivingRoute对象，构造成AMap.Polyline的path参数需要的格式
  // DrivingResult对象结构参考文档 https://lbs.amap.com/api/javascript-api/reference/route-search#m_DriveRoute
  function parseRouteToPath(route) {
    var path = []
    for (var i = 0, l = route.steps.length; i < l; i++) {
      var step = route.steps[i]
      for (var j = 0, n = step.path.length; j < n; j++) {
        path.push(step.path[j])
      }
    }
    return path
  }
</script>
</body>
</html>
