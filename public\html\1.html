<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <style>
        .video {
            width: 100%;
            height: 80%;

        }

        .home {
            width: 100%;
            height: 100%;
        }

        #video-container {
            width: 100%;
            height: 60%;
        }

        .video-item {
            display: flex;
            padding: 1%;
        }

        .item {
            flex: 1;
            height: 40vh;
            margin: 0 1%;

        }
    </style>

</head>
<body>
<div class="video">
    <div class="video-item">
        <div class="item">
            <div class="home" ref="viewtools">
                <div id="video-container"></div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="./ezuikit.js"></script>
<script>
    var themeData = {
        "header": {
            "color": "#1890ff",
            "activeColor": "#FFFFFF",
            "backgroundColor": "#000000",
            "btnList": [
                // {
                //     "iconId": "deviceID",
                //     "part": "left",
                //     "defaultActive": 0,
                //     "memo": "顶部设备序列号",
                //     "isrender": 1
                // },
                // {
                //     "iconId": "deviceName",
                //     "part": "left",
                //     "defaultActive": 0,
                //     "memo": "顶部设备名称",
                //     "isrender": 1
                // },
                // {
                //     "iconId": "cloudRec",
                //     "part": "right",
                //     "defaultActive": 0,
                //     "memo": "云存储",
                //     "isrender": 0
                // },
                // {
                //     "iconId": "rec",
                //     "part": "right",
                //     "defaultActive": 0,
                //     "memo": "SD卡回放",
                //     "isrender": 0
                // }
            ]
        },
        "footer": {
            "color": "#FFFFFF",
            "activeColor": "#1890FF",
            "backgroundColor": "#00000021",
            "btnList": [
                // {
                //     "iconId": "play",
                //     "part": "left",
                //     "defaultActive": 1,
                //     "memo": "播放",
                //     "isrender": 1
                // },
                // {
                //     "iconId": "capturePicture",
                //     "part": "left",
                //     "defaultActive": 0,
                //     "memo": "截屏按钮",
                //     "isrender": 1
                // },
                // {
                //     "iconId": "sound",
                //     "part": "left",
                //     "defaultActive": 0,
                //     "memo": "声音按钮",
                //     "isrender": 1
                // },
                // {
                //     "iconId": "pantile",
                //     "part": "left",
                //     "defaultActive": 0,
                //     "memo": "云台控制按钮",
                //     "isrender": 1
                // },
                // {
                //     "iconId": "recordvideo",
                //     "part": "left",
                //     "defaultActive": 0,
                //     "memo": "录制按钮",
                //     "isrender": 1
                // },
                // {
                //     "iconId": "talk",
                //     "part": "left",
                //     "defaultActive": 0,
                //     "memo": "对讲按钮",
                //     "isrender": 1
                // },
                // {
                //     "iconId": "zoom",
                //     "part": "left",
                //     "defaultActive": 0,
                //     "memo": "电子放大",
                //     "isrender": 1
                // },
                // {
                //     "iconId": "hd",
                //     "part": "right",
                //     "defaultActive": 0,
                //     "memo": "清晰度切换按钮",
                //     "isrender": 1
                // },
                {
                    "iconId": "webExpend",
                    "part": "right",
                    "defaultActive": 0,
                    "memo": "网页全屏按钮",
                    "isrender": 1
                },
                // {
                //     "iconId": "expend",
                //     "part": "right",
                //     "defaultActive": 0,
                //     "memo": "全局全屏按钮",
                //     "isrender": 1
                // }
            ]
        }
    };

    var player = new EZUIKit.EZUIKitPlayer({
        id:'video-container',
        autoplay: true,
        url: "ezopen://open.ys7.com/*********/1.hd.live", // 播放地址
        accessToken: "at.b83kx05rbdzbveowdejmbdb13x1jzil6-7i0cqx4cx8-11xwnvg-qxpg72dcn",
        width: 780,
        height: 360,
        //splitBasis:1, //旧版本3.5有个自带的分屏功能，现在没有了
        audio:0,
        themeData: themeData, //上面的对象内容
        handleError:(data) => {
            console.log(data)
        },
        handleSuccess: () => {
            setTimeout(() => {
                this.player && this.player.stop()
            }, 30000)
            // console.log("播放成功回调函数，此处可执行播放成功后续动作")
        },
    })


</script>
</body>
</html>
