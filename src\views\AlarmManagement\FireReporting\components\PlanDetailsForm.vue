<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="12">
            <a-form-item label="预案名称" name="unitType" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-col>{{data.schemeName}}</a-col>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="单位名称" name="unitType" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-col>{{data.unitName}}</a-col>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="预案文件" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
              <a :href="data.schemeFile">{{data.schemeFileName}}</a>
            </a-form-item>
          </a-col>
          <Divider style="margin: 0 0 15px ">指挥</Divider>
          <a-col :span="12">
            <a-form-item label="指挥组长" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-col>{{data.groupLeader}}</a-col>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="指挥副组长" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-col>{{data.deputyGroupLeader}}</a-col>
            </a-form-item>
          </a-col>
          <Divider style="margin: 0 0 15px ">救援</Divider>
          <a-col :span="12">
            <a-form-item label="现场核实组" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-col>{{data.verifyGroup}}</a-col>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="灭火行动组" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-col>{{data.actionGroup}}</a-col>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="物资抢救组" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-col>{{data.rescueGroup}}</a-col>
            </a-form-item>
          </a-col>
          <Divider style="margin: 0 0 15px ">疏散</Divider>
          <a-col :span="12">
            <a-form-item label="疏散引导组" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-col>{{data.guideGroup}}</a-col>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="现场警戒组" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-col>{{data.alarmGroup}}</a-col>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import {Form,Divider} from 'ant-design-vue';
  import {ref, nextTick, defineEmits, reactive, defineExpose} from 'vue';
  import {getDictItemList} from '@/hooks/params/param'
  import MonthSelect from '/@/components/Form/src/jeecg/components/serchselect/MonthSelect.vue';
  import {defHttp} from '/@/utils/http/axios';


  const confirmLoading = ref<boolean>(false);
  const formState = reactive({});
  const activeKey = ref('1')
  const emits = defineEmits(['success', 'register', 'ok'])
  const labelCol = reactive({
    xs: {span: 24},
    sm: {span: 8},
  })
  const wrapperCol = reactive({
    xs: {span: 24},
    sm: {span: 16},
  })
  const labelCol1 = reactive({
    xs: {span: 24},
    sm: {span: 4},
  })
  const wrapperCol1 = reactive({
    xs: {span: 24},
    sm: {span: 20},
  })
  const validatorRules = {};
  const useForm = Form.useForm;
  const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});
  const data = ref<any>({});
  const Api = reactive<any>({
    list: '/sys/dtPreScheme/getPreScheme',
  });

  function add() {
    edit({});
  }

  function edit(record) {
    nextTick(() => {
      resetFields();
      Object.assign(formState, record);
      loadData1()
    });

  }

  function loadData1(arg) {
    let params = {
      id: formState.preSchemeId,
    }
    defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
      if (res.success) {
        if (res.result.schemeFile.lastIndexOf('\\') >= 0) {
          let reg = new RegExp('\\\\', 'g');
          res.result.schemeFile = res.result.schemeFile.replace(reg, '/');
        }
        res.result.schemeFileName = res.result.schemeFile.substring(res.result.schemeFile.lastIndexOf('/') + 1);
        res.result.schemeFile = window._CONFIG['imagesUrl'] + res.result.schemeFile
        Object.assign(data.value, res.result);
      }
    }).finally(() => {
    });
  }

  function submitForm() {

  }

  defineExpose({
    add,
    edit,
    submitForm,
    activeKey
  });

</script>

<style lang="less" scoped>
  .chooseUnit {
    color: rgb(0, 153, 255);
    cursor: pointer;
  }

  .antd-modal-form {
    padding: 24px 24px 24px 24px;
  }

  .uploadBoxs {
    width: 50%;
    height: 150px;
    cursor: pointer;
    border: 1px dashed #1296db;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 10px;

    img {
      width: 30px;
      height: 30px;
    }
  }
</style>
