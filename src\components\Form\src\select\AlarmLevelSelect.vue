<template>
  <a-select
    :placeholder="placeholder"
    allowClear
    v-model:value="arrayValue"
    :filterOption="filterOption"
    :getPopupContainer="getPopupContainer"
    :style="style"
    :mode="mode"
    @change="onChange"
  >
    <a-select-option :value="null">请选择…</a-select-option>
    <a-select-option value="1">一级告警</a-select-option>
    <a-select-option value="2">二级告警</a-select-option>
    <a-select-option value="3">三级告警</a-select-option>
    <a-select-option value="4">四级告警</a-select-option>
  </a-select>
</template>
<script lang="ts">
  import {
    defineComponent,
    PropType,
    ref,
    reactive,
    watchEffect,
    computed,
    unref,
    watch,
    onMounted,
    nextTick
  } from 'vue';
  import {propTypes} from '/src/utils/propTypes';
  import {useAttrs} from '/src/hooks/core/useAttrs';
  import {defHttp} from '/@/utils/http/axios';
  import {loadDictItem, loadTreeData} from '/src/api/common/api';
  import {get, omit} from 'lodash-es';
  import {useRuleFormItem} from '/src/hooks/component/useFormItem';
  import {CompTypeEnum} from '/src/enums/CompTypeEnum';
  import {LoadingOutlined} from '@ant-design/icons-vue';

  export default defineComponent({
    name: 'AlarmLevelSelect',
    inheritAttrs: false,
    components: {LoadingOutlined},
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.number, propTypes.array]),
      pcode: propTypes.string,
      type: propTypes.string,
      placeholder: propTypes.string,
      mode: propTypes.string,
      mode: {
        type: propTypes.string,
        default: "default",
      },
    },
    emits: ['options-change', 'change', 'update:value'],
    setup(props, {emit, refs}) {
      const arrayValue = ref<any[]>(!props.value ? [] : props.value.split(props.spliter));
      //update-begin-author:taoyan date:20220404 for: 使用useRuleFormItem定义的value，会有一个问题，如果不是操作设置的值而是代码设置的控件值而不能触发change事件
      // 此处添加空值的change事件,即当组件调用地代码设置value为''也能触发change事件
      watch(() => props.value, (val) => {
          if (!val) {
            arrayValue.value = [];
          } else {
            arrayValue.value = props.value.toString();
          }
        }
      );
      function onChange(e) {
        arrayValue.value = e;
        emit('change', e);
        emit('update:value', e)
      }

      /** 用于搜索下拉框中的内容 */
      function filterOption(input, option) {
        // update-begin--author:liaozhiyang---date:20230914---for：【QQYUN-6514】 配置的时候，Y轴不能输入多个字段了，控制台报错
        if (typeof option.children === 'function') {
          // 在 label 中搜索
          let labelIf = option.children()[0]?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          if (labelIf) {
            return true;
          }
        }
        // update-end--author:liaozhiyang---date:20230914---for：【QQYUN-6514】 配置的时候，Y轴不能输入多个字段了，控制台报错
        // 在 value 中搜索
        return (option.value || '').toString().toLowerCase().indexOf(input.toLowerCase()) >= 0;
      }

      return {
        CompTypeEnum,
        onChange,
        arrayValue,
        filterOption,
      };
    },
  });
</script>
