<template>
    <a-spin :spinning="confirmLoading">
        <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
            <JFormContainer :disabled="disabled">
                <a-row>
                    <a-col :span="24">
                        <JVxeTable
                                ref="tableRef"
                                bordered
                                row-number
                                row-selection
                                row-selection-type="radio"
                                click-select-row
                                highlight-current-row
                                keep-source
                                resizable
                                :maxHeight="484"
                                :loading="loading"
                                :dataSource="dataSource"
                                :columns="columns"
                                :pagination="pagination"
                                @pageChange="handlePageChange"
                                @selectRowChange="handleSelectRowChange">
                            <template #action="props">
                                <a @click="handleDetail(props.row)">查看</a>
                            </template>
                        </JVxeTable>
                    </a-col>
                </a-row>
            </JFormContainer>
        </a-form>
    </a-spin>
    <PlanDetailsModal ref="PlanDetailsModal1"></PlanDetailsModal>
</template>

<script lang="ts" setup>
    import {Form, Descriptions, Image} from 'ant-design-vue';
    import * as echarts from "echarts";
    import {
        defineComponent,
        ref,
        reactive,
        onMounted,
        defineProps,
        defineExpose,
        nextTick,
        defineEmits
    } from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {useMessage} from '/@/hooks/web/useMessage';
    import PlanDetailsModal from './PlanDetailsModal.vue';

    const props = defineProps(["disabled"])
    const emit = defineEmits(['success', 'register', 'ok'])
    const confirmLoading = ref<boolean>(false);
    const validatorRules = {};
    const Api = reactive<any>({
        list: '/sys/dtPreScheme/pageList',
        edit: '/sys/dtFireReport/startPlan',
    });
    const formState = reactive({});
    const dataSource = ref<any>([]);
    const columns = ref([
        {
            title: '单位名称',
            key: 'unitName',
            minWidth: 150,
            align: "center",
        },
        {
            title: '预案名称',
            key: 'schemeName',
            minWidth: 200,
            align: "center",
        },
        {
            title: '操作',
            type: "slot",
            key: 'action',
            align: 'center',
            fixed: 'right',
            width: 150,
            slotName: 'action',
        },
    ]);
    const PlanDetailsModal1 = ref();
    const {createMessage} = useMessage();
    const pagination = ref<any>({
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条';
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
    });
    const useForm = Form.useForm;
    const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

    onMounted(() => {
        //初始化字典选项
    });
    //查看
    function handleDetail(e) {
        e.preSchemeId=e.id
        PlanDetailsModal1.value.disableSubmit = false;
        PlanDetailsModal1.value.edit(e);
    }
    function add() {
        edit({});
    }

    function edit(record) {
        nextTick(() => {
            resetFields();
            Object.assign(formState, record);
            loadData(record)
        });
    }

    // 当分页参数变化时触发的事件
    function handlePageChange(event) {
        // 重新赋值
        pagination.value.current = event.current;
        pagination.value.pageSize = event.pageSize;

        // 查询数据
        loadData();
    }

    function loadData(record) {
        let params = {
            unitId: record.unitId,
            pageNo: pagination.value.current,
            pageSize: pagination.value.pageSize
        };
        defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                dataSource.value = res.result.records;
                if (res.result && res.result.total) {
                    pagination.value.total = res.result.total;
                } else {
                    pagination.value.total = 0;
                }
            }
        }).finally(() => {
        });
    }

    // 选择的行
    const selectedRows = ref<Recordable[]>([]);

    // 选中
    function handleSelectRowChange(event) {
        selectedRows.value = event.selectedRows;
    }

    /**
     * 提交数据
     */
    async function submitForm() {
        confirmLoading.value = true;
        let model = {
            id:formState.id,
            preSchemeId:selectedRows.value[0].id,
        };
        defHttp.post({url: Api.edit, params:model}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                createMessage.success(res.message);
                emit('ok');
            } else {
                createMessage.warning(res.message);
            }
        }) .finally(() => {
            confirmLoading.value = false;
        });
    }

    defineExpose({
        add,
        edit,
        submitForm,
    });
</script>

<style lang="less" scoped>
    /deep/ .ant-descriptions-header {
        margin-bottom: 4px;
    }

    .charts {
        width: 100%;
        height: 200px;
        border: 1px solid #f0f0f0;
    }

    .video {
        width: 200px;
        height: 100px;
        float: left;
        margin-right: 5px;
        position: relative;
        background: #000;
    }

    .video .videos {
        width: 100%;
        max-height: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
</style>
