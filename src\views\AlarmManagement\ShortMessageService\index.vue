<!-- 逾期推送管理 OverduePushManagement -->
<template>
  <a-card :bordered="false">
    <a-descriptions size="small" :column="2" title="短信服务">
    </a-descriptions>
    <div class="table-operator">
      <a-button type="primary" @click="handleManage">
        管理
      </a-button>
    </div>
    <a-descriptions size="small" :column="2" title="">
      <a-descriptions-item label="短信平台"> {{ data?data.messagePlatform:'' }}</a-descriptions-item>
      <a-descriptions-item label="AccessKey ID">{{ data?data.messageKey:'' }}</a-descriptions-item>
      <a-descriptions-item label="AccessKey Secret"> {{ data?data.messageSecret:'' }}</a-descriptions-item>
      <a-descriptions-item label="Sign"> {{ data?data.messageSign:'' }}</a-descriptions-item>
      <a-descriptions-item label="短信服务开关"> {{ data&&data.serverSwitch=='1'?'开启':'关闭' }}</a-descriptions-item>
    </a-descriptions>
    <a-descriptions size="small" title="短信模板">
    </a-descriptions>
    <div class="table-operator">
      <a-button type="primary" @click="handleAdd">
        新增
      </a-button>
    </div>
    <!-- 操作按钮区域 -->

    <!-- table区域-begin -->
    <JVxeTable
      ref="tableRef"
      bordered
      row-number
      keep-source
      resizable
      :maxHeight="484"
      :loading="loading"
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      style="margin-top: 8px"
      @pageChange="handlePageChange">
      <template #action="props">
        <a @click="handleEdit(props.row)">编辑</a>
        <a-divider type="vertical"/>
        <Popconfirm title="是否确认删除？" @confirm="handleDelete(props.row)">
          <a>删除</a>
        </Popconfirm>
        <a-divider type="vertical"/>
        <a @click="handleDebugging(props.row)">调试</a>
      </template>
    </JVxeTable>
  </a-card>
  <MessageModal ref="MessageModal1" @ok="loadData1"/>
  <MessageTemplateModal ref="MessageTemplateModal1" @ok="loadData"/>
  <DebuggingModal ref="DebuggingModal1"/>
</template>

<script lang="ts" setup>
import {onMounted, ref, reactive} from 'vue';
import {defHttp} from '/@/utils/http/axios';
import {Descriptions, Popconfirm, Tabs} from 'ant-design-vue';
import {filterObj} from '/@/utils/common/compUtils';
import {useUserStore} from '/@/store/modules/user';
import MessageModal from './components/MessageModal.vue';
import MessageTemplateModal from './components/MessageTemplateModal.vue';
import DebuggingModal from './components/DebuggingModal.vue';
import {useMessage} from '/@/hooks/web/useMessage';
import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
import DateSelect from "@/components/Form/src/jeecg/components/serchselect/DateSelect.vue";
import JSelectMultiple from "../../../components/Form/src/jeecg/components/JSelectMultiple.vue";
import UnitIdSelect from '/@/components/Form/src/jeecg/components/serchselect/UnitIdSelect.vue';

const userStore = useUserStore();
const MessageModal1 = ref();
const MessageTemplateModal1 = ref();
const DebuggingModal1 = ref();
const labelCol = reactive({
  xs: {span: 24},
  sm: {span: 8},
})
const wrapperCol = reactive({
  xs: {span: 24},
  sm: {span: 16},
})
const activeKeyRef = ref('1');
const queryParam = ref<any>({});
const loading = ref<boolean>(false);
const columns = ref([
  {
    title: '模板名称',
    key: 'templateName',
    minWidth: 120,
    align: "center",
  },
  {
    title: '模板内容',
    key: 'templateContent',
    minWidth: 300,
    align: "center",
  },
  {
    title: '模板CODE',
    key: 'templateCode',
    minWidth: 120,
    align: "center",
  },
  {
    title: '操作',
    type: "slot",
    key: 'action',
    align: 'center',
    fixed: 'right',
    width: 200,
    slotName: 'action',
  },
]);
const Api = reactive<any>({
  list: '/business/dtMessageTemplate/pageList',
  delete: '/business/dtMessageTemplate/delMessageTemplate',
  getMessageConfig: '/business/dtMessageConfig/getMessageConfig',
});
const dataSource = ref<any>([{}]);
const pagination = ref<any>({
  current: 1,
  pageSize: 10,
  pageSizeOptions: ['10', '20', '30'],
  showTotal: (total, range) => {
    return range[0] + '-' + range[1] + ' 共' + total + '条';
  },
  showQuickJumper: true,
  showSizeChanger: true,
  total: 0,
});
const iSorter = ref<any>({column: 'createTime', order: 'desc'});
const iFilters = ref<any>({});
const data = ref();
const {createMessage} = useMessage();

function componentClick(key) {
  activeKeyRef.value = key;
}

//新增
function handleAdd() {
  MessageTemplateModal1.value.disableSubmit = false;
  MessageTemplateModal1.value.add();
}

function handleEdit(record) {
  MessageTemplateModal1.value.disableSubmit = false;
  MessageTemplateModal1.value.edit(record);
}

function handleDebugging(record) {
  DebuggingModal1.value.disableSubmit = false;
  DebuggingModal1.value.edit(record);
}

function handleManage() {
  MessageModal1.value.disableSubmit = false;
  MessageModal1.value.edit(data.value);
}

/**
 * 获取查询参数
 */
function getQueryParams() {
  let params = Object.assign(queryParam.value, iSorter.value, iFilters.value);
  params.pageNo = pagination.value.current;
  params.pageSize = pagination.value.pageSize;
  return filterObj(params);
}

// 当分页参数变化时触发的事件
function handlePageChange(event) {
  // 重新赋值
  pagination.value.current = event.current;
  pagination.value.pageSize = event.pageSize;
  // 查询数据
  loadData();
}

function loadData(arg) {
  if (arg === 1) {
    pagination.value.current = 1;
  }
  loading.value = true;
  let params = getQueryParams();
  defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      dataSource.value = res.result.records;
      if (res.result && res.result.total) {
        pagination.value.total = res.result.total;
      } else {
        pagination.value.total = 0;
      }
    }
  }).finally(() => {
    loading.value = false;
  });
}

function loadData1() {
  loading.value = true;
  defHttp.get({url: Api.getMessageConfig}, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      data.value = res.result;
    }
  }).finally(() => {
    loading.value = false;
  });
}

function handleDelete(e) {
  defHttp.delete({url: Api.delete, data: {id: e.id}}, {
    isTransformResponse: false,
    joinParamsToUrl: true
  }).then((res) => {
    if (res.success) {
      loadData();
      createMessage.success(res.message);
    } else {
      createMessage.warning(res.message);
    }
  });
}


onMounted(() => {
  //初始加载页面
  loadData();
  loadData1();
});

</script>

<style lang="less" scoped>
:deep(.ant-descriptions-header) {
  margin: 0;
  position: relative;

  .ant-descriptions-title {
    padding: 0 10px;

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      transform: translate(0, -50%);
      width: 5px;
      height: 20px;
      background: #1890ff;
      content: '';
    }

  }
}


</style>
