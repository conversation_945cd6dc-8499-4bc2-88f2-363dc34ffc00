<template>
    <a-spin :spinning="confirmLoading">
        <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
            <JFormContainer :disabled="disabled">
                <a-row>
                    <a-col :span="20">
                        <a-form-item label="响应总结" name="responseResult" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-col>{{formState.responseResult}}</a-col>
                        </a-form-item>
                    </a-col>
                    <a-col :span="4">
                        <a-button type="primary" preIcon="ant-design:search-outlined" @click="handleDetail" style="float:right;">预案详情
                        </a-button>
                    </a-col>
                    <a-col :span="24" style="padding-left: 4%">
                        <Timeline>
                            <Timeline.Item v-for="(item,index) in data" :key="index">
                                <p>{{item.responseBody}}{{item.responseContext}}</p>
                                <p>{{item.responseTime}}</p>
                            </Timeline.Item>
                        </Timeline>
                    </a-col>
                </a-row>
            </JFormContainer>
        </a-form>
    </a-spin>
    <PlanDetailsModal ref="PlanDetailsModal1"></PlanDetailsModal>
    <FireResponseListModal ref="FireResponseListModal1" @ok="loadData(formState)"></FireResponseListModal>
</template>

<script lang="ts" setup>
    import {Form, Descriptions, Image} from 'ant-design-vue';
    import * as echarts from "echarts";
    import {
        defineComponent,
        ref,
        reactive,
        onMounted,
        defineProps,
        defineExpose,
        nextTick,
        defineEmits
    } from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {useMessage} from '/@/hooks/web/useMessage';
    import { Popconfirm,Timeline  } from 'ant-design-vue';
    import PlanDetailsModal from './PlanDetailsModal.vue';
    import FireResponseListModal from './FireResponseListModal.vue';

    const props = defineProps(["disabled"])
    const emit = defineEmits(['success', 'register', 'ok'])
    const confirmLoading = ref<boolean>(false);
    const validatorRules = {};
    const Api = reactive<any>({
        list: '/sys/dtFireReportResponse/getResponseRecord',
    });
    const labelCol = reactive({
        xs: {span: 24},
        sm: {span: 4},
    })
    const wrapperCol = reactive({
        xs: {span: 24},
        sm: {span: 20},
    })
    const formState = reactive({});
    const responseResult = ref();
    const data = ref();
    const PlanDetailsModal1 = ref();
    const {createMessage} = useMessage();
    const useForm = Form.useForm;
    const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});
    onMounted(() => {
        //初始化字典选项
    });
    //查看
    function handleDetail() {
        PlanDetailsModal1.value.disableSubmit = false;
        PlanDetailsModal1.value.edit(formState);
    }
    function add() {
        edit({});
    }

    function edit(record) {
        nextTick(() => {
            resetFields();
            Object.assign(formState, record);
            loadData(record)
        });
    }

    function loadData(record) {
        let params = {
            reportId:record.id
        };
        defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                responseResult.value = res.result.responseResult;
                data.value = res.result.responseModelList;
            }
        }).finally(() => {
        });
    }
    /**
     * 提交数据
     */
    async function submitForm() {

    }

    defineExpose({
        add,
        edit,
        submitForm,
    });
</script>

<style lang="less" scoped>
    /deep/ .ant-descriptions-header {
        margin-bottom: 4px;
    }

    .charts {
        width: 100%;
        height: 200px;
        border: 1px solid #f0f0f0;
    }

    .video {
        width: 200px;
        height: 100px;
        float: left;
        margin-right: 5px;
        position: relative;
        background: #000;
    }

    .video .videos {
        width: 100%;
        max-height: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
</style>
