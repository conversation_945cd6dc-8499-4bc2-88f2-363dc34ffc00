<template>
    <a-spin :spinning="confirmLoading">
        <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
            <JFormContainer :disabled="disabled">
                <a-row>
                    <a-col :span="12">
                        <a-form-item label="告警类型" :wrapperCol="wrapperCol" :labelCol="labelCol">
                            <span v-if="formState.alarmType == '1'">疑似火警</span>
                            <span v-if="formState.alarmType == '2'">故障</span>
                            <span v-if="formState.alarmType == '3'">隐患</span>
                            <span v-if="formState.alarmType == '4'">其他</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="设备类型" :wrapperCol="wrapperCol" :labelCol="labelCol">
                            <span>{{ formState.deviceTypeName }}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="设备名称" :wrapperCol="wrapperCol" :labelCol="labelCol">
                            <span>{{ formState.deviceName }}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="设备位置" :wrapperCol="wrapperCol" :labelCol="labelCol">
                            <span>{{ formState.deviceLocation }}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="状态" :wrapperCol="wrapperCol" :labelCol="labelCol">
                            <span v-if="formState.alarmStatus == '1'">待处理</span>
                            <span v-if="formState.alarmStatus == '2'">已派单</span>
                            <span v-if="formState.alarmStatus == '3'">已处理</span>
                            <span v-if="formState.alarmStatus == '4'">关闭</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="逾期状态" :wrapperCol="wrapperCol" :labelCol="labelCol">
                            <span v-if="formState.overdueFlag == '0'">否</span>
                            <span v-if="formState.overdueFlag == '1'">是</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="告警内容" :wrapperCol="wrapperCol" :labelCol="labelCol">
                            <span>{{ formState.alarmDescription }}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="指派人员" :wrapperCol="wrapperCol" :labelCol="labelCol" name="repairUsername"
                                     v-bind="validateInfos.repairUsername">
                            <RepairUsernameSelect ref="RepairUsernameSelect1" @change="chooseUserNames"
                                                  v-model:value="formState.repairUsername"
                                                  placeholder="请选择指派人员" mode="default"
                                                  triggerChange="false"></RepairUsernameSelect>
                        </a-form-item>
                    </a-col>
                </a-row>
            </JFormContainer>
        </a-form>
    </a-spin>
</template>

<script lang="ts" setup>
    import {Form} from 'ant-design-vue';
    import {defineComponent, ref, reactive, onMounted, defineProps, defineExpose, UnwrapRef, nextTick, defineEmits} from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {useMessage} from '/@/hooks/web/useMessage';
    import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
    import RepairUsernameSelect from '/@/components/Form/src/jeecg/components/serchselect/RepairUsernameSelect.vue';

    const confirmLoading = ref<boolean>(false);
    const props = defineProps(["disabled"])
    const emits = defineEmits(['success', 'register', 'ok'])
    const labelCol = ref<any>({xs: {span: 24}, sm: {span: 7}});
    const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 16}});
    const {createMessage} = useMessage();
    const Api = reactive({
        edit: '/sys/dtAlarmOrder/assignUser',
    });

    const assignStaffOptions = ref<any>([])

    const formState = reactive({
        id: '',
        unitId: '',
        alarmType: '',
        deviceTypeName: '',
        deviceName: '',
        deviceLocation: '',
        alarmStatus: '',
        overdueFlag: '',
        alarmDescription: '',
        repairUsername: '',//	指派人员名称
        repairRealname: '',//	指派人员名称
    });
    const validatorRules = {
        repairUsername: [{required: true, message: '请选择指派人员', trigger: 'change'}],
    };
    const useForm = Form.useForm;
    const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

    function chooseUserNames(e,n,p) {
        formState.repairRealname = n
    }

    onMounted(() => {
        //初始化字典选项
    });

    function add() {
        edit({});
    }

    const RepairUsernameSelect1 = ref();

    function edit(record) {
        nextTick(() => {
            resetFields();
            Object.assign(formState, record);
            RepairUsernameSelect1.value.loadDictOptions(formState.unitId);
        });
    }

    /**
     * 提交数据
     */
    async function submitForm() {
        await validate();
        confirmLoading.value = true;
        let httpurl = '';
        let method = '';
        //时间格式化
        let model = {
            alarmId:formState.id,
            repairRealname:formState.repairRealname,
            repairUsername:formState.repairUsername,
        };
        httpurl += Api.edit;
        method = 'post';
        defHttp.request({url: httpurl, params: model, method: method,}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                createMessage.success(res.message);
                emits('ok');
            } else {
                createMessage.warning(res.message);
            }
        }).finally(() => {
            confirmLoading.value = false;
        });

    }

    defineExpose({
        add,
        edit,
        submitForm,
    });

</script>

<style scoped>
    .antd-modal-form {
        padding: 24px 24px 24px 24px;
    }
</style>
