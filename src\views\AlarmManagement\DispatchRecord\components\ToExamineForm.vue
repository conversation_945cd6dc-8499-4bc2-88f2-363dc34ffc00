<template>
    <a-spin :spinning="confirmLoading">
        <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
            <JFormContainer :disabled="disabled">
                <a-row>
                    <a-col :span="24">
                        <a-form-item label="告警类型" :labelCol="labelCols">
                            <span v-if="formState.alarmType == '1'">疑似火警</span>
                            <span v-if="formState.alarmType == '2'">故障</span>
                            <span v-if="formState.alarmType == '3'">隐患</span>
                            <span v-if="formState.alarmType == '4'">其他</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="设备名称" :labelCol="labelCol">
                            <span>{{formState.deviceName}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="设备位置" :labelCol="labelCol">
                            <span>{{formState.deviceLocation}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="告警内容" :labelCol="labelCol">
                            <span>{{formState.alarmDescription}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="告警时间" :labelCol="labelCol">
                            <span>{{formState.alarmTime}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="处理结果" :labelCol="labelCol">
                            <span>{{formState.repairResult}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="维修时间" :labelCol="labelCol">
                            <span>{{formState.repairTime}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="处理过程" :labelCol="labelCols">
                            <span>{{formState.repairProcess}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="处理照片" :labelCol="labelCols">
                            <Image :width="200" :src="formState.repairPicture"/>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="审核结果" name="checkResult" :labelCol="labelCols"
                                     v-bind="validateInfos.checkResult">
                            <JSelectMultiple v-model:value="formState.checkResult" placeholder="请选择处理结果"
                                             dictCode="checkResult" mode="default"
                                             triggerChange="false" @change="oncheckResultSelect"></JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="审核内容" name="checkProcess" :labelCol="labelCols"
                                     v-bind="validateInfos.checkProcess">
                            <a-textarea placeholder="请输入审核内容" v-model:value="formState.checkProcess"/>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24" v-if="show">
                        <a-form-item label="指派人员" :labelCol="labelCols" name="repairUsername"
                                     v-bind="validateInfos.repairUsername">
                            <RepairUsernameSelect ref="RepairUsernameSelect1" @change="chooseUserNames"
                                                  v-model:value="formState.repairUsername"
                                                  placeholder="请选择指派人员" mode="default"
                                                  triggerChange="false"></RepairUsernameSelect>
                        </a-form-item>
                    </a-col>
                </a-row>
            </JFormContainer>
        </a-form>
    </a-spin>
</template>

<script lang="ts" setup>
    import {Form, Image} from 'ant-design-vue';
    import {
        defineComponent,
        ref,
        reactive,
        onMounted,
        defineProps,
        defineExpose,
        UnwrapRef,
        nextTick,
        defineEmits
    } from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {useMessage} from '/@/hooks/web/useMessage';
    import JFormContainer from '@/components/Form/src/jeecg/components/JFormContainer.vue';
    import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
    import RepairUsernameSelect from '/@/components/Form/src/jeecg/components/serchselect/RepairUsernameSelect.vue';

    const props = defineProps(["disabled"])
    const emits = defineEmits(['success', 'register', 'ok'])
    const RepairUsernameSelect1 = ref();
    const confirmLoading = ref<boolean>(false);
    const show = ref<boolean>(false);
    const labelCol = ref<any>({xs: {span: 24}, sm: {span: 6}});
    const labelCols = ref<any>({xs: {span: 24}, sm: {span: 3}});
    const {createMessage} = useMessage();
    const formState = reactive({
        alarmType: "",
        deviceName: "",
        deviceLocation: "",
        alarmDescription: "",
        alarmTime: "",
        repairResult: "",
        repairTime: "",
        repairProcess: "",
        repairPicture: "",
        checkResult: "",
        checkProcess: "",
        repairUsername: "",
        repairRealname: "",
    });
    const validatorRules = {
        checkResult: [{required: true, message: '请选择处理结果', trigger: 'change'}],
        checkProcess: [{required: true, message: '请输入审核内容'}],
        repairUsername: [{required: false, message: '请选择指派人员', trigger: 'change'}],
    };

    const useForm = Form.useForm;
    const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

    const Api = reactive({
        edit: '/sys/dtAlarmOrder/checkAlarmOrder',
    });

    function oncheckResultSelect(e) {
        if (e == "不通过") {
            show.value = true
            validatorRules.repairUsername[0].required = true
            nextTick(() => {
                RepairUsernameSelect1.value.loadDictOptions(formState.unitId);
            });
        } else {
            show.value = false
            validatorRules.repairUsername[0].required = false
        }
    }

    function chooseUserNames(e, n, p) {
        formState.repairRealname = n
    }

    function add() {
        edit({});
    }

    function edit(record) {
        nextTick(() => {
            resetFields();
            Object.assign(formState, record);
            formState.repairPicture = formState.repairPicture ? window._CONFIG['imagesUrl'] + formState.repairPicture : ""
            formState.repairUsername = undefined
            formState.repairRealname = undefined
            show.value = false
        });
    }

    /**
     * 提交数据
     */
    async function submitForm() {
        await validate();
        confirmLoading.value = true;
        let httpurl = '';
        let method = '';
        //时间格式化
        let model = {
            id: formState.id,
            checkResult: formState.checkResult,
            checkProcess: formState.checkProcess,
        };
        if (formState.checkResult == "不通过") {
            model.repairUsername = formState.repairUsername
            model.repairRealname = formState.repairRealname
        }
        httpurl += Api.edit;
        method = 'post';
        defHttp.request({url: httpurl, params: model, method: method,}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                createMessage.success(res.message);
                emits('ok');
            } else {
                createMessage.warning(res.message);
            }
        }).finally(() => {
            confirmLoading.value = false;
        });
    }

    defineExpose({
        add,
        edit,
        submitForm,
    });
</script>

<style scoped>
    .antd-modal-form {
        padding: 24px 24px 24px 24px;
    }
</style>
