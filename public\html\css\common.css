@font-face {
  font-family: 'Source Han Sans CN';
  src: url('../font/SourceHanSansCN-Regular.ttf');
}
@font-face {
  font-family: 'YouSheBiaoTiHei-Regular';
  src: url('../font/优设标题黑.ttf') format('truetype');
}

@font-face {
  font-family: 'OPPOSans-Heavy';
  src: url('../font/OPPOSans-H.ttf') format('truetype');
}
* {
  margin: 0px;
  padding: 0px;
}
html {
  font-size: 30px;

}
body {
  height: auto;
  font: normal 100% Arial, sans-serif;
  font-size: .5rem;
}

html,
body {
  overflow: hidden;
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
}
.title {
  padding-top: 1%;
  overflow: hidden;
}
@media screen and (max-width:3000px) {
  html {
    font-size: 2vw;
  }
}

@media screen and (max-width:2000px) {
  html {
    font-size: 2vw;
  }
}

@media screen and (max-width:1900px) {
  html {
    font-size: 2vw;
  }
}

@media screen and (max-width:1500px) {
  html {
    font-size: 2vw;
  }
}

@media screen and (max-width:1100px) {
  html {
    font-size: 2vw;
  }
}
@media screen and (max-width:1000px) {
  html {
    font-size: 2vw;
  }
}
@media screen and (max-width:800px) {
  html {
    font-size: 2vw;
  }
}
