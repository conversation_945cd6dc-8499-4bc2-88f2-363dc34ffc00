<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <a-row>
        <a-col :span="12">
          <a-form-item label="单位名称" :labelCol="labelCol" name="unitName">
            <a-input placeholder="请输入参数编码" :disabled="true" v-model:value="formState.unitName"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="规则级别" :labelCol="labelCol" name="ruleLevel">
            <JSelectMultiple v-model:value="formState.ruleLevel" placeholder="请选择规则级别" dictCode="alarmRuleLevel"
              mode="default" :triggerChange="false"></JSelectMultiple>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="设备编码" v-if="formState.ruleLevel == '1'" :labelCol="labelCol" name="deviceCode">
            <JSelectMultiple @change="changeDeviceCode" v-model:value="formState.deviceCode" placeholder="请选择设备编码"
              :options="deviceCodeOptions" mode="default" :triggerChange="false"></JSelectMultiple>
          </a-form-item>
        </a-col><a-col :span="12">
          <a-form-item label="设备名称" v-if="formState.ruleLevel == '1'" :labelCol="labelCol" name="deviceName">
            <a-input placeholder="请输入设备名称" :disabled="true" v-model:value="formState.deviceName"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="设备分类" name="deviceCategory" v-if="formState.ruleLevel == '2'" :labelCol="labelCol">
            <JSelectMultiple @change="getSelectCategory" v-model:value="formState.deviceCategory" placeholder="请选择设备分类"
              :options="deviceCategoryOptions" mode="default" :triggerChange="false">
            </JSelectMultiple>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="设备类型" name="deviceType" v-if="formState.ruleLevel == '2'" :labelCol="labelCol">
            <JSelectMultiple v-model:value="formState.deviceType" placeholder="请选择设备类型" :options="deviceTypeOptions"
              mode="default" :triggerChange="false"></JSelectMultiple>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="规则名称" :labelCol="labelCol" name="ruleName">
            <a-input placeholder="请输入规则名称" v-model:value="formState.ruleName"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="规则类型" :labelCol="labelCol" name="ruleType">
            <JSelectMultiple @change="changeruleType" v-model:value="formState.ruleType" placeholder="请选择规则类型"
              :options="ruleTypeOptions" mode="default" :triggerChange="false"></JSelectMultiple>
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="iss == '1'">
          <a-form-item label="参数编码" :labelCol="labelCol" name="paramCode">
            <JSelectMultiple @change="chooseValues" v-model:value="formState.paramCode" placeholder="请选择参数编码"
              :options="paramCodeOptions" :triggerChange="false"></JSelectMultiple>
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="iss == '2'">
          <a-form-item label="设备状态" :labelCol="labelCol" name="deviceState">
            <JSelectMultiple v-model:value="formState.deviceState" placeholder="请选择设备状态" dict-code="devicestatus"
              mode="default" :triggerChange="false"></JSelectMultiple>
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="iss == '1'">
          <a-form-item label="告警间隔时长" :labelCol="labelCol" name="duration">
            <a-input addon-after="s" type="Number" placeholder="请输入告警间隔时长" v-model:value="formState.duration">s</a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24" v-if="iss == '1'">
          <a-form-item label="可用参数" :labelCol="labelCols" class="formItms">
            <div class="boxGroup" v-for="(item, index) in paramsCodeArr" :key="index" @click="addParams(item)">
              {{ item }}
            </div>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="公式" :labelCol="labelCols" name="formula">
            <a-textarea placeholder="请输入公式" v-model:value="formState.formula" />
          </a-form-item>
        </a-col>
        <a-col :span="24" v-if="iss == '1'" class="calcClass">
          <div class="calcBoxGroups">
            <div class="calcBoxGroup" v-for="(item, index) in calcBtn" :key="index" @click="addParams(item)">
              {{ item }}
            </div>
          </div>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" :labelCol="labelCols" name="remark">
            <a-textarea placeholder="请输入备注" v-model:value="formState.remark" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="报警类型" :labelCol="labelCol" name="alarmType">
            <JSelectMultiple v-model:value="formState.alarmType" placeholder="请选择报警类型" :options="alarmTypeOptions"
              mode="default" :triggerChange="false"></JSelectMultiple>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="告警码" :labelCol="labelCol" name="alarmCode">
            <a-input placeholder="请输入告警码" v-model:value="formState.alarmCode"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="是否启用" :labelCol="labelCol" name="status">
            <JSwitch v-model:value="formState.status" :options="['1', '0']" :labelOptions="['是', '否']"></JSwitch>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import { ref, reactive, defineEmits, onMounted, defineExpose } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
import { useUserStore } from '/@/store/modules/user';
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface';

const userStore = useUserStore();

const emit = defineEmits(['success', 'register', 'ok'])
const confirmLoading = ref<boolean>(false);
// 规则级别
const ruleLevelOptions = ref<any>([])
// 设备编码
const deviceCodeOptions = ref<any>([])
// 规则类型
const ruleTypeOptions = ref<any>([])
// 参数编码
const paramCodeOptions = ref<any>([])
// 告警类型
const alarmTypeOptions = ref<any>([])
// 设备分类
const deviceCategoryOptions = ref<any>([])
// 设备类型
const deviceTypeOptions = ref<any>([])
const Api = reactive({
  getDevice: '/sys/dtDevice/pageList',
  getAlarmLevel: '/sys/dict/getDictItemList',
  getParameCode: '/sys/dtParamTemplate/pageList',
  getDeviceCategoryList: '/sys/dtDeviceCategory/getDeviceCategoryList',
  getDeviceTypeList: '/sys/dtDeviceCategory/getDeviceTypeList',
  add: '/sys/dtAlarmRule/addAlarmRule',
  edit: '/sys/dtAlarmRule/editAlarmRule',
});
onMounted(() => {
  ruleLevelOptions.length = 0
  ruleTypeOptions.length = 0
  alarmTypeOptions.length = 0
  defHttp.get({ url: Api.getAlarmLevel, params: { dictCode: 'alarmRuleLevel' } }).then((e) => {
    ruleLevelOptions.value = e
  });
  defHttp.get({ url: Api.getAlarmLevel, params: { dictCode: 'alarmRuleType' } }).then((e) => {
    ruleTypeOptions.value = e
  });
  defHttp.get({ url: Api.getAlarmLevel, params: { dictCode: 'alarmType' } }).then((e) => {
    alarmTypeOptions.value = e
  });
  let params = ref({
    deviceCategory: formState.deviceCategory,
    deviceType: formState.deviceType
  })
  defHttp.get({ url: Api.getParameCode, params }).then((e) => {
    e.records.forEach(res => {
      res.value = res.paramCode
      res.label = res.paramName
    });
    paramCodeOptions.value = e.records
  });
  defHttp.get({ url: Api.getDeviceCategoryList }).then((e) => {
    e.forEach(i => {
      i.label = i.deviceCategory
      i.value = i.id
    });
    deviceCategoryOptions.value = e
  });
  // 暂无数据   设备编码
  defHttp.get({ url: Api.getDevice }).then((e) => {
    if (e.records.length != 0) {
      e.records.forEach(s => {
        s.value = s.deviceCode
        s.label = s.deviceCode
      });
    }
    deviceCodeOptions.value = e.records
  });
})
const getSelectCategory = (e: Object): void => {
  deviceTypeOptions.length = 0
  formState.deviceType = ''
  defHttp.get({ url: Api.getDeviceTypeList, params: { parentId: e } }).then((res) => {
    if (res.length != 0) {
      res.forEach(i => {
        i.label = i.deviceType
        i.value = i.id
      });
      deviceTypeOptions.value = res
    }
  });
}
function changeDeviceCode(e) {
  deviceCodeOptions.value.forEach(s => {
    if (s.deviceCode == e) {
      formState.deviceName = s.deviceName
    }
  });
}

const iss = ref('')
function changeruleType(e) {
  iss.value = e
}
const paramsCodeArr = ref<any>([])
const calcBtn = ref<any>(['7',
  '8',
  '9',
  '+',
  'AC',
  '>',
  '>=',
  '4',
  '5',
  '6',
  '-',
  "'",
  '<',
  '<=',
  '1',
  '2',
  '3',
  '*',
  '%',
  '||',
  '(',
  '0',
  '.',
  '#',
  '/',
  '==',
  '&&',
  ')',])


function chooseValues(e) {
  paramsCodeArr.value = e.split(',')
}
function addParams(e) {
  formState.formula += e
}

const formState = reactive({
  unitId: userStore.getLoginInfo.unitInfo.id,
  unitName: userStore.getLoginInfo.unitInfo.unitName,
  orgCode: userStore.getLoginInfo.unitInfo.orgCode,
  ruleLevel: '',
  deviceCode: '',
  deviceName: '',
  ruleName: '',
  ruleType: '',
  paramCode: '',
  duration: '',
  deviceState: '',
  formula: '',
  remark: '',
  alarmType: '',
  alarmCode: '',
  status: '0',
  deviceCategory: '',
  deviceType: ''
});
const formRef = ref();

const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 6 } });
const labelCols = ref<any>({ xs: { span: 24 }, sm: { span: 3 } });
// const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
//表单验证
const validatorRules = {
  unitName: [{ required: true, message: '请选输入单位名称' }],
  duration: [{ required: true, message: '请选输入告警间隔时长' }],
  ruleLevel: [{ required: true, message: '请选择规则级别', trigger: 'change' }],
  deviceCode: [{ required: true, message: '请选择设备编码', trigger: 'change' }],
  deviceName: [{ required: true, message: '请输入设备名称' }],
  ruleName: [{ required: true, message: '请输入规则名称' }],
  ruleType: [{ required: true, message: '请选择规则类型', trigger: 'change' }],
  paramCode: [{ required: true, message: '请选择参数编码', trigger: 'change' }],
  formula: [{ required: true, message: '请输入公式' }],
  alarmType: [{ required: true, message: '请选择告警类型', trigger: 'change' }],
  alarmCode: [{ required: true, message: '请输入告警编码' }],
  status: [{ required: true, message: '请选择是否启用', trigger: 'change' }],
  deviceCategory: [{ required: true, message: '请选择设备分类', trigger: 'change' }],
  deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  deviceState: [{ required: true, message: '请选择设备状态', trigger: 'change' }],
};
function submitForm() {
  if (!is.value) {
    let params = reactive({
      alarmCode: formState.alarmCode,
      alarmType: formState.alarmType,
      deviceCategory: formState.deviceCategory,
      deviceCode: formState.deviceCode,
      deviceName: formState.deviceName,
      deviceType: formState.deviceType,
      duration: formState.duration,
      formula: formState.formula,
      paramCode: formState.paramCode,
      remark: formState.remark,
      ruleLevel: formState.ruleLevel,
      ruleName: formState.ruleName,
      ruleType: formState.ruleType,
      status: formState.status,
      unitId: formState.unitId,
      unitName: formState.unitName,
      orgCode: formState.orgCode,
      deviceState: formState.deviceState,
    })
    console.log(params);

    formRef.value.validate().then(() => {
      defHttp.post({ url: Api.add, params }).then((e) => {
        emit('ok');
      });
    }).catch((error: ValidateErrorEntity<any>) => {
      console.log('error', error);
    });
  } else {
    console.log(formState);

    let params = reactive({
      alarmCode: formState.alarmCode,
      alarmType: formState.alarmType,
      duration: formState.duration,
      formula: formState.formula,
      paramCode: formState.paramCode,
      remark: formState.remark,
      ruleName: formState.ruleName,
      ruleType: formState.ruleType,
      status: formState.status,
      deviceState: formState.deviceState,
      id: formState.id,
    })

    formRef.value.validate().then(() => {
      defHttp.put({ url: Api.edit, params }).then((e) => {
        emit('ok');
      });
    }).catch((error: ValidateErrorEntity<any>) => {
      console.log('error', error);
    });

  }

}
function add(e) {
  Object.assign(formState, {
    unitId: userStore.getLoginInfo.unitInfo.id,
    unitName: userStore.getLoginInfo.unitInfo.unitName,
    orgCode: userStore.getLoginInfo.unitInfo.orgCode,
    ruleLevel: '',
    deviceCode: '',
    deviceName: '',
    ruleName: '',
    ruleType: '',
    paramCode: '',
    duration: '',
    formula: '',
    remark: '',
    alarmType: '',
    alarmCode: '',
    status: '0',
    deviceCategory: '',
    deviceType: '',
    deviceState: ''
  })

}
let is = ref(0)
function edit(e) {
  console.log(e);
  is.value = 1
  iss.value = e.ruleType
  paramsCodeArr.value = e.paramCode.split(',')
  getSelectCategory(e.deviceCategory)
  Object.assign(formState, e)
}
defineExpose({
  add,
  edit,
  submitForm,
});
</script>

<style lang="less" scoped> .antd-modal-form {
   padding: 24px 24px 24px 24px;
 }

 ::v-deep .formItms .ant-form-item-control-input-content {
   display: flex;
   flex-direction: row;
   align-items: center;
   justify-content: flex-start;
 }

 .boxGroup {
   max-width: 100px;
   padding: 0 10px;
   border: 1px solid #DEDEDE;
   cursor: pointer;
   border-radius: 5px;
   text-align: center;
   margin-right: 8px;

   &:active {
     background-color: #DEDEDE;
   }
 }


 .calcClass {
   min-height: 200px;

   .calcBoxGroups {
     width: 100%;
     height: 28%;
     padding: 0 23%;
     display: flex;
     flex-direction: row;
     align-items: center;
     justify-content: flex-start;
     flex-wrap: wrap;

     .calcBoxGroup {
       width: 12.2%;
       height: 62.2%;
       background-color: #DEDEDE;
       border-radius: 5px;
       cursor: pointer;
       margin-right: 10px;
       margin-bottom: 10px;
       display: flex;
       flex-direction: row;
       align-items: center;
       justify-content: center;
     }
   }
 }
</style>
