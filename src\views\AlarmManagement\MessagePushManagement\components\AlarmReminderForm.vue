<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="24">
            <a-form-item label="推送主题" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="pushTopic"
                         v-bind="validateInfos.pushTopic">
              <a-input placeholder="请输入推送主题" v-model:value="formState.pushTopic"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="选择单位" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="unitId"
                         v-bind="validateInfos.unitId">
              <UnitIdSelect v-model:value="formState.unitId" placeholder="请选择单位名称"
                            mode="default" @change="onUnitIdSelect" :disabled="formState.id"></UnitIdSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="formState.ruleType=='1'">
            <a-form-item label="告警级别" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="alarmLevels"
                         v-bind="validateInfos.alarmLevels">
              <JSelectMultiple :placeholder="'请选择告警级别'"
                               v-model:value="formState.alarmLevels" @change="onJSelectMultiple"
                               dictCode="alarmLevel" mode="default" :triggerChange="false">
              </JSelectMultiple>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-show="formState.ruleType=='1'">
            <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="deviceCodes"
                         v-bind="validateInfos.deviceCodes">
              <template #label>
                <span>特定设备</span>
                <a-tooltip title="默认推送全部设备信息，当特定设备选择后，仅推送特定设备的告警">
                  <a-icon type="question-circle"/>
                </a-tooltip>
              </template>
              <DeviceCodesSelect ref="DeviceCodesSelect1" :placeholder="'请选择特定设备'"
                                v-model:value="formState.deviceCodes" @change="onDeviceCodesSelect"
                                :triggerChange="false">
              </DeviceCodesSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="formState.ruleType=='2'">
            <a-form-item label="累计推送上限" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="pushCount"
                         v-bind="validateInfos.pushCount">
              <a-input-number style="width: 80%" placeholder="请输入累计推送上限" v-model:value="formState.pushCount" :min="0" :precision="0"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="推送方式" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="pushType"
                         v-bind="validateInfos.pushType">
              <PushTypeSelect :placeholder="'请选择推送方式'"
                              v-model:value="formState.pushType"
                              :triggerChange="false" @change="onPushTypeSelect">
              </PushTypeSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="formState.pushType.indexOf('3')!=-1">
            <a-form-item label="短信模板" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="templateCode"
                         v-bind="validateInfos.templateCode">
              <TemplateCodeSelect :placeholder="'请选择短信模板'"
                                  v-model:value="formState.templateCode"
                                  mode="default" :triggerChange="false">
              </TemplateCodeSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="推送内容" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="pushContent"
                         v-bind="validateInfos.pushContent">
              <a-textarea placeholder="请输入推送内容" v-model:value="formState.pushContent"
                          rows="3"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="通知人员" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="pushUsernames"
                         v-bind="validateInfos.pushUsernames">
              <JSelectUserId @change="onJSelectUserId" ref='JSelectUserId1' v-model:value="formState.pushUsernames" placeholder="请选择用户"></JSelectUserId>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="状态" :labelCol="labelCol" name="status">
              <JSwitch v-model:value="formState.status" :options="['1', '0']" :labelOptions="['是', '否']"
                       checkedChildren="启用"
                       unCheckedChildren="停用"></JSwitch>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import {Form} from 'ant-design-vue';
import {
  defineComponent,
  ref,
  reactive,
  onMounted,
  defineProps,
  defineExpose,
  UnwrapRef,
  nextTick,
  defineEmits
} from 'vue';
import {useMessage} from '/@/hooks/web/useMessage';
import {defHttp} from '/@/utils/http/axios';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import DeviceCodesSelect
  from '/@/components/Form/src/jeecg/components/serchselect/DeviceCodesSelect.vue';
import PushTypeSelect from '/@/components/Form/src/jeecg/components/serchselect/PushTypeSelect.vue';
import UnitIdSelect from "@/components/Form/src/jeecg/components/serchselect/UnitIdSelect.vue";
import TemplateCodeSelect
  from "@/components/Form/src/jeecg/components/serchselect/TemplateCodeSelect.vue";
import JSelectUserId from "../../../../components/Form/src/jeecg/components/JSelectUserId.vue";
import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';



const useForm = Form.useForm;
const confirmLoading = ref<boolean>(false);
const labelCol = ref<any>({xs: {span: 24}, sm: {span: 6}});
const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 18}});
const labelCol1 = ref<any>({xs: {span: 24}, sm: {span: 4}});
const wrapperCol1 = ref<any>({xs: {span: 24}, sm: {span: 20}});
const {createMessage} = useMessage();
const Api = reactive({
  add: '/sys/dtPushConfig/addPushConfig',
  edit: '/sys/dtPushConfig/editPushConfig',
});
const formState = reactive({
  id: undefined,
  ruleType: '',
  pushTopic: '',
  unitId: '',
  alarmLevels: '',
  deviceCodes: '',
  deviceNames: '',
  pushType: ["1"],
  templateCode: '',
  templateContent: '',
  pushContent: '',
  pushUsernames: '',
  pushUsernames: '',
  pushCount: '5',
  status: '1',
});
const emits = defineEmits([ 'ok'])

//表单验证
const validatorRules = {
  pushTopic: [{required: true, message: '请输入推送主题!'}],
  unitId: [{required: true, message: '请选择单位名称!', trigger: 'change'}],
  alarmLevels: [{required: true, message: '请选择告警级别!', trigger: 'change'}],
  pushType: [{required: true, message: '请选择推送方式!', trigger: 'change'}],
  templateCode: [{required: false, message: '请选择短信模板!', trigger: 'change'}],
  pushContent: [{required: true, message: '请输入推送内容!'}],
  pushUsernames: [{required: true, message: '请选择通知人员!', trigger: 'change'}],
  pushCount: [{required: true, message: '请输入累积推送上限!'}],
  status: [{required: true, message: '请选择是否启用!'}],
};
const {
  resetFields,
  validate,
  validateInfos
} = useForm(formState, validatorRules, {immediate: false});
const DeviceCodesSelect1 = ref()
function onUnitIdSelect(e){
  formState.deviceCodes = undefined
  DeviceCodesSelect1.value.arrayValue = undefined
  DeviceCodesSelect1.value.loadDictOptions(e);
}
function onDeviceCodesSelect(e,n){
  formState.deviceCodes = e
  formState.deviceNames = n
}
function onJSelectMultiple(){
}
function onPushTypeSelect(e){
  if (e.indexOf("3")!=-1) {
    validatorRules.templateCode[0].required = true
  }else{
    validatorRules.templateCode[0].required = false
  }
}
const JSelectUserId1 = ref()
function onJSelectUserId(e){
  let name = []
  let pushUserids = []
  for(let item of JSelectUserId1.value.selectOptions){
    name.push(item.label)
    pushUserids.push(item.id)
  }
  formState.pushUserids = pushUserids.toString()
  formState.pushUsernames = name.toString()
  formState.pushUsernames = e
}
function add() {
  edit({});
}

function edit(record) {
  nextTick(() => {
    resetFields();
    const tmpData = {};
    Object.keys(formState).forEach((key) => {
      if (record.hasOwnProperty(key)) {
        tmpData[key] = record[key]
      }
    })
    tmpData.pushType = record.pushType?record.pushType.split(","):["1"]
    tmpData.pushUserids = record.pushUserids?record.pushUserids:""
    Object.assign(formState, tmpData);
    DeviceCodesSelect1.value.loadDictOptions(record.unitId);
    if (formState.ruleType == "2") {
      validatorRules.alarmLevels[0].required = false
      validatorRules.pushCount[0].required = true
    }else{
      validatorRules.alarmLevels[0].required = true
      validatorRules.pushCount[0].required = false
    }
  });
}

/**
 * 提交数据
 */
async function submitForm() {
  await validate();
  confirmLoading.value = true;
  let httpurl = '';
  let method = '';
  //时间格式化
  let model = Object.assign({}, formState);
  if (!formState.id) {
    httpurl += Api.add;
    method = 'post';
  } else {
    httpurl += Api.edit;
    method = 'put';
  }
  if(formState.ruleType=='1'){
    model.pushCount = undefined
  }
  model.pushType = model.pushType.toString()
  defHttp.request({
    url: httpurl,
    params: model,
    method: method,
  }, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      createMessage.success(res.message);
      emits('ok');
    } else {
      createMessage.warning(res.message);
    }
  }).finally(() => {
    confirmLoading.value = false;
  });
}

defineExpose({
  add,
  edit,
  submitForm
});
</script>

<style scoped>
.antd-modal-form {
  padding: 24px 24px 24px 24px;
}
</style>
