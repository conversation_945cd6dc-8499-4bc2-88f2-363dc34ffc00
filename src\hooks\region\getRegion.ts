import json from './region.json'
import { ref } from 'vue'
const areaName = ref<any>([])
function getRegionName(e) {
    areaName.value = []
    if (e != '') {
        e.split(',').forEach(s => {
            getNewTree(json, s)
        });
    }
    return Array.from(new Set(areaName.value)).join(',');
}
function getNewTree(obj, e) {
    obj.map(item => {
        if (item.code == e) return areaName.value.push(item.name)
        if (item.children && item.children.length > 0) getNewTree(item.children, e)
    })
}
export {
    getRegionName
}