<!-- 逾期推送管理 OverduePushManagement -->
<template>
  <a-card :bordered="false">
    <Tabs :activeKey="activeKeyRef" class="tabsbox" @tabClick="componentClick">
      <TabPane tab="告警提醒" key="1" style="width: 100%">
        <!--自定义查询区域-->
        <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
          <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
            <a-row :gutter="24">
              <a-col :lg="6">
                <a-form-item label="告警级别">
                  <JSelectMultiple :placeholder="'请选择告警级别'"
                                   v-model:value="queryParam.alarmType"
                                   dictCode="alarmLevel" mode="default" :triggerChange="false">
                  </JSelectMultiple>
                </a-form-item>
              </a-col>
              <a-col :lg="6">
                <a-form-item label="推送主题">
                  <a-input placeholder="请输入推送主题"
                           v-model:value="queryParam.unitName"></a-input>
                </a-form-item>
              </a-col>
              <a-col :lg="6">
                <a-form-item label="选择单位">
                  <UnitIdSelect v-model:value="queryParam.unitId" placeholder="请选择单位名称"
                                mode="default"></UnitIdSelect>
                </a-form-item>
              </a-col>
              <a-col :lg="6">
                <a-form-item label="通知人员">
                  <a-input placeholder="请输入通知人员"
                           v-model:value="queryParam.unitName"></a-input>
                </a-form-item>
              </a-col>
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery"
                           style="margin-right: 5px">查询
                </a-button>
                <a-button preIcon="ant-design:sync-outlined" @click="searchReset">重置
                </a-button>
                <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleAdd(1)"
                          style="margin-left: 5px">
                  新增
                </a-button>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- table区域-begin -->
        <JVxeTable
          ref="tableRef"
          bordered
          row-number
          keep-source
          resizable
          :maxHeight="484"
          :loading="loading"
          :dataSource="dataSource"
          :columns="columns"
          :pagination="pagination"
          style="margin-top: 8px"
          @pageChange="handlePageChange">
          <template #alarmLevels="props">
            <span v-if="props.row.alarmLevels&&props.row.alarmLevels.indexOf('1')!=-1">一级告警</span>
            <span v-if="props.row.alarmLevels&&props.row.alarmLevels.indexOf('1')!=-1&&props.row.alarmLevels.indexOf('2')!=-1">，</span>
            <span v-if="props.row.alarmLevels&&props.row.alarmLevels.indexOf('2')!=-1">二级告警</span>
            <span v-if="props.row.alarmLevels&&props.row.alarmLevels.indexOf('2')!=-1&&props.row.alarmLevels.indexOf('3')!=-1">，</span>
            <span v-if="props.row.alarmLevels&&props.row.alarmLevels.indexOf('3')!=-1">三级告警</span>
          </template>
          <template #pushType="props">
            <span v-if="props.row.pushType&&props.row.pushType.indexOf('1')!=-1">平台</span>
            <span v-if="props.row.pushType&&props.row.pushType.indexOf('1')!=-1&&props.row.pushType.indexOf('2')!=-1">，</span>
            <span v-if="props.row.pushType&&props.row.pushType.indexOf('2')!=-1">APP</span>
            <span v-if="props.row.pushType&&props.row.pushType.indexOf('2')!=-1&&props.row.pushType.indexOf('3')!=-1">，</span>
            <span v-if="props.row.pushType&&props.row.pushType.indexOf('3')!=-1">短信</span>
          </template>
          <template #status="props">
            <span v-if="props.row.status==1">启用</span>
            <span v-else>停用</span>
          </template>
          <template #action="props">
            <Popconfirm title="是否启用？" v-if="props.row.status == '0'" @confirm="handleStatus(props.row)">
              <a>启用</a>
            </Popconfirm>
            <Popconfirm title="是否停用？" v-else @confirm="handleStatus(props.row)">
              <a>停用</a>
            </Popconfirm>
            <a-divider type="vertical"/>
            <a @click="handleEdit(props.row)">编辑</a>
            <a-divider type="vertical"/>
            <Popconfirm title="是否删除？" @confirm="handleDelete(props.row)">
              <a>删除</a>
            </Popconfirm>
          </template>
        </JVxeTable>
      </TabPane>
      <TabPane tab="逾期提醒" key="2" forceRender>
        <!--自定义查询区域-->
        <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
          <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
            <a-row :gutter="24">
              <a-col :lg="6">
                <a-form-item label="推送主题">
                  <a-input placeholder="请输入推送主题"
                           v-model:value="queryParam1.unitName"></a-input>
                </a-form-item>
              </a-col>
              <a-col :lg="6">
                <a-form-item label="通知人员">
                  <a-input placeholder="请输入通知人员"
                           v-model:value="queryParam1.unitName"></a-input>
                </a-form-item>
              </a-col>
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery1"
                          style="margin-right: 5px">查询
                </a-button>
                <a-button preIcon="ant-design:sync-outlined" @click="searchReset1">重置
                </a-button>
                <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleAdd(2)"
                          style="margin-left: 5px">
                  新增
                </a-button>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!-- table区域-begin -->
        <JVxeTable
          ref="tableRef"
          bordered
          row-number
          keep-source
          resizable
          :maxHeight="484"
          :loading="loading"
          :dataSource="dataSource1"
          :columns="columns1"
          :pagination="pagination1"
          style="margin-top: 8px"
          @pageChange="handlePageChange1">
          <template #pushType="props">
            <span v-if="props.row.pushType&&props.row.pushType.indexOf('1')!=-1">平台</span>
            <span v-if="props.row.pushType&&props.row.pushType.indexOf('1')!=-1&&props.row.pushType.indexOf('2')!=-1">，</span>
            <span v-if="props.row.pushType&&props.row.pushType.indexOf('2')!=-1">APP</span>
            <span v-if="props.row.pushType&&props.row.pushType.indexOf('2')!=-1&&props.row.pushType.indexOf('3')!=-1">，</span>
            <span v-if="props.row.pushType&&props.row.pushType.indexOf('3')!=-1">短信</span>
          </template>
          <template #status="props">
            <span v-if="props.row.status==1">启用</span>
            <span v-else>停用</span>
          </template>
          <template #action="props">
            <Popconfirm title="是否启用？" v-if="props.row.status == '0'" @confirm="handleStatus(props.row)">
              <a>启用</a>
            </Popconfirm>
            <Popconfirm title="是否停用？" v-else @confirm="handleStatus(props.row)">
              <a>停用</a>
            </Popconfirm>
            <a-divider type="vertical"/>
            <a @click="handleEdit(props.row)">编辑</a>
            <a-divider type="vertical"/>
            <Popconfirm title="是否删除？" @confirm="handleDelete(props.row)">
              <a>删除</a>
            </Popconfirm>
          </template>
        </JVxeTable>
      </TabPane>
    </Tabs>
  </a-card>
  <AlarmReminderModal ref="AlarmReminderModal1" @ok="onAlarmReminderModal"/>
</template>

<script lang="ts" setup>
import {onMounted, ref, reactive} from 'vue';
import {defHttp} from '/@/utils/http/axios';
import {Popconfirm, Tabs} from 'ant-design-vue';
import {filterObj} from '/@/utils/common/compUtils';
import {useUserStore} from '/@/store/modules/user';
import {useMessage} from '/@/hooks/web/useMessage';
import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
import JSelectMultiple from "../../../components/Form/src/jeecg/components/JSelectMultiple.vue";
import UnitIdSelect from '/@/components/Form/src/jeecg/components/serchselect/UnitIdSelect.vue';
import AlarmReminderModal from './components/AlarmReminderModal.vue';

const {TabPane} = Tabs;
const userStore = useUserStore();
const AlarmReminderModal1 = ref();
const labelCol = reactive({
  xs: {span: 24},
  sm: {span: 8},
})
const wrapperCol = reactive({
  xs: {span: 24},
  sm: {span: 16},
})
const activeKeyRef = ref('1');
const queryParam = ref<any>({});
const queryParam1 = ref<any>({});
const loading = ref<boolean>(false);
const columns = ref([
  {
    title: '推送主题',
    key: 'pushTopic',
    minWidth: 150,
    align: "center",
  },
  {
    title: '单位名称',
    key: 'unitName',
    minWidth: 150,
    align: "center",
  },
  {
    title: '告警等级',
    key: 'alarmLevels',
    minWidth: 250,
    align: "center",
    type: 'slot',
    slotName: 'alarmLevels',
  },
  {
    title: '特定设备',
    key: 'deviceNames',
    minWidth: 250,
    align: "center",
  },
  {
    title: '通知人员',
    key: 'pushUsernames',
    minWidth: 150,
    align: "center",

  },
  {
    title: '推送方式',
    key: 'pushType',
    minWidth: 150,
    align: "center",
    type: 'slot',
    slotName: 'pushType',
  },
  {
    title: '短信模板',
    key: 'templateContent',
    minWidth: 200,
    align: "center",
  },
  {
    title: '推送内容',
    key: 'pushContent',
    minWidth: 200,
    align: "center",
  },
  {
    title: '状态',
    key: 'status',
    minWidth: 150,
    align: "center",
    type: 'slot',
    slotName: 'status',
  },
  {
    title: '操作',
    type: "slot",
    key: 'action',
    align: 'center',
    fixed: 'right',
    width: 200,
    slotName: 'action',
  },
]);
const columns1 = ref([
  {
    title: '推送主题',
    key: 'pushTopic',
    minWidth: 150,
    align: "center",
  },
  {
    title: '单位名称',
    key: 'unitName',
    minWidth: 150,
    align: "center",
  },
  {
    title: '累积推送上限',
    key: 'pushCount',
    minWidth: 250,
    align: "center",
  },
  {
    title: '通知人员',
    key: 'pushUsernames',
    minWidth: 150,
    align: "center",

  },
  {
    title: '推送方式',
    key: 'pushType',
    minWidth: 150,
    align: "center",
    type: 'slot',
    slotName: 'pushType',
  },
  {
    title: '短信模板',
    key: 'templateContent',
    minWidth: 200,
    align: "center",
  },
  {
    title: '推送内容',
    key: 'pushContent',
    minWidth: 200,
    align: "center",
  },
  {
    title: '状态',
    key: 'status',
    minWidth: 150,
    align: "center",
    type: 'slot',
    slotName: 'status',
  },
  {
    title: '操作',
    type: "slot",
    key: 'action',
    align: 'center',
    fixed: 'right',
    width: 200,
    slotName: 'action',
  },
]);
const Api = reactive<any>({
  list: '/sys/dtPushConfig/pageList',
  delete: '/sys/dtPushConfig/delPushConfig',
  editPushConfigStatus: '/sys/dtPushConfig/editPushConfigStatus',
});
const dataSource = ref<any>([{}]);
const dataSource1 = ref<any>([{}]);
const pagination = ref<any>({
  current: 1,
  pageSize: 10,
  pageSizeOptions: ['10', '20', '30'],
  showTotal: (total, range) => {
    return range[0] + '-' + range[1] + ' 共' + total + '条';
  },
  showQuickJumper: true,
  showSizeChanger: true,
  total: 0,
});
const pagination1 = ref<any>({
  current: 1,
  pageSize: 10,
  pageSizeOptions: ['10', '20', '30'],
  showTotal: (total, range) => {
    return range[0] + '-' + range[1] + ' 共' + total + '条';
  },
  showQuickJumper: true,
  showSizeChanger: true,
  total: 0,
});
const iSorter = ref<any>({column: 'createTime', order: 'desc'});
const iFilters = ref<any>({});
const iSorter1 = ref<any>({column: 'createTime', order: 'desc'});
const iFilters1 = ref<any>({});
const {createMessage} = useMessage();

function componentClick(key) {
  activeKeyRef.value = key;
  if(key=='1'){
    loadData(1)
  }else{
    loadData1(1)
  }
}

//新增
function handleAdd(ruleType) {
  let params = {
    ruleType:ruleType,
  }
  AlarmReminderModal1.value.disableSubmit = false;
  AlarmReminderModal1.value.edit(params);
}

//新增
function handleEdit(record) {
  AlarmReminderModal1.value.disableSubmit = false;
  AlarmReminderModal1.value.edit(record);
}

/**
 * 获取查询参数
 */
function getQueryParams() {
  let params = Object.assign(queryParam.value, iSorter.value, iFilters.value);
  params.pageNo = pagination.value.current;
  params.pageSize = pagination.value.pageSize;
  return filterObj(params);
}
function getQueryParams1() {
  let params = Object.assign(queryParam1.value, iSorter1.value, iFilters1.value);
  params.pageNo = pagination1.value.current;
  params.pageSize = pagination1.value.pageSize;
  return filterObj(params);
}
// 当分页参数变化时触发的事件
function handlePageChange(event) {
  // 重新赋值
  pagination.value.current = event.current;
  pagination.value.pageSize = event.pageSize;
  // 查询数据
  loadData();
}
// 当分页参数变化时触发的事件
function handlePageChange1(event) {
  // 重新赋值
  pagination1.value.current = event.current;
  pagination1.value.pageSize = event.pageSize;
  // 查询数据
  loadData1();
}
//查询
function searchQuery() {
  loadData(1);
}
//查询
function searchQuery1() {
  loadData1(1);
}
//重置
function searchReset() {
  queryParam.value = {};
  loadData(1);
}
//重置
function searchReset1() {
  queryParam1.value = {};
  loadData1(1);
}
function loadData(arg) {
  if (arg === 1) {
    pagination.value.current = 1;
  }
  loading.value = true;
  let params = getQueryParams();
  params.ruleType = activeKeyRef.value
  defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      dataSource.value = res.result.records;
      if (res.result && res.result.total) {
        pagination.value.total = res.result.total;
      } else {
        pagination.value.total = 0;
      }
    }
  }).finally(() => {
    loading.value = false;
  });
}
function loadData1(arg) {
  if (arg === 1) {
    pagination1.value.current = 1;
  }
  loading.value = true;
  let params = getQueryParams1();
  params.ruleType = activeKeyRef.value
  defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      dataSource1.value = res.result.records;
      if (res.result && res.result.total) {
        pagination1.value.total = res.result.total;
      } else {
        pagination1.value.total = 0;
      }
    }
  }).finally(() => {
    loading.value = false;
  });
}
function handleDelete(e) {
  defHttp.delete({url: Api.delete, data: {id: e.id}}, {
    isTransformResponse: false,
    joinParamsToUrl: true
  }).then((res) => {
    if (res.success) {
      if(activeKeyRef.value == '2'){
        loadData1()
      }else{
        loadData()
      }
      createMessage.success(res.message);
    } else {
      createMessage.warning(res.message);
    }
  });
}

function handleStatus(n) {
  let params = {
    id: n.id,
    status: n.status == '0' ? '1' : '0'
  }
  defHttp.get({url: Api.editPushConfigStatus, params}, {
    isTransformResponse: false,
    joinParamsToUrl: true
  }).then((res) => {
    if (res.success) {
      if(activeKeyRef.value == '2'){
        loadData1()
      }else{
        loadData()
      }
      createMessage.success(res.message);
    } else {
      createMessage.warning(res.message);
    }
  });
}
function onAlarmReminderModal(){
  if(activeKeyRef.value == '2'){
    loadData1()
  }else{
    loadData()
  }
}
onMounted(() => {
  //初始加载页面
  loadData();
});

</script>

<style scoped>

</style>
