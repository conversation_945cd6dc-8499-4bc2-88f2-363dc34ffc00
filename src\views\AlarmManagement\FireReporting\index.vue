<template>
    <a-card :bordered="false">
        <!--自定义查询区域-->
        <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
            <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
                <a-row :gutter="24">
                    <a-col :lg="6">
                        <a-form-item label="单位名称">
                            <a-input placeholder="请输入单位名称" v-model:value="queryParam.unitName"></a-input>
                        </a-form-item>
                    </a-col>
                    <a-col :lg="6">
                        <a-form-item label="开始时间">
                            <DateSelect v-model:value="queryParam.startDate" placeholder="请选择开始时间"
                                            @change="onStartDateSelect"></DateSelect>
                        </a-form-item>
                    </a-col>
                    <a-col :lg="6">
                        <a-form-item label="结束时间">
                            <DateSelect v-model:value="queryParam.endDate" placeholder="请选择结束时间"
                                            @change="onEndDateSelect"></DateSelect>
                        </a-form-item>
                    </a-col>
                    <a-col :lg="6">
                        <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery"
                                  style="margin-right: 5px">查询
                        </a-button>
                        <a-button preIcon="ant-design:sync-outlined" @click="searchReset" style="margin-left: 8px">重置
                        </a-button>
                    </a-col>
                </a-row>
            </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <div class="table-operator">

        </div>
        <!-- table区域-begin -->
        <JVxeTable
                ref="tableRef"
                bordered
                row-number
                keep-source
                resizable
                :maxHeight="484"
                :loading="loading"
                :dataSource="dataSource"
                :columns="columns"
                :pagination="pagination"
                style="margin-top: 8px"
                @pageChange="handlePageChange">
            <template #reportStatus="props">
                <span v-if="props.row.reportStatus=='1'">待响应</span>
                <span v-if="props.row.reportStatus=='2'">响应中</span>
                <span v-if="props.row.reportStatus=='3'">已响应</span>
            </template>
            <template #action="props">
                <a @click="handleViewReport(props.row)">查看简报</a>
                <a-divider type="vertical"/>
                <a @click="handleFireResponse(props.row)" v-if="props.row.reportStatus=='2'">火灾响应</a>
                <a @click="handleResponseRecord(props.row)" v-else-if="props.row.reportStatus=='3'">响应记录</a>
                <a @click="handleActivateContingencyPlan(props.row)" v-else>启动预案</a>
            </template>
        </JVxeTable>
    </a-card>
    <ViewReportModal ref="ViewReportModal1"></ViewReportModal>
    <ActivateContingencyPlanModal ref="ActivateContingencyPlanModal1" @ok="loadData"></ActivateContingencyPlanModal>
    <FireResponseModal ref="FireResponseModal1" @ok="loadData"></FireResponseModal>
    <ResponseRecordModal ref="ResponseRecordModal1" @ok="loadData"></ResponseRecordModal>
</template>

<script lang="ts" setup>
    import {onMounted, ref, reactive} from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {filterObj} from '/@/utils/common/compUtils';
    import {initDictOptions} from '/@/utils/dict';
    import {Popconfirm} from 'ant-design-vue';
    import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
    import DateSelect from '/@/components/Form/src/jeecg/components/serchselect/DateSelect.vue';
    import ViewReportModal from './components/ViewReportModal.vue';
    import ActivateContingencyPlanModal from './components/ActivateContingencyPlanModal.vue';
    import FireResponseModal from './components/FireResponseModal.vue';
    import ResponseRecordModal from './components/ResponseRecordModal.vue';

    import {useUserStore} from '/@/store/modules/user';

    const userStore = useUserStore();
    const ViewReportModal1 = ref();
    const ActivateContingencyPlanModal1 = ref();
    const FireResponseModal1 = ref();
    const ResponseRecordModal1 = ref();
    const labelCol = reactive({
        xs: {span: 24},
        sm: {span: 8},
    })
    const wrapperCol = reactive({
        xs: {span: 24},
        sm: {span: 16},
    })
    const queryParam = ref<any>({});
    const loading = ref<boolean>(false);
    const columns = ref([
        {
            title: '上报单位',
            key: 'unitName',
            minWidth: 150,
            align: "center",
        },
        {
            title: '简报',
            key: 'reportDesc',
            minWidth: 150,
            align: "center",
        },
        {
            title: '火势大小',
            key: 'fireSize',
            minWidth: 100,
            align: "center",
        },
        {
            title: '是否蔓延',
            key: 'spreadFlag',
            minWidth: 100,
            align: "center",
        },
        {
            title: '燃烧物质',
            key: 'material',
            minWidth: 100,
            align: "center",
        },
        {
            title: '是否有人员受伤',
            key: 'injuredFlag',
            minWidth: 120,
            align: "center",
        },
        {
            title: '是否有人被困',
            key: 'stuckFlag',
            minWidth: 120,
            align: "center",
        },
        {
            title: '被困人员数量和位置',
            key: 'position',
            minWidth: 150,
            align: "center",
        },
        {
            title: '上报人',
            key: 'reportRealname',
            minWidth: 150,
            align: "center",
        },
        {
            title: '上报时间',
            key: 'reportTime',
            minWidth: 200,
            align: "center",
        },
        {
            title: '状态',
            key: 'reportStatus',
            minWidth: 100,
            align: "center",
            type: "slot",
            slotName: "reportStatus",
        },
        {
            title: '操作',
            type: "slot",
            key: 'action',
            align: 'center',
            fixed: 'right',
            width: 150,
            slotName: 'action',
        },
    ]);
    const Api = reactive<any>({
        list: '/sys/dtFireReport/pageList',
    });
    const dataSource = ref<any>([]);
    const pagination = ref<any>({
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条';
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0
    });

    const iSorter = ref<any>({column: 'createTime', order: 'desc'});
    const iFilters = ref<any>({});

    /**
     * 获取查询参数
     */
    function getQueryParams() {
        let params = Object.assign(queryParam.value, iSorter.value, iFilters.value);
        params.pageNo = pagination.value.current;
        params.pageSize = pagination.value.pageSize;
        return filterObj(params);
    }

    // 开始时间
    function onStartDateSelect(e) {
    }

    // 结束时间
    function onEndDateSelect(e) {
    }


    // 当分页参数变化时触发的事件
    function handlePageChange(event) {
        // 重新赋值
        pagination.value.current = event.current;
        pagination.value.pageSize = event.pageSize;

        // 查询数据
        loadData();
    }


    function loadData(arg) {
        if (arg === 1) {
            pagination.value.current = 1;
        }
        loading.value = true;
        let params = getQueryParams();
        defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                // res.result.records[0].superviseStatus = '0'
                // dataSource.value = [];
                dataSource.value = res.result.records;
                if (res.result && res.result.total) {
                    pagination.value.total = res.result.total;
                } else {
                    pagination.value.total = 0;
                }
            }
        }).finally(() => {
            loading.value = false;
        });
    }

    //查询
    function searchQuery() {
        loadData(1);
    }


    //重置
    function searchReset() {
        queryParam.value = {};
        queryParam.value.unitId = userStore.getLoginInfo.unitInfo.id;
        loadData(1);
    }

    //查看
    function handleViewReport(e) {
        ViewReportModal1.value.disableSubmit = false;
        ViewReportModal1.value.edit(e);
    }
    //查看
    function handleActivateContingencyPlan(e) {
        ActivateContingencyPlanModal1.value.disableSubmit = false;
        ActivateContingencyPlanModal1.value.edit(e);
    }
    //查看
    function handleFireResponse(e) {
        FireResponseModal1.value.disableSubmit = false;
        FireResponseModal1.value.edit(e);
    }
    function handleResponseRecord(e) {
        ResponseRecordModal1.value.disableSubmit = false;
        ResponseRecordModal1.value.edit(e);
    }
    async function initDictConfig() {
        queryParam.value.unitId = userStore.getLoginInfo.unitInfo.id;
    }

    function handleSuccess() {
        loadData(1);
    }

    onMounted(() => {
        //初始化字典选项
        initDictConfig();
        //初始加载页面
        loadData();
    });
</script>
<style lang="less" scoped>
    .jeecg-basic-table-form-container {
        padding: 0px;

        .table-page-search-submitButtons {
            display: block;
            margin-bottom: 0;
            white-space: nowrap;
        }

        .ant-form-item {
            margin-bottom: 8px !important;
        }
    }
</style>
