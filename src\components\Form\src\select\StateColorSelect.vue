<template>
  <div class="demo-color-block">
    <el-color-picker
      v-model="selectedColor"
      @change="change">
    </el-color-picker>
    {{selectedColor}}
  </div>
</template>

<script lang="ts">
  import {ref,defineEmits,watch,defineComponent} from 'vue'
  import {propTypes} from '/src/utils/propTypes';

  export default defineComponent({
    name: 'StateColorSelect',
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.number, propTypes.array]),
    },
    emits: ['change', 'update:value'],
    setup(props, {emit, refs}) {
      const selectedColor = ref('')
      watch(() => props.value, (val) => {
          if (!val) {
            selectedColor.value = "";
          } else {
            selectedColor.value = props.value;
          }
        }
      );
      function change(e) {
        selectedColor.value = e
        emit('change', e);
        emit('update:value', e)
      }
      return {
        change,
        selectedColor,
      };
    },
  });


</script>

<style>
  .demo-color-block {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .demo-color-block .demonstration {
    margin-right: 16px;
  }
</style>
