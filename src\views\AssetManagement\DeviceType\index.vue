<!-- 设备设施类型 -->
<template>
    <a-card :bordered="false">
        <!--自定义查询区域-->
        <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
            <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
                <a-row :gutter="24">
                    <a-col :lg="6">
                        <a-form-item label="设备设施类型">
                            <a-input placeholder="请输入设备设施类型" v-model:value="queryParam.deviceCategory"></a-input>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
            <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery"
                style="margin-right: 5px">查询</a-button>
            <a-button @click="handleAdd" type="primary" preIcon="ant-design:plus" style="margin-right: 5px">新增</a-button>
        </div>
        <!-- table区域-begin -->
        <a-table :columns="columns" rowKey="id" :bordered="true" :data-source="dataSource"
            class="components-table-demo-nested" :expandedRowKeys="expandedRowKeys" @expand="expandList">
            <template #operation="props">
                <a @click="handleEdit(props.record)">编辑</a>
                <a-divider type="vertical" />
                <a @click="handleAddType(props.record)">添加子级</a>
            </template>
            <template #expandedRowRender>
                <a-table :columns="innerColumns" :data-source="innerDataSource" :pagination="false">
                    <template #operation="props">
                        <a @click="handleEditType(props.record)">编辑</a>
                        <a-divider type="vertical" />
                    </template>
                </a-table>
            </template>
        </a-table>
    </a-card>
    <DeptModal ref="DeptModal1" @ok="handleSuccess" />
    <DeptModalAdd ref="DeptModalA" @ok="handleSuccess" />
    <DeptModalEdit ref="DeptModalE" @ok="handleSuccess" />
    <DeptModals ref="DeptModalEditType" @ok="handleSuccess" />
</template>
<!--巡检项次-->
<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { loadCategoryData } from '/@/api/common/api';
import { initDictOptions } from '/@/utils/dict';
import DeptModal from './components/DeptModal.vue';
import DeptModalAdd from './components/DeptModal1.vue';
import DeptModalEdit from './components/DeptModalEdit.vue';
import DeptModals from './components/DeptModalEditType.vue';
const DeptModalEditType = ref();
let ids = ref('')
let pId = ref('')
const expandedRowKeys = ref<any>([])
function handleEditType(record) {
    ids = record.id
    DeptModalEditType.value.disableSubmit = false;
    DeptModalEditType.value.edit(record);
}
function handleAddType(record) {
    ids = record.id
    DeptModalEditType.value.disableSubmit = false;
    DeptModalEditType.value.add(record);
}
function expandList(flag, record) {
    expandedRowKeys.value = []
    if (flag) {
        expandedRowKeys.value.push(record.id)
        pId = record.id
        getTypeList(record.id)
    }
}

function getTypeList(pId) {
    innerDataSource.length = 0
    defHttp.get({ url: Api.getDeviceTypeList, params: { parentId: pId } }).then((res) => {
        res.forEach(e => {
            innerDataSource.push(e)
        });
    }).finally(() => {
        loading.value = false;
    });
}
function loadDatas(e) {
    dataSource.length = 0
    let params = {
        parentId: e
    }
    defHttp.get({ url: Api.getDeviceTypeList, params }).then((res) => {
        dataSource.value = res
    });
}

const dataSource = reactive<any>([]);
const innerDataSource = reactive<any>([]);

const columns = [
    // { title: 'ID', dataIndex: 'id', key: 'id' },
    { title: '消防设备分类', dataIndex: 'deviceCategory', key: 'deviceCategory' },
    { title: '序值', dataIndex: 'dataSort', key: 'dataSort' },
    { title: '操作', key: 'operation', slots: { customRender: 'operation' } },
];

const innerColumns = [
    { title: '消防设备类型', dataIndex: 'deviceType', key: 'deviceType', width: 245 },
    { title: '序值', dataIndex: 'dataSort', key: 'dataSort', width: 500 },
    { title: '操作', key: 'operation', slots: { customRender: 'operation' } },
];


const DeptModal1 = ref();
const DeptModalA = ref();
const DeptModalE = ref();
function handleAdd() {
    DeptModal1.value.disableSubmit = false;
    DeptModal1.value.add();
}
function handleAddChildren(e) {
    DeptModalA.value.disableSubmit = false;
    DeptModalA.value.add(e);
}
function handleEdit(record) {
    DeptModalE.value.disableSubmit = false;
    DeptModalE.value.edit(record);
}

//-----自定义查询----begin--------
const labelCol = reactive({
    xs: { span: 24 },
    sm: { span: 10 },
})
const wrapperCol = reactive({
    xs: { span: 24 },
    sm: { span: 14 },
})
const queryParam = ref<any>({
    deviceCategory: ''
});
const loading = ref<boolean>(false);
const dictOptions = ref<any>([]);
const Api = reactive<any>({
    list: '/sys/dtDeviceCategory/getDeviceCategoryList',
    getDeviceTypeList: '/sys/dtDeviceCategory/getDeviceTypeList',


});

const pagination = ref<any>({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30'],
    showTotal: (total, range) => {
        return range[0] + '-' + range[1] + ' 共' + total + '条';
    },
    showQuickJumper: true,
    showSizeChanger: true,
    total: dataSource.length,
});

const selectedRowKeys = ref<any>([]);
const selectionRows = ref<any>([]);

// 当分页参数变化时触发的事件
function handlePageChange(event) {
    // 重新赋值
    pagination.value.current = event.current;
    pagination.value.pageSize = event.pageSize;
    // 查询数据
    loadData();
}

function delType(e) {
    console.log(e);
}


function loadData(arg) {
    if (arg === 1) {
        pagination.value.current = 1;
    }
    loading.value = true;
    let params = queryParam.value
    dataSource.length = 0
    defHttp.get({ url: Api.list, params }).then((res) => {
        res.forEach((e, i) => {
            e.key = i + 1
            dataSource.push(e)
        });
        if (res && res.total) {
            pagination.value.total = res.total;
        } else {
            pagination.value.total = 0;
        }
    }).finally(() => {
        loading.value = false;
    });
}
//查询
function searchQuery() {
    loadData(1);
    selectedRowKeys.value = [];
    selectionRows.value = [];
}


/**
 * 初始化字典选项
 */
async function initDictConfig() {
}
/**
 * 保存表单后回调事件
 */
function handleSuccess() {
    selectedRowKeys.value = [];
    selectionRows.value = [];
    loadData(1);
    loadDatas(ids)
    getTypeList(pId)
}

onMounted(() => {
    dictOptions.value['kaiguan'] = [
        { text: '是', value: '1' },
        { text: '否', value: '2' },
    ];
    //初始加载页面
    loadData();
    //初始化字典选项
    initDictConfig();
});
</script>
<style lang="less" scoped>
::v-deep .ant-table-cell {
    text-align: center;
}

.jeecg-basic-table-form-container {
    padding: 0px;

    .table-page-search-submitButtons {
        display: block;
        margin-bottom: 0;
        white-space: nowrap;
    }

    .ant-form-item {
        margin-bottom: 8px !important;
    }
}
</style>
