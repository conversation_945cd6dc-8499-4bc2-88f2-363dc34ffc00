<template>
    <a-input :placeholder="props.placeholderName" v-model:value="inputVal" @input="getInput"
        @blur="blurInput($event)"></a-input>
</template>

<script lang="ts" setup>
    import { ref, defineProps, defineEmits } from 'vue'
    const props = defineProps(['placeholderName', 'value'])
    const emit = defineEmits(['returnInputValue'])
    let inputVal = ref < string > ('')
    inputVal = ref < string > (props.value)
    const getInput = () => {
        // console.log(props.value);
    }
    const blurInput = (event) => {
        emit('returnInputValue', event.target.value)
    }
</script>
<style lang="less" scoped>

</style>
