<template>
    <a-spin :spinning="confirmLoading">
        <a-form class="antd-modal-form" ref="formRef" :model="formState">
            <JFormContainer :disabled="disabled">
                <a-row :gutter="24">
                    <a-col :span="24">
                        <a-tabs v-model:activeKey="activeKey">
                            <a-tab-pane key="1" tab="基础信息评分">
                                <p>总分：{{}} 得分：{{}}</p>
                                <JVxeTable ref="tableRef1" bordered row-number keep-source resizable :maxHeight="484"
                                           :loading="loading1" :dataSource="dataSource1" :columns="columns1"
                                           :pagination="pagination1"
                                           style="margin-top: 8px"
                                           @pageChange="handlePageChange1">
                                </JVxeTable>
                            </a-tab-pane>
                            <a-tab-pane key="2" tab="联网告警评分">
                                <p>总分：{{}} 得分：{{}}</p>
                                <JVxeTable ref="tableRef2" bordered row-number keep-source resizable :maxHeight="484"
                                           :loading="loading2" :dataSource="dataSource2" :columns="columns2"
                                           :pagination="pagination2"
                                           style="margin-top: 8px"
                                           @pageChange="handlePageChange2">
                                </JVxeTable>
                            </a-tab-pane>
                        </a-tabs>
                    </a-col>
                </a-row>
            </JFormContainer>
        </a-form>
    </a-spin>
</template>

<script lang="ts" setup>
    import {Form} from 'ant-design-vue';
    import {ref, nextTick, defineEmits, reactive,defineExpose} from 'vue';
    import {getDictItemList} from '@/hooks/params/param'
    import MonthSelect from '/@/components/Form/src/jeecg/components/serchselect/MonthSelect.vue';
    import {defHttp} from '/@/utils/http/axios';


    const confirmLoading = ref<boolean>(false);
    const formState = reactive({});
    const useForm = Form.useForm;
    const activeKey = ref('1')
    const emits = defineEmits(['success', 'register', 'ok'])
    const loading1 = ref<boolean>(false);
    const loading2 = ref<boolean>(false);
    const columns1 = ref([
        {
            title: '评分维度',
            key: 'systemName',
            minWidth: 150,
            align: "center",
        },
        {
            title: '扣分',
            key: 'buildingType_dictText',
            minWidth: 180,
            align: "center",
        },
        {
            title: '扣分原因',
            key: 'systemName',
            minWidth: 80,
            align: "center",
        },
        {
            title: '扣分时间',
            key: 'systemName',
            minWidth: 80,
            align: "center",
        },
    ]);
    const dataSource1 = ref<any>([]);
    const columns2 = ref([
        {
            title: '扣分',
            key: 'systemName',
            minWidth: 150,
            align: "center",
        },
        {
            title: '超时级别',
            key: 'buildingType_dictText',
            minWidth: 180,
            align: "center",
        },
        {
            title: '告警内容',
            key: 'systemName',
            minWidth: 80,
            align: "center",
        },
        {
            title: '设备类型',
            key: 'systemName',
            minWidth: 80,
            align: "center",
        },
        {
            title: '告警类型',
            key: 'systemName',
            minWidth: 80,
            align: "center",
        },
        {
            title: '设备名称',
            key: 'systemName',
            minWidth: 80,
            align: "center",
        },
        {
            title: '设备编码',
            key: 'systemName',
            minWidth: 80,
            align: "center",
        },
        {
            title: '扣分时间',
            key: 'systemName',
            minWidth: 80,
            align: "center",
        },
    ]);
    const dataSource2 = ref<any>([]);
    const pagination1 = ref<any>({
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条';
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: dataSource1.value.length,
    });
    const pagination2 = ref<any>({
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条';
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: dataSource2.value.length,
    });
    const Api = reactive<any>({
        list: '/sys/dtAssessSystem/pageList',
    });
    // 当分页参数变化时触发的事件
    function handlePageChange1(event) {
        // 重新赋值
        pagination1.value.current = event.current;
        pagination1.value.pageSize = event.pageSize;
        // 查询数据
        loadData1();
    }
    function loadData1(arg) {
        if (arg === 1) {
            pagination1.value.current = 1;
        }
        loading1.value = true;
        defHttp.get({url: Api.list,}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                dataSource1.value = res.result.records;
                if (res.result && res.result.total) {
                    pagination1.value.total = res.result.total;
                } else {
                    pagination1.value.total = 0;
                }
            }
        }).finally(() => {
            loading1.value = false;
        });
    }
    // 当分页参数变化时触发的事件
    function handlePageChange2(event) {
        // 重新赋值
        pagination1.value.current = event.current;
        pagination1.value.pageSize = event.pageSize;
        // 查询数据
        loadData2();
    }
    function loadData2(arg) {
        if (arg === 1) {
            pagination2.value.current = 1;
        }
        loading2.value = true;
        defHttp.get({url: Api.list}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                dataSource2.value = res.result.records;
                if (res.result && res.result.total) {
                    pagination2.value.total = res.result.total;
                } else {
                    pagination2.value.total = 0;
                }
            }
        }).finally(() => {
            loading2.value = false;
        });
    }
    function add() {
        edit({});
    }

    function edit(record) {
        loadData1(1)
        loadData2(1)
    }

    function submitForm() {

    }

    defineExpose({
        add,
        edit,
        submitForm,
        activeKey
    });

</script>

<style lang="less" scoped>
    .chooseUnit {
        color: rgb(0, 153, 255);
        cursor: pointer;
    }

    .antd-modal-form {
        padding: 24px 24px 24px 24px;
    }

    .uploadBoxs {
        width: 50%;
        height: 150px;
        cursor: pointer;
        border: 1px dashed #1296db;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        border-radius: 10px;

        img {
            width: 30px;
            height: 30px;
        }
    }
</style>
