<!-- 报警规则配置 -->
<template>
    <a-card :bordered="false">
        <!--自定义查询区域-->
        <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
            <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
                <a-row :gutter="24">
                    <a-col :lg="6">
                        <a-form-item label="规则级别">
                            <JSelectMultiple v-model:value="formState.ruleLevel" placeholder="请选择规则级别"
                                :options="ruleLevelOptions" mode="default" :triggerChange="false">
                            </JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="设备分类" name="deviceCategory" :labelCol="labelCol">
                            <JSelectMultiple @change="getSelectCategory" v-model:value="formState.deviceCategory"
                                placeholder="请选择设备分类" :options="deviceCategoryOptions" mode="default"
                                :triggerChange="false">
                            </JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="设备类型" name="deviceType" :labelCol="labelCol">
                            <JSelectMultiple v-model:value="formState.deviceType" placeholder="请选择设备类型"
                                :options="deviceTypeOptions" mode="default" :triggerChange="false"></JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="设备编码" name="alarmCode" :labelCol="labelCol">
                            <a-input placeholder="请输入设备编码" v-model:value="formState.deviceCode"></a-input>
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="设备名称" name="alarmCode" :labelCol="labelCol">
                            <a-input placeholder="请输入设备名称" v-model:value="formState.deviceName"></a-input>
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="规则类型" name="deviceCategory" :labelCol="labelCol">
                            <JSelectMultiple v-model:value="formState.ruleType" placeholder="请选择规则类型"
                                :options="ruleTypeOptions" mode="default" :triggerChange="false">
                            </JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="告警类型" name="deviceType" :labelCol="labelCol">
                            <JSelectMultiple v-model:value="formState.alarmType" placeholder="请选择告警类型"
                                :options="alarmTypeOptions" mode="default" :triggerChange="false"></JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="告警编码" name="alarmCode" :labelCol="labelCol">
                            <a-input placeholder="请输入告警编码" v-model:value="formState.alarmCode"></a-input>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
            <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery"
                style="margin-right: 5px">查询</a-button>
            <a-button @click="handleAdd" type="primary" preIcon="ant-design:plus" style="margin-right: 5px">新增</a-button>

        </div>
        <!-- table区域-begin -->
        <JVxeTable ref="tableRef" bordered row-number keep-source resizable :maxHeight="484" :loading="loading"
            :dataSource="dataSource" :columns="columns" :pagination="pagination" style="margin-top: 8px" row-selection
            @pageChange="handlePageChange">
            <template #ruleType="props">
                 <span> {{ props.row.ruleType == '1' ? '采集参数' : '状态' }} </span>
            </template>
            <template #alarmType="props">
                 <span> {{ props.row.ruleType == '1' ? '报警' : '离线' }} </span>
            </template>
            <template #action="props">
                <a @click="handleEdit(props.row)">编辑</a>
                <a-divider type="vertical" />
                <Popconfirm title="请确认是否下发？">
                    <a>下发</a>
                </Popconfirm>
                <a-divider type="vertical" />
                <Popconfirm title="请确认是否删除？" @confirm="handleDel(props.row)">
                    <a>删除</a>
                </Popconfirm>
            </template>
        </JVxeTable>
    </a-card>
    <DeptModal ref="DeptModal1" @ok="handleSuccess" />
</template>
<!--巡检项次-->
<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { Popconfirm } from 'ant-design-vue';
import { loadCategoryData } from '/@/api/common/api';
import { initDictOptions } from '/@/utils/dict';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import DeptModal from './components/DeptModal.vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { getTimeProps } from 'ant-design-vue/lib/date-picker/generatePicker';
const DeptModal1 = ref();
// 规则级别
const ruleLevelOptions = ref<any>([])
// 设备分类
const deviceCategoryOptions = ref<any>([])
// 设备类型
const deviceTypeOptions = ref<any>([])
// 规则类型
const ruleTypeOptions = ref<any>([])
// 告警类型
const alarmTypeOptions = ref<any>([])
function handleAdd() {
    DeptModal1.value.disableSubmit = false;
    DeptModal1.value.add();
}
function handleEdit(e) {
    DeptModal1.value.disableSubmit = false;
    DeptModal1.value.edit(e);
}
const labelCol = reactive({
    xs: { span: 24 },
    sm: { span: 10 },
})
const wrapperCol = reactive({
    xs: { span: 24 },
    sm: { span: 14 },
})
const loading = ref<boolean>(false);
const dictOptions = ref<any>([]);
const { createMessage } = useMessage();
const formState = reactive({
    ruleLevel: '',
    deviceCategory: '',
    deviceType: '',
    deviceCode: '',
    deviceName: '',
    ruleType: '',
    alarmType: '',
    alarmCode: '',
    pageNo: '1',
    pageSize: '10',
});

//表头
const columns = ref([
    {
        title: '单位名称',
        key: 'unitName',
        align: "center",
        minWidth: 250,
    },
    {
        title: '规则名称',
        key: 'ruleName',
        minWidth: 150,
        align: "center",
    },
    {
        title: '规则级别',
        key: 'ruleLevel',
        minWidth: 100,
        align: "center",
    },
    {
        title: '设备分类',
        key: 'deviceCategoryName',
        minWidth: 100,
        align: "center",
    },
    {
        title: '设备类型',
        key: 'deviceTypeName',
        minWidth: 150,
        align: "center",
    }, {
        title: '设备编码',
        key: 'deviceCode',
        minWidth: 150,
        align: "center",
    },
    {
        title: '设备名称',
        key: 'deviceName',
        minWidth: 220,
        align: "center",
    },
    {
        title: '规则类型',
        key: 'ruleType',
        minWidth: 150,
        align: "center",
        type:'slot',
        slotName:'ruleType'
    },
    {
        title: '报警类型',
        key: 'alarmType',
        minWidth: 200,
        align: "center",
        type:'slot',
        slotName:'alarmType'
    }, {
        title: '告警码',
        key: 'alarmCode',
        minWidth: 150,
        align: "center",
    },
    {
        title: '操作',
        type: "slot",
        key: 'action',
        align: 'center',
        fixed: 'right',
        width: 150,
        slotName: 'action',
    },
]);
const dataSource = ref<any>([]);
const Api = reactive<any>({
    list: '/sys/dtAlarmRule/pageList',
    getDeviceCategoryList: '/sys/dtDeviceCategory/getDeviceCategoryList',
    getDeviceTypeList: '/sys/dtDeviceCategory/getDeviceTypeList',
    getAlarmLevel: '/sys/dict/getDictItemList',
    edit: '/sys/dtAlarmBase/editAlarmBase',
    del: '/sys/dtAlarmRule/delAlarmRule'
});
function handleDel(e) {
    defHttp.delete({ url: Api.del, data: { id: e.id } }, { isTransformResponse: false,joinParamsToUrl: true }).then((res) => {
        if(res.success){
            loadData();
            createMessage.success(res.message);
        }else {
            createMessage.warning(res.message);
        }
    });
}
const pagination = ref<any>({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30'],
    showTotal: (total, range) => {
        return range[0] + '-' + range[1] + ' 共' + total + '条';
    },
    showQuickJumper: true,
    showSizeChanger: true,
    total: dataSource.value.length,
});

const selectedRowKeys = ref<any>([]);
const selectionRows = ref<any>([]);

onMounted(() => {
    dictOptions.value['kaiguan'] = [
        { text: '是', value: '1' },
        { text: '否', value: '2' },
    ];
    defHttp.get({ url: Api.getAlarmLevel, params: { dictCode: 'alarmRuleLevel' } }).then((e) => {
        // options3.value = e
    });
    getTimeProps()
    //初始加载页面
    loadData();
    //初始化字典选项
    initDictConfig();
});
// 获取条件
function getTimeProps() {
    defHttp.get({ url: Api.getAlarmLevel, params: { dictCode: 'alarmRuleLevel' } }).then((e) => {
        ruleLevelOptions.value = e
    });
    defHttp.get({ url: Api.getAlarmLevel, params: { dictCode: 'alarmRuleType' } }).then((e) => {
        ruleTypeOptions.value = e
    });
    defHttp.get({ url: Api.getAlarmLevel, params: { dictCode: 'alarmType' } }).then((e) => {
        alarmTypeOptions.value = e
    });
    defHttp.get({ url: Api.getDeviceCategoryList }).then((e) => {
        e.forEach(i => {
            i.label = i.deviceCategory
            i.value = i.id
        });
        deviceCategoryOptions.value = e
    });
}

const getSelectCategory = (e: Object): void => {
    deviceTypeOptions.length = 0
    defHttp.get({ url: Api.getDeviceTypeList, params: { parentId: e } }).then((res) => {
        if (res.length != 0) {
            res.forEach(i => {
                i.label = i.deviceType
                i.value = i.id
            });
            deviceTypeOptions.value = res
        }
    });
}

// 当分页参数变化时触发的事件
function handlePageChange(event) {
    // 重新赋值
    pagination.value.current = event.current;
    pagination.value.pageSize = event.pageSize;
    formState.pageNo = event.current;
    formState.pageSize = event.pageSize;
    // 查询数据
    loadData();
}

/**
 * 初始化数据
 */
function loadData(arg) {

    if (arg === 1) {
        pagination.value.current = 1;
    }
    loading.value = true;
    let params = formState
    defHttp.get({ url: Api.list, params }, { isTransformResponse: false }).then((res) => {
        console.log(res);

        dataSource.value = res.result.records
        if (res.result && res.result.total) {
            pagination.value.total = res.result.total;
        } else {
            pagination.value.total = 0;
        }

    }).finally(() => {
        loading.value = false;
    });
}
//查询
function searchQuery() {
    loadData(1);
    selectedRowKeys.value = [];
    selectionRows.value = [];
}


/**
 * 初始化字典选项
 */
async function initDictConfig() {
}
/**
 * 保存表单后回调事件
 */
function handleSuccess() {
    selectedRowKeys.value = [];
    selectionRows.value = [];
    loadData(1);
}


</script>
<style lang="less" scoped>
.jeecg-basic-table-form-container {
    padding: 0px;

    .table-page-search-submitButtons {
        display: block;
        margin-bottom: 0;
        white-space: nowrap;
    }

    .ant-form-item {
        margin-bottom: 8px !important;
    }
}
</style>
