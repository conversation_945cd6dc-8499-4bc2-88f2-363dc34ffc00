<template>
  <span v-for="tag in tagsData">
    <a-checkable-tag
      style="border:1px solid #d9d9d9;"
      :key="tag.value"
      :checked="selectedTags.indexOf(tag.value) > -1"
      @change="checked => handleChange(tag.value, checked)"
    >{{ tag.label }}</a-checkable-tag>
  </span>
</template>
<script lang="ts">
  import {
    defineComponent,
    PropType,
    ref,
    reactive,
    watchEffect,
    computed,
    unref,
    watch,
    onMounted,
    nextTick
  } from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import {propTypes} from '/src/utils/propTypes';
  import {LoadingOutlined} from '@ant-design/icons-vue';
  import {Tag} from 'ant-design-vue';
  import {useMessage} from '/@/hooks/web/useMessage';

  export default defineComponent({
    name: 'EnergyLabelTag',
    inheritAttrs: false,
    components: {LoadingOutlined},
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.number, propTypes.array]),
      parentId: propTypes.string,
    },
    emits: ['options-change', 'change', 'update:value'],
    setup(props, {emit, refs}) {
      const tagsData = ref([])
      const selectedTags = ref([])
      const param1 = ref()
      const parentenergyLabel = ref()
      const Api = reactive<any>({
        loadTreeData: '/sys/category/loadTreeRoot',
        getDepartByOrgCode: '/sys/sysDepart/getDepartByOrgCode',
      });
      const {createMessage} = useMessage();
      //update-begin-author:taoyan date:20220404 for: 使用useRuleFormItem定义的value，会有一个问题，如果不是操作设置的值而是代码设置的控件值而不能触发change事件
      // 此处添加空值的change事件,即当组件调用地代码设置value为''也能触发change事件
      watch(() => props.value, (val) => {
          if (!val) {
            selectedTags.value = [];
          }
        }
      );
      onMounted(() => {
      });
      function loadData(param) {
        param1.value = param
        if (param&&param.editbutton) {
          let params = {
            orgCode: param.orgCode,
          }
          defHttp.get({url: Api.getDepartByOrgCode, params}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
              parentenergyLabel.value = res.result.energyLabel
            } else {
            }
          })
          let params1 = {
            pcode: "A03",
            async: false,
          }
          defHttp.get({url: Api.loadTreeData, params:params1}, {isTransformResponse: false}).then((res) => {
            tagsData.value = []
            if (res.success) {
              for (let item of res.result) {
                tagsData.value.push({
                  value:item['code'],
                  label:item['title'],
                });
              }
            } else {
            }
          })
        }else{
          if(param&&param.orgCode){
            let params = {
              orgCode: param.orgCode,
            }
            defHttp.get({url: Api.getDepartByOrgCode, params}, {isTransformResponse: false}).then((res) => {
              tagsData.value = []
              if (res.success) {
                if(res.result.energyLabel){
                  for (let i in res.result.energyLabel.split(",")) {
                    tagsData.value.push({
                      value:res.result.energyLabel.split(",")[i],
                      label:res.result.energyLabel_dictText.split(",")[i],
                    });
                  }
                }
              } else {
              }
            })
          }else{
            let params = {
              pcode: "A03",
              async: false,
            }
            defHttp.get({url: Api.loadTreeData, params}, {isTransformResponse: false}).then((res) => {
              tagsData.value = []
              if (res.success) {
                for (let item of res.result) {
                  tagsData.value.push({
                    value:item['code'],
                    label:item['title'],
                  });
                }
              } else {
              }
            })
          }
        }
      }
      function handleChange(tag, checked) {
        const nextSelectedTags = checked ? [...selectedTags.value, tag] : selectedTags.value.filter(t => t !== tag)
        selectedTags.value = nextSelectedTags
        if (param1.value&&param1.value.editbutton) {
          if (checked) {
            if (parentenergyLabel.value.indexOf(tag) < 0) {
              createMessage.warning("当前父级中不存在此标签，不能选中！");
              selectedTags.value = nextSelectedTags.filter(item => item !== tag)
            }else{
            }
          }
        }
        emit('change', selectedTags.value.toString(), tag, checked);
        emit('update:value', selectedTags.value);
      }

      return {
        handleChange,
        selectedTags,
        tagsData,
        loadData,
      };
    },
  });
</script>


<style scoped lang="less">
  // update-begin--author:liaozhiyang---date:20230110---for：【QQYUN-7799】字典组件（原生组件除外）加上颜色配置
  .colorText {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    padding: 0 6px;
    border-radius: 8px;
    background-color: red;
    color: #fff;
    font-size: 12px;
  }

  // update-begin--author:liaozhiyang---date:20230110---for：【QQYUN-7799】字典组件（原生组件除外）加上颜色配置
</style>
