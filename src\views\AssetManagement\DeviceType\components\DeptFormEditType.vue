<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <a-row>
        <a-col :span="24">
          <a-form-item label="消防设备分类:" :labelCol="labelCol">
            <JSelectMultiple v-model:value="formState.parentId" placeholder="请选择消防设备分类" :options="options" mode="default"
              :disabled="true" :triggerChange="false">
            </JSelectMultiple>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="消防设备类型" :labelCol="labelCol" name="deviceType">
            <a-input placeholder="请输入消防设备类型" v-model:value="formState.deviceType"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="LOGO" :labelCol="labelCol">
            <JImageUpload :url-params="`?businessCategory=deviceType1&businessType=deviceType2&businessId=deviceType3`"
              :fileMax="1" v-model:value="formState.logoUrl"></JImageUpload>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="序值" :labelCol="labelCol" name="dataSort">
            <a-input placeholder="请输入序值" v-model:value="formState.dataSort"></a-input>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import { ref, reactive, defineEmits, defineExpose } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import JImageUpload from '/@/components/Form/src/jeecg/components/JImageUpload.vue';
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface';

const options = ref<any>([])
let ids = ref<string>('')

const emits = defineEmits(['success', 'register', 'ok'])

const formState = reactive({
  deviceCategory: '',
  dataSort: "",
  deviceType: "",
  logoUrl: "",
});
const formRef = ref();
const Api = reactive({
  edit: '/sys/dtDeviceCategory/editDeviceCategory',
  getDeviceCategoryList: '/sys/dtDeviceCategory/getDeviceCategoryList',
  add: '/sys/dtDeviceCategory/addDeviceCategory',
});

const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 6 } });
const confirmLoading = ref<boolean>(false);
//表单验证
const validatorRules = {
  deviceCategory: [{ required: true, message: '请输入分类名称' }],
  dataSort: [{ required: true, message: '请输入序值' }],
  deviceType: [{ required: true, message: '请输入消防设备类型名称' }],
};
function edit(e) {
  console.log(e);

  Object.assign(formState, e)
  // 获取分类
  defHttp.get({ url: Api.getDeviceCategoryList }).then((e) => {
    e.forEach(i => {
      i.label = i.deviceCategory
      i.value = i.id
    });
    options.value = e
  });
  ids = ref(e.id)
}
function submitForm(is) {
  console.log(is.value);
  let params = reactive({})
  if (is.value) {
    // 新增
    params = reactive({
      dataFlag: '1',
      dataSort: formState.dataSort,
      deviceType: formState.deviceType,
      logoUrl: formState.logoUrl,
      parentId: ids,
    })
    formRef.value.validate().then(() => {
      defHttp.post({ url: Api.add, params }).then((e) => {
        emits('ok');
      });
    }).catch((error: ValidateErrorEntity<any>) => {
      console.log('error', error);
    });
  } else {
    // 编辑
    params = reactive({
      dataSort: formState.dataSort,
      deviceType: formState.deviceType,
      logoUrl: formState.logoUrl,
      id: ids
    })
    formRef.value.validate().then(() => {
      defHttp.put({ url: Api.edit, params }).then((e) => {
        emits('ok');
      });
    }).catch((error: ValidateErrorEntity<any>) => {
      console.log('error', error);
    });
  }

}
function add(e) {
  e.parentId = e.id
  Object.assign(formState, e)
  // 获取分类
  defHttp.get({ url: Api.getDeviceCategoryList }).then((e) => {
    e.forEach(i => {
      i.label = i.deviceCategory
      i.value = i.id
    });
    options.value = e
  });
  ids = ref(e.id)
  // let params = reactive({
  //   dataFlag: '1',
  //   dataSort: formState.dataSort,
  //   deviceType: formState.deviceType,
  //   logoUrl: formState.logoUrl,
  //   id: ids
  // })
  // formRef.value.validate().then(() => {
  //   defHttp.put({ url: Api.edit, params }).then((e) => {
  //     emits('ok');
  //   });
  // }).catch((error: ValidateErrorEntity<FormState>) => {
  //   console.log('error', error);
  // });



}
defineExpose({
  add,
  edit,
  submitForm
});
</script>

<style lang="less" scoped> .antd-modal-form {
   padding: 24px 24px 24px 24px;
 }
</style>
