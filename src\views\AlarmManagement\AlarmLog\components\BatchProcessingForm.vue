<template>
    <a-spin :spinning="confirmLoading">
        <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
            <JFormContainer :disabled="disabled">
                <a-row>
                    <a-col :span="24">
                        <a-form-item label="处理人" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <a-col>{{userStore.getLoginInfo.userInfo.realname}}</a-col>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item label="处理结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
                        <a-radio-group v-model:value="formState.alarmStatuss" @change="statisticst">
                          <a-radio value="4">误报</a-radio>
                          <a-radio value="3">已处置</a-radio>
                        </a-radio-group>
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="处理内容" :labelCol="labelCol" :wrapperCol="wrapperCol" name="disposeResult"
                                     v-bind="validateInfos.disposeResult">
                            <a-textarea placeholder="请输入处理内容" v-model:value="formState.disposeResult"/>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="图片" :labelCol="labelCol" :wrapperCol="wrapperCol">
                            <JImageUpload
                                    :url-params="`?businessCategory=deviceManagement1&businessType=deviceManagement2&businessId=deviceManagement3`"
                                    :fileMax="1" v-model:value="formState.disposePicture"></JImageUpload>
                        </a-form-item>
                    </a-col>

                </a-row>
            </JFormContainer>
        </a-form>
    </a-spin>
</template>

<script lang="ts" setup>
    import {Form} from 'ant-design-vue';
    import {
        defineComponent,
        ref,
        reactive,
        onMounted,
        defineProps,
        defineExpose,
        UnwrapRef,
        nextTick,
        defineEmits
    } from 'vue';
    import {useMessage} from '/@/hooks/web/useMessage';
    import {defHttp} from '/@/utils/http/axios';
    import JImageUpload from '/@/components/Form/src/jeecg/components/JImageUpload.vue';
    import JFormContainer from '@/components/Form/src/jeecg/components/JFormContainer.vue';
    import {useUserStore} from '/@/store/modules/user';

    const userStore = useUserStore();
    const props = defineProps(["disabled"])
    const emits = defineEmits(['success', 'register', 'ok'])

    const useForm = Form.useForm;
    const confirmLoading = ref<boolean>(false);
    const labelCol = ref<any>({xs: {span: 24}, sm: {span: 4}});
    const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 20}});
    const labelCol1 = ref<any>({xs: {span: 24}, sm: {span: 8}});
    const wrapperCol1 = ref<any>({xs: {span: 24}, sm: {span: 16}});
    const {createMessage} = useMessage();
    const Api = reactive({
        add: '/sys/dtAlarmDetail/editAlarmDetailStatus',
    });

    const formState = reactive({
        alarmStatuss: '4',
        disposeResult: '',
        disposePicture: '',
    });
    //表单验证
    const validatorRules = {
        // disposeResult: [{required: true, message: '请输入处理内容!'}],
    };
    const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

    function add() {
        edit({});
    }

    function edit(record) {
        nextTick(() => {
            resetFields();
            Object.assign(formState, record);
        });
    }

    function statisticst(e) {
      formState.alarmStatuss = e.target.value;
    }

    /**
     * 提交数据
     */
    async function submitForm() {
        // await validate();
        confirmLoading.value = true;
        let httpurl = '';
        let method = '';
        //时间格式化
        let model = {
            ids:formState.id,
            disposeResult:formState.disposeResult,
            disposePicture:formState.disposePicture,
            alarmStatus: formState.alarmStatuss,
        };
        httpurl += Api.add;
        method = 'put';
        defHttp.request({url: httpurl, params: model, method: method,}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                createMessage.success(res.message);
                emits('ok');
            } else {
                createMessage.warning(res.message);
            }
        }).finally(() => {
            confirmLoading.value = false;
        });
    }


    defineExpose({
        add,
        edit,
        submitForm
    });
</script>

<style scoped>
    .antd-modal-form {
        padding: 24px 24px 24px 24px;
    }
</style>
