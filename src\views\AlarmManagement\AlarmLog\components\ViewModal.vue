<template>
  <BasicModal
    :title="title"
    :width="width"
    :visible="visible"
    :height="600"
    @ok="handleOk"
    :okText="'确定'"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    :cancelText="cancelText">
    <ViewForm ref="ViewForm1" @ok="submitCallback" :disabled="disableSubmit"/>
    <template #footer>
      <a-button @click="handleCancel">关闭</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
  import {ref, nextTick, defineExpose, defineEmits} from 'vue';
  import {BasicModal} from '/@/components/Modal';
  import {useParamStore} from '/@/store/modules/getParam';
  import ViewForm from './ViewForm.vue';

  const ViewForm1 = ref();

  const paramStore = useParamStore();
  const title = ref<string>('告警详情');
  const cancelText = ref<string>('关闭');
  const width = ref<number>(800);
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const emit = defineEmits(['register', 'ok']);

  function add() {
    visible.value = true;
    nextTick(() => {
      ViewForm1.value.add();
    });
  }

  function edit(record) {
    visible.value = true;
    nextTick(() => {
      ViewForm1.value.edit(record);
    });
  }

  function handleOk() {
    ViewForm1.value.submitForm();
  }

  function submitCallback() {
    handleCancel();
    emit('ok');
  }

  function handleCancel() {
    visible.value = false;
  }

  defineExpose({
    add,
    edit,
    disableSubmit,
  });
</script>

<style scoped>

</style>
