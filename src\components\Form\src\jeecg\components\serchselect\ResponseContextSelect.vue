<template>
    <a-radio-group
            :disabled="disabled"
            :show-arrow="true"
            :size="size"
            @change="onChange"
            :value="arrayValue"
    >
        <a-radio value="已到达现场" style="float:left;">已到达现场</a-radio>
        <a-radio value="已联络" style="float:left;">已联络</a-radio>
        <a-radio value="其他" style="float:left;">其他</a-radio>
        <a-input placeholder="请输入响应内容" v-model:value="arrayValue1" style="float:left;width: 30%;"/>
    </a-radio-group>
</template>
<script lang="ts">
    import {computed, defineComponent, onMounted, ref, nextTick, watch, reactive} from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {propTypes} from '/@/utils/propTypes';
    import {useMessage} from '/@/hooks/web/useMessage';
    import {useUserStore} from '/@/store/modules/user';

    export default defineComponent({
        name: 'FireSizeSelect',
        components: {},
        inheritAttrs: false,
        props: {
            value: propTypes.oneOfType([propTypes.string, propTypes.array]),
            spliter: {
                type: String,
                required: false,
                default: ',',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        emits: ['options-change', 'change', 'input', 'update:value'],
        setup(props, {emit, refs}) {
            const arrayValue = ref<any[]>(!props.value ? [] : props.value);
            const arrayValue1 = ref("");
            arrayValue.value="已到达现场"
            emit('change', arrayValue.value);
            const userStore = useUserStore();
            const Api = reactive<any>({});
            onMounted(() => {
            });

            watch(() => props.value, (val) => {
                    arrayValue.value = val;
                }
            );

            function onChange(selectedValue) {
                emit('change', selectedValue.target.value);
                emit('update:value', selectedValue.target.value);
            }

            return {
                onChange,
                arrayValue,
                arrayValue1,
            };
        },
    });
</script>
<style lang="less" scoped>
    // ::v-deep .ant-select-selection-item {
    //   color: rgba(0, 0, 0, 0.25) !important;
    // }
</style>
