<template>
  <a-spin :spinning="confirmLoading">
    <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="24">
            <a-button type="primary" preIcon="ant-design:search-outlined" @click="handleDetail"
                      style="margin-right: 5px">预案详情
            </a-button>
            <a-button preIcon="ant-design:plus-outlined" type="primary" @click="handleAdd">添加响应内容</a-button>
          </a-col>
          <a-col :span="24">
            <JVxeTable
              ref="tableRef"
              bordered
              row-number
              keep-source
              resizable
              :maxHeight="484"
              style="margin: 5px 0"
              :loading="loading"
              :dataSource="dataSource"
              :columns="columns">
              <template #responseContext="props">
                <span>{{props.row.responseBody}}{{props.row.responseContext}}</span>
              </template>
              <template #responseTime="props">
                <span>{{props.row.responseTime.substring(0,16)}}</span>
              </template>
              <template #action="props">
                <Popconfirm :title="'请确认是否删除？'" @confirm="handleDelete(props.row)">
                  <a>删除</a>
                </Popconfirm>
              </template>
            </JVxeTable>
          </a-col>
          <a-col :span="24">
            <a-form-item label="响应总结" name="responseResult" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea placeholder="请输入响应总结" v-model:value="formState.responseResult" rows="3"/>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
  <PlanDetailsModal ref="PlanDetailsModal1"></PlanDetailsModal>
  <FireResponseListModal ref="FireResponseListModal1" @ok="loadData(formState)"></FireResponseListModal>
</template>

<script lang="ts" setup>
  import {Form, Modal, Image} from 'ant-design-vue';
  import * as echarts from "echarts";
  import {
    defineComponent,
    ref,
    reactive,
    onMounted,
    defineProps,
    defineExpose,
    nextTick,
    defineEmits
  } from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import {useMessage} from '/@/hooks/web/useMessage';
  import {Popconfirm} from 'ant-design-vue';
  import PlanDetailsModal from './PlanDetailsModal.vue';
  import FireResponseListModal from './FireResponseListModal.vue';

  const props = defineProps(["disabled"])
  const emit = defineEmits(['success', 'register', 'ok'])
  const confirmLoading = ref<boolean>(false);
  const validatorRules = {};
  const Api = reactive<any>({
    list: '/sys/dtFireReportResponse/getResponseRecord',
    edit: '/sys/dtFireReportResponse/fireReportResponseFinish',
    delete: '/sys/dtFireReportResponse/delFireReportResponse',
  });
  const labelCol = reactive({
    xs: {span: 24},
    sm: {span: 2},
  })
  const wrapperCol = reactive({
    xs: {span: 24},
    sm: {span: 22},
  })
  const formState = reactive({});
  const dataSource = ref<any>([]);
  const columns = ref([
    {
      title: '响应内容',
      key: 'responseContext',
      minWidth: 150,
      align: "center",
      type: "slot",
      slotName: 'responseContext',
    },
    {
      title: '响应时间',
      key: 'responseTime',
      minWidth: 200,
      align: "center",
      type: "slot",
      slotName: 'responseTime',
    },
    {
      title: '操作',
      type: "slot",
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: 150,
      slotName: 'action',
    },
  ]);
  const PlanDetailsModal1 = ref();
  const FireResponseListModal1 = ref();
  const {createMessage} = useMessage();
  const useForm = Form.useForm;
  const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});
  onMounted(() => {
    //初始化字典选项
  });

  //查看
  function handleDetail() {
    PlanDetailsModal1.value.disableSubmit = false;
    PlanDetailsModal1.value.edit(formState);
  }

  function handleAdd() {
    FireResponseListModal1.value.disableSubmit = false;
    FireResponseListModal1.value.edit(formState);
  }

  function add() {
    edit({});
  }

  function edit(record) {
    nextTick(() => {
      resetFields();
      Object.assign(formState, record);
      loadData(record)
    });
  }

  function loadData(record) {
    let params = {
      reportId: record.id
    };
    defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
      if (res.success && res.result) {
        dataSource.value = res.result.responseModelList ? res.result.responseModelList : [];
      }
    }).finally(() => {
    });
  }

  function handleDelete(record) {
    defHttp.delete({url: Api.delete, data: {id: record.id}}, {
      isTransformResponse: false,
      joinParamsToUrl: true
    }).then((res) => {
      if (res.success) {
        loadData(formState);
        createMessage.success(res.message);
      } else {
        createMessage.warning(res.message);
      }
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    if (dataSource.value.length == 0) {
      createMessage.warning("请添加响应内容！");
      return
    }
    Modal.confirm({
      title: '确认结束？',
      content: '是否确定救援结束？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        confirmLoading.value = true;
        let params = {
          reportId: formState.id,
          responseResult: formState.responseResult,
        };
        defHttp.post({url: Api.edit, params}, {isTransformResponse: false}).then((res) => {
          if (res.success) {
            createMessage.success(res.message);
            emit('ok');
          } else {
            createMessage.warning(res.message);
          }
        }).finally(() => {
          confirmLoading.value = false;
        });
      },
    });

  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  /deep/ .ant-descriptions-header {
    margin-bottom: 4px;
  }

  .charts {
    width: 100%;
    height: 200px;
    border: 1px solid #f0f0f0;
  }

  .video {
    width: 200px;
    height: 100px;
    float: left;
    margin-right: 5px;
    position: relative;
    background: #000;
  }

  .video .videos {
    width: 100%;
    max-height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
</style>
