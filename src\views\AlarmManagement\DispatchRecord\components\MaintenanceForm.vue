<template>
    <a-spin :spinning="confirmLoading">
        <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
            <JFormContainer :disabled="disabled">
                <a-row>
                    <a-col :span="24">
                        <a-form-item label="告警类型" :labelCol="labelCols">
                            <span v-if="formState.alarmType == '1'">疑似火警</span>
                            <span v-if="formState.alarmType == '2'">故障</span>
                            <span v-if="formState.alarmType == '3'">隐患</span>
                            <span v-if="formState.alarmType == '4'">其他</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="设备名称" :labelCol="labelCol">
                            <span>{{formState.deviceName}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="设备位置" :labelCol="labelCol">
                            <span>{{formState.deviceLocation}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="告警内容" :labelCol="labelCol">
                            <span>{{formState.alarmDescription}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="告警时间" :labelCol="labelCol">
                            <span>{{formState.alarmTime}}</span>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="处理结果" ref="repairResult" name="repairResult" :labelCol="labelCol"
                                     v-bind="validateInfos.repairResult">
                            <JSelectMultiple v-model:value="formState.repairResult" placeholder="请选择处理结果"
                                             dictCode="repairResult" mode="default"
                                             triggerChange="false"></JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="处理过程" ref="repairProcess" name="repairProcess" :labelCol="labelCols"
                                     v-bind="validateInfos.repairProcess">
                            <a-textarea placeholder="请输入处理过程" v-model:value="formState.repairProcess"/>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="处理照片" :labelCol="labelCols" ref="repairPicture" name="repairPicture">
                            <JImageUpload
                                    :url-params="`?businessCategory=deviceManagement1&businessType=deviceManagement2&businessId=deviceManagement3`"
                                    :fileMax="1" v-model:value="formState.repairPicture"></JImageUpload>
                        </a-form-item>
                    </a-col>


                </a-row>
            </JFormContainer>
        </a-form>
    </a-spin>
</template>

<script lang="ts" setup>
    import {Form} from 'ant-design-vue';
    import {
        defineComponent,
        ref,
        reactive,
        onMounted,
        defineProps,
        defineExpose,
        UnwrapRef,
        nextTick,
        defineEmits
    } from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {useMessage} from '/@/hooks/web/useMessage';
    import JFormContainer from '@/components/Form/src/jeecg/components/JFormContainer.vue';
    import DateTimeSelect from '/@/components/Form/src/jeecg/components/serchselect/DateTimeSelect.vue';
    import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
    import JImageUpload from '/@/components/Form/src/jeecg/components/JImageUpload.vue';

    const props = defineProps(["disabled"])
    const emits = defineEmits(['success', 'register', 'ok'])
    const {createMessage} = useMessage();
    const confirmLoading = ref<boolean>(false);
    const labelCol = ref<any>({xs: {span: 24}, sm: {span: 6}});
    const labelCols = ref<any>({xs: {span: 24}, sm: {span: 3}});
    const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 16}});

    const formState = reactive({
        alarmType: '',
        deviceName: '',
        deviceLocation: '',
        alarmDescription: '',
        alarmTime: '',
        repairResult: '',
        repairProcess: '',
        repairPicture: '',
    });
    const validatorRules = {
        repairResult: [{required: true, message: '请选择处理结果', trigger: 'change'}],
        repairProcess: [{required: true, message: '请输入处理过程'}],
    };

    const useForm = Form.useForm;
    const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

    const Api = reactive({
        edit: '/sys/dtAlarmOrder/repairAlarmOrder',
    });

    onMounted(() => {
    });

    function add() {
        edit({});

    }

    function edit(record) {
        nextTick(() => {
            resetFields();
            Object.assign(formState, record);
        });
    }

    /**
     * 提交数据
     */
    async function submitForm() {
        await validate();
        confirmLoading.value = true;
        let httpurl = '';
        let method = '';
        //时间格式化
        let model = {
            id: formState.id,
            repairPicture: formState.repairPicture,
            repairProcess: formState.repairProcess,
            repairResult: formState.repairResult,
        };
        httpurl += Api.edit;
        method = 'post';
        defHttp.request({url: httpurl, params: model, method: method,}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                createMessage.success(res.message);
                emits('ok');
            } else {
                createMessage.warning(res.message);
            }
        }).finally(() => {
            confirmLoading.value = false;
        });
    }

    defineExpose({
        add,
        edit,
        submitForm,
    });
</script>

<style scoped>
    .antd-modal-form {
        padding: 24px 24px 24px 24px;
    }
</style>
