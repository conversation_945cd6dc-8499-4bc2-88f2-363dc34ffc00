{"Keys": ["com.unity.services.core.cloud-environment", "com.unity.purchasing.version", "com.unity.purchasing.initializer-assembly-qualified-names", "com.unity.services.core.version", "com.unity.services.core.initializer-assembly-qualified-names", "com.unity.services.core.all-package-names"], "Values": [{"m_Value": "production", "m_IsReadOnly": false}, {"m_Value": "4.8.0", "m_IsReadOnly": true}, {"m_Value": "UnityEngine.Purchasing.Registration.IapCoreInitializeCallback, UnityEngine.Purchasing.Stores, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "1.8.1", "m_IsReadOnly": true}, {"m_Value": "Unity.Services.Core.Registration.CorePackageInitializer, Unity.Services.Core.Registration, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "com.unity.purchasing;com.unity.services.core", "m_IsReadOnly": false}]}