<template>
    <JSelectMultiple @change="getSelect" v-model:value="selectVal" :placeholder="props.placeholderName"
        :options="selectOptions" mode="default" triggerChange="false"></JSelectMultiple>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue'
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import { fdSelectOptions } from '../FilterDatalogger/index.ts'
const props = defineProps(['placeholderName', 'value', 'selectOptions'])
const emit = defineEmits(['returnSelectValue'])
const selectVal = ref<string>(props.value)
const getSelect = (e) => {
    emit('returnSelectValue', fdSelectOptions(props.selectOptions, e))
}
</script>
<style lang="less" scoped></style>
