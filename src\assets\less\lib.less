// @import 'ant-design-vue/dist/antd.min.css';

.dis(@d, @f, @a, @j) {
    display: @d;
    flex-direction: @f;
    align-items: @a;
    justify-content: @j;
}

// 标题字体
@font-face {
    font-family: ttf;
    src: url(../../../public/html/font/优设标题黑.ttf);
}

// 数字字体
@font-face {
    font-family: opp;
    src: url(../../../public/html/font/OPPOSans-H.ttf);
}

// 文本字体
@font-face {
    font-family: sy;
    src: url(../../../public/html/font/SourceHanSansCN-Regular.ttf);
}

@vws: 1920px;
@vhs: 1080px;
@cw: 0;
@ch: 0;
.wh(@w, @h) {
    // @cw : calc(1 - (@w / @vws) * @w);
    // @ch : calc(1 - (@h / @vhs) * @h);
    width: @w;
    height: @h;
}

.fs(@s) {
    font-size: calc(@s + 0.3vw);
}
