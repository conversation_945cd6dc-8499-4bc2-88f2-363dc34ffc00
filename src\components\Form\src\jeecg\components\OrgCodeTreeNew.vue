<template>
  <TreeSelect :class="prefixCls" :value="treeValue" :treeData="treeData" allowClear labelInValue treeDefaultExpandAll
    :replaceFields="{ children: 'children', title: 'departName', key: 'orgCode', value: 'orgCode' }"
    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }" style="width: 100%" v-bind="attrs" @change="onChange">
  </TreeSelect>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { propTypes } from '/@/utils/propTypes';
import { useAttrs } from '/@/hooks/core/useAttrs';
import { useDesign } from '/@/hooks/web/useDesign';
import { TreeSelect } from 'ant-design-vue';

enum Api {
  view = '/sys/sysDepart/queryMyDepartTreeSync',
  root = '/sys/sysDepart/queryMyDeptTreeList',
  children = '/sys/sysDepart/queryMyDepartTreeSync',
}

const { prefixCls } = useDesign('j-tree-dict');
const props = defineProps({
  // v-model:value
  value: propTypes.string.def(''),
  field: propTypes.string.def('id'),
  parentCode: propTypes.string.def(''),
  async: propTypes.bool.def(false),
});
const attrs = useAttrs();
const emit = defineEmits(['change', 'update:value']);

const treeData = ref<any>([]);
const treeValue = ref<any>(null);

watch(
  () => props.value,
  () => loadViewInfo(),
  { deep: true, immediate: true }
);
watch(
  () => props.parentCode,
  () => loadRoot(),
  { deep: true, immediate: true }
);

async function loadViewInfo() {
  if (!props.value || props.value == '0') {
    treeValue.value = { value: null, label: null };
  } else {
    let params = { field: props.field, val: props.value };
    let result = await defHttp.get({ url: Api.view, params });
    console.log(props);

    treeValue.value = {
      value: props.value,
      label: result.name,
    };
  }
}

async function loadRoot() {
  let params = {
    // async: props.async,
    // pcode: props.parentCode,
  };
  console.log();

  let result = await defHttp.get({ url: Api.root, params });
  console.log(result);

  // result.forEach(e => {
  //   e.value = e.orgCode
  //   e.key = e.orgCode

  // })
  treeData.value = [...result];
  // handleTreeNodeValue(result);
}

// function handleTreeNodeValue(result) {
//   let storeField = props.field == 'code' ? 'code' : 'key';
//   for (let i of result) {
//     i.value = i[storeField];
//     i.isLeaf = i.leaf;
//     if (i.children && i.children.length > 0) {
//       handleTreeNodeValue(i.children);
//     }
//   }
// }

function onChange(value) {
  console.log(value);

  // if (!value) {
  //   emitValue('', '');
  // } else {
  //   treeData.value.forEach(e => {
  //     if (e.id == value.value) {
  //       emitValue(value.value, e.orgCode);
  //     } else {
  //       e.children.forEach(r => {
  //         if (r.id == value.value) {
  //           emitValue(value.value, e.orgCode);
  //         }
  //       })
  //     }





  // switch (e.id) {
  //   case value.value:
  //     console.log('case id');
  //     emitValue(value.value, e.orgCode);
  //     break;
  //   default:
  //     console.log('case default', value.value, e.orgCode);
  //     emitValue(value.value, e.orgCode);
  //     break;
  // }
  //   })
  // }
  // if (!value) {
  //   emitValue('');
  // } else {
  //   emitValue(value.value);
  // }
  console.log(value);

  treeValue.value = value;
}

function emitValue(value, o) {
  emit('change', value, o);
  emit('update:value', value);
}
</script>

<style lang="less">
//noinspection LessUnresolvedVariable
@prefix-cls: ~'@{namespace}-j-tree-dict';

.@{prefix-cls} {}
</style>
