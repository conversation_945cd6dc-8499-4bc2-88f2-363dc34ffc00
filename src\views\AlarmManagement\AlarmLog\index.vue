<!-- 告警记录 AlarmLog -->
<template>
  <a-card :bordered="false">
    <!--自定义查询区域-->
    <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
      <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="6">
            <a-form-item label="单位名称">
              <a-input placeholder="请输入单位名称" v-model:value="queryParam.unitName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item label="设备名称">
              <a-input placeholder="请输入设备名称" v-model:value="queryParam.deviceName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item label="告警类型">
              <JSelectMultiple :placeholder="'请选择告警类型'"
                               v-model:value="queryParam.alarmType"
                               dictCode="alarmType" mode="default" :triggerChange="false">
              </JSelectMultiple>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item label="告警状态">
              <JSelectMultiple :placeholder="'请选择告警状态'"
                               v-model:value="queryParam.alarmStatus"
                               dictCode="alarmStatus" mode="default" :triggerChange="false">
              </JSelectMultiple>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item label="开始时间">
              <DateSelect v-model:value="queryParam.startDate" placeholder="请选择开始时间"
                          @change="onStartDateSelect"></DateSelect>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item label="结束时间">
              <DateSelect v-model:value="queryParam.endDate" placeholder="请选择结束时间"
                          @change="onEndDateSelect"></DateSelect>
            </a-form-item>
          </a-col>
          <a-col :lg="12">
            <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询
            </a-button>
            <a-button type="primary" @click="searchBatchProcessing"
                      v-auth="'AlarmManagement:AlarmLog:components:BatchProcessingAllModal'"
                      style="margin-left: 8px">批量处理
            </a-button>
            <a-button type="primary" @click="handleAdd" style="margin-left: 8px" v-if="hasPermission('AlarmManagement:AlarmLog:components:add')">手动上报
            </a-button>
            <!--<a-button type="primary" preIcon="ant-design:export-outlined" @click="onExport" style="margin-left: 8px">导出-->
            <!--</a-button>-->
            <a-button preIcon="ant-design:sync-outlined" @click="searchReset"
                      style="margin-left: 8px">重置
            </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 操作按钮区域 -->
    <div class="table-operator">
    </div>
    <!-- table区域-begin -->
    <JVxeTable
      ref="tableRef"
      bordered
      row-number
      row-selection
      keep-source
      resizable
      :maxHeight="484"
      :loading="loading"
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      style="margin-top: 8px"
      @selectRowChange="handleSelectRowChange"
      @pageChange="handlePageChange">
      <template #alarmType="props">
        <!--1疑似火警 2故障 3隐患 4其他-->
        <span v-if="props.row.alarmType == '1'">疑似火警</span>
        <span v-if="props.row.alarmType == '2'">故障</span>
        <span v-if="props.row.alarmType == '3'">隐患</span>
        <span v-if="props.row.alarmType == '4'">其他</span>
      </template>
      <template #alarmLevel="props">
        <span v-if="props.row.alarmLevel == '1'">一级告警</span>
        <span v-if="props.row.alarmLevel == '2'">二级告警</span>
        <span v-if="props.row.alarmLevel == '3'">三级告警</span>
      </template>
      <template #overdueFlag="props">
        <span v-if="props.row.overdueFlag == '0'">否</span>
        <span v-if="props.row.overdueFlag == '1'">是</span>
      </template>
      <template #alarmStatus="props">
        <!--1待处理 2已派单 3已处理 4关闭-->
        <span v-if="props.row.alarmStatus == '1'">待处理</span>
        <span v-if="props.row.alarmStatus == '2'">已派单</span>
        <span v-if="props.row.alarmStatus == '3'">已处理</span>
        <span v-if="props.row.alarmStatus == '4'">关闭</span>
        <span v-if="props.row.alarmStatus == '5'">已上报</span>
      </template>

      <template #action="props">
        <a v-if="hasPermission('AlarmManagement:AlarmLog:components:DispatchModal')">
          <a @click="handleDetail(props.row)"
             v-if="props.row.alarmStatus == '1'&&props.row.alarmSource !== '3'">派单</a>
          <a-divider type="vertical"
                     v-if="props.row.alarmStatus == '1'&&props.row.alarmSource !== '3'"/>
        </a>
        <a v-if="hasPermission('AlarmManagement:AlarmLog:components:SuperviseAndHandleModal')">
          <a @click="handleSAH(props.row)"
             v-if="props.row.alarmStatus == '1'&&props.row.alarmSource !== '3'">督办</a>
          <a-divider type="vertical"
                     v-if="props.row.alarmStatus == '1'&&props.row.alarmSource !== '3'"/>
        </a>
        <a @click="handleView(props.row)" v-if="props.row.alarmStatus == '3'">查看</a>
        <a v-if="hasPermission('AlarmManagement:AlarmLog:components:BatchProcessingModal')">
          <a @click="searchBatchProcessinglist(props.row)"
             v-if="props.row.alarmStatus == '1'">处理</a>
          <a-divider type="vertical" v-if="props.row.alarmStatus == '1'"/>
        </a>
        <a @click="handleReports(props.row)" v-if="props.row.alarmStatus == '1'">上报</a>
        <a-divider type="vertical" v-if="props.row.alarmStatus == '1'"/>
        <Popconfirm title="确认关闭此告警？" @confirm="handleDelete(props.row.id)"
                    v-if="props.row.alarmStatus == '1'">
          <a>关闭</a>
        </Popconfirm>
      </template>
    </JVxeTable>
  </a-card>
  <!--手动上报-->
  <AlarmModal ref="AlarmModal1" @ok="handleSuccess"/>
  <!--处理-->
  <BatchProcessingModal ref="BatchProcessingModal1" @ok="loadData"/>
  <!--批量处理-->
  <BatchProcessingAllModal ref="BatchProcessingAllModal1" @ok="loadData"/>
  <!--派单-->
  <DispatchModal ref="DispatchModal1" @ok="loadData"/>
  <!--督办-->
  <SuperviseAndHandleModal ref="SuperviseAndHandleModal1" @ok="loadData"/>
  <!--查看-->
  <ViewModal ref="ViewModal1"/>
  <!--上报-->
  <ReportsModal ref="ReportsModal1" @ok="loadData"/>
</template>

<script lang="ts" setup>
import {onMounted, ref, reactive} from 'vue';
import {defHttp} from '/@/utils/http/axios';
import {filterObj} from '/@/utils/common/compUtils';
import {useMessage} from '/@/hooks/web/useMessage';
import {initDictOptions} from '/@/utils/dict';
import {Popconfirm} from 'ant-design-vue';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import DateSelect from '/@/components/Form/src/jeecg/components/serchselect/DateSelect.vue';
import AlarmModal from './components/AlarmModal.vue';
import BatchProcessingModal from './components/BatchProcessingModal.vue';
import DispatchModal from './components/DispatchModal.vue';
import SuperviseAndHandleModal from './components/SuperviseAndHandleModal.vue';
import BatchProcessingAllModal from './components/BatchProcessingAllModal.vue';
import ViewModal from './components/ViewModal.vue';
import ReportsModal from './components/ReportsModal.vue';
import {useUserStore} from '/@/store/modules/user';
import {usePermission} from '/@/hooks/web/usePermission';

const {hasPermission} = usePermission();

const userStore = useUserStore();

const AlarmModal1 = ref();
const BatchProcessingModal1 = ref();
const BatchProcessingAllModal1 = ref();
const DispatchModal1 = ref();
const SuperviseAndHandleModal1 = ref();
const ViewModal1 = ref();
const ReportsModal1 = ref();


const labelCol = reactive({
  xs: {span: 24},
  sm: {span: 8},
})
const wrapperCol = reactive({
  xs: {span: 24},
  sm: {span: 16},
})
const queryParam = ref<any>({
  startDate: '',
  endDate: '',
});
const loading = ref<boolean>(false);
const columns = ref([
  {
    title: '告警时间',
    key: 'alarmTime',
    minWidth: 180,
    align: "center",
  },
  {
    title: '告警内容',
    key: 'alarmDescription',
    minWidth: 200,
    align: "center",
  },
  {
    title: '类型',
    key: 'deviceTypeName',
    minWidth: 120,
    align: "center",
  },
  {
    title: '告警类型',
    key: 'alarmType',
    minWidth: 120,
    align: "center",
    type: 'slot',
    slotName: 'alarmType',
  },
  {
    title: '告警级别',
    key: 'alarmLevel',
    minWidth: 120,
    align: "center",
    type: 'slot',
    slotName: 'alarmLevel',
  },
  {
    title: '设备/建筑物名称',
    key: 'deviceName',
    minWidth: 180,
    align: "center",
  },
  {
    title: '设备编码',
    key: 'deviceCode',
    minWidth: 150,
    align: "center",
  },
  {
    title: '地址',
    key: 'deviceLocation',
    minWidth: 180,
    align: "center",
  },
  {
    title: '单位名称',
    key: 'unitName',
    minWidth: 150,
    align: "center",
  },
  {
    title: '逾期级别',
    key: 'overdueLevel',
    minWidth: 150,
    align: "center",
  },
  {
    title: '状态',
    key: 'alarmStatus',
    minWidth: 150,
    align: "center",
    type: 'slot',
    slotName: 'alarmStatus',
  },
  {
    title: '处置内容',
    key: 'disposeResult',
    minWidth: 150,
    align: "center",
  },
  {
    title: '处置人员',
    key: 'disposeRealname',
    minWidth: 150,
    align: "center",
  },
  {
    title: '处置时间',
    key: 'disposeTime',
    minWidth: 150,
    align: "center",
  },
  {
    title: '操作',
    type: "slot",
    key: 'action',
    align: 'center',
    fixed: 'right',
    width: 250,
    slotName: 'action',
  },
]);
const Api = reactive<any>({
  list: '/sys/dtAlarmDetail/pageList',
  editAlarmDetailStatus: '/sys/dtAlarmDetail/editAlarmDetailStatus',//编辑告警详情状态
});
const {createMessage} = useMessage();
const dataSource = ref<any>([]);
const pagination = ref<any>({
  current: 1,
  pageSize: 10,
  pageSizeOptions: ['10', '20', '30'],
  showTotal: (total, range) => {
    return range[0] + '-' + range[1] + ' 共' + total + '条';
  },
  showQuickJumper: true,
  showSizeChanger: true,
  total: 0,
});
// 选择的行
const selectedRowKeys = ref<any>([]);
const selectionRows = ref<any>([]);
const iSorter = ref<any>({column: 'createTime', order: 'desc'});
const iFilters = ref<any>({});
const tableRef = ref();

/**
 * 获取查询参数
 */
function getQueryParams() {
  let params = Object.assign(queryParam.value, iSorter.value, iFilters.value);
  params.pageNo = pagination.value.current;
  params.pageSize = pagination.value.pageSize;
  return filterObj(params);
}

// 开始时间
function onStartDateSelect(e) {
}

// 结束时间
function onEndDateSelect(e) {
}


// 当分页参数变化时触发的事件
function handlePageChange(event) {
  // 重新赋值
  pagination.value.current = event.current;
  pagination.value.pageSize = event.pageSize;
  // 查询数据
  loadData();
}

/**
 * 复选框选中事件
 * @param rowKeys
 * @param rows
 */
function handleSelectRowChange(event) {
  selectedRowKeys.value = event.selectedRowIds;
  selectionRows.value = event.selectedRows;
}

function loadData(arg) {
  if (arg === 1) {
    pagination.value.current = 1;
  }
  loading.value = true;
  let params = getQueryParams();
  defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      for (let i in res.result.records) {
        if (res.result.records[i].deviceType == '建筑物') {
          res.result.records[i].deviceTypeName = res.result.records[i].deviceType;
        }
      }
      dataSource.value = res.result.records;
      if (res.result && res.result.total) {
        pagination.value.total = res.result.total;
      } else {
        pagination.value.total = 0;
      }
    }
  }).finally(() => {
    selectedRowKeys.value = [];
    selectionRows.value = [];
    tableRef.value.clearSelection();
    loading.value = false;
  });
}

//查询
function searchQuery() {
  selectedRowKeys.value = [];
  selectionRows.value = [];
  loadData(1);
}

//批量处理
function searchBatchProcessing() {
  if (selectedRowKeys.value.length > 0) {
    BatchProcessingAllModal1.value.title = "批量处理";
    BatchProcessingAllModal1.value.disableSubmit = false;
    let params = {
      ids: selectedRowKeys.value.toString()
    }
    BatchProcessingAllModal1.value.edit(params);
  } else {
    createMessage.warning("请先至少选择一个告警！");
  }

}

//处理
function searchBatchProcessinglist(record) {
  BatchProcessingModal1.value.title = "处理";
  BatchProcessingModal1.value.disableSubmit = false;
  BatchProcessingModal1.value.edit(record);
}

//导出
function onExport() {

}

//重置
function searchReset() {
  queryParam.value = {};
  queryParam.value.unitId = userStore.getLoginInfo.unitInfo.id;
  loadData(1);
}
function handleAdd() {
  AlarmModal1.value.disableSubmit = false;
  AlarmModal1.value.add();
}
//派单
function handleDetail(e) {
  DispatchModal1.value.disableSubmit = false;
  DispatchModal1.value.edit(e);
}

//督办
function handleSAH(e) {
  SuperviseAndHandleModal1.value.disableSubmit = false;
  SuperviseAndHandleModal1.value.edit(e);
}

//查看
function handleView(e) {
  ViewModal1.value.disableSubmit = false;
  ViewModal1.value.edit(e);
}

//上报
function handleReports(e) {
  ReportsModal1.value.disableSubmit = false;
  ReportsModal1.value.edit(e);
}

//关闭
function handleDelete(id) {
  let params = {
    ids: id,
    alarmStatus: "4",
  };
  defHttp.put({
    url: Api.editAlarmDetailStatus,
    params
  }, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      createMessage.success(res.message);
    } else {
      createMessage.warning(res.message);
    }
  }).finally(() => {
    loadData()
  });
}

async function initDictConfig() {
  queryParam.value.unitId = userStore.getLoginInfo.unitInfo.id;
}

function datePickerFormat(aDate, timeAppend) {
  aDate = aDate != undefined ? aDate : new Date()
  let year = aDate.getFullYear()
  let month = aDate.getMonth() + 1
  month = month < 10 ? ('0' + month) : month
  let day = aDate.getDate()
  day = day < 10 ? ('0' + day) : day
  return year + '-' + month + '-' + day
}

function handleSuccess() {
  loadData(1);
}

onMounted(() => {
  //初始化字典选项
  initDictConfig();
  //初始加载页面
  loadData();
});


</script>
<style lang="less" scoped>
.jeecg-basic-table-form-container {
  padding: 0px;

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 0;
    white-space: nowrap;
  }

  .ant-form-item {
    margin-bottom: 8px !important;
  }
}

.ant-divider-vertical {
  /*border-left: 1px solid #ffffff;*/
}
</style>
