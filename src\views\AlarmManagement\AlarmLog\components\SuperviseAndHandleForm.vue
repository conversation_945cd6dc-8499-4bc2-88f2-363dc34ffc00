<template>
  <a-spin :spinning="confirmLoading">
      <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
        <JFormContainer :disabled="disabled">
          <a-row>
            <a-col :span="12">
              <a-form-item label="告警类型"  :wrapperCol="wrapperCol" :labelCol="labelCol">
                <!--1疑似火警 2故障 3隐患 4其他-->
                <span v-if="formState.alarmType == '1'">疑似火警</span>
                <span v-if="formState.alarmType == '2'">故障</span>
                <span v-if="formState.alarmType == '3'">隐患</span>
                <span v-if="formState.alarmType == '4'">其他</span>
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="设备类型"  :wrapperCol="wrapperCol" :labelCol="labelCol">
                <span>{{ formState.deviceTypeName }}</span>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备名称"  :wrapperCol="wrapperCol" :labelCol="labelCol">
                <span>{{ formState.deviceName }}</span>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备位置"  :wrapperCol="wrapperCol" :labelCol="labelCol">
                <span>{{ formState.deviceLocation }}</span>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="状态"  :wrapperCol="wrapperCol" :labelCol="labelCol">
                <!--1待处理 2已派单 3已处理 4关闭-->
                <span v-if="formState.alarmStatus == '1'">待处理</span>
                <span v-if="formState.alarmStatus == '2'">已派单</span>
                <span v-if="formState.alarmStatus == '3'">已处理</span>
                <span v-if="formState.alarmStatus == '4'">关闭</span>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="逾期状态"  :wrapperCol="wrapperCol" :labelCol="labelCol">
                <span v-if="formState.overdueFlag == '0'">否</span>
                <span v-if="formState.overdueFlag == '1'">是</span>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="告警内容"  :wrapperCol="wrapperCol" :labelCol="labelCol">
                <span>{{ formState.alarmDescription }}</span>
              </a-form-item>
            </a-col>
            <a-col :span="12">
            </a-col>
            <a-col :span="24">
              <a-form-item label="督办描述" name="superviseDesc" :wrapperCol="wrapperCol1" :labelCol="labelCol1"
                           v-bind="validateInfos.superviseDesc">
                <a-input v-model:value="formState.superviseDesc" placeholder="请输入督办描述"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="督办截止时间" name="superviseEndTime" :wrapperCol="wrapperCol" :labelCol="labelCol" v-bind="validateInfos.superviseEndTime">
                <DateTimeSelect v-model:value="formState.superviseEndTime" placeholder="请选择督办截止时间"
                                @change="onStartDateTimeSelect"></DateTimeSelect>
              </a-form-item>
            </a-col>
          </a-row>
        </JFormContainer>

      </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import {Form} from 'ant-design-vue';
  import {defineComponent, ref, reactive, onMounted, defineProps, defineExpose, UnwrapRef, nextTick, defineEmits} from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import {useMessage} from '/@/hooks/web/useMessage';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import JFormContainer from '@/components/Form/src/jeecg/components/JFormContainer.vue';
  import DateTimeSelect from '/@/components/Form/src/jeecg/components/serchselect/DateTimeSelect.vue';

  const confirmLoading = ref<boolean>(false);
  const props = defineProps(["disabled"])
  const emits = defineEmits(['success', 'register', 'ok'])

  const labelCol = ref<any>({xs: {span: 24}, sm: {span: 8}});
  const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 16}});
  const labelCol1 = ref<any>({xs: {span: 24}, sm: {span: 4}});
  const wrapperCol1 = ref<any>({xs: {span: 24}, sm: {span: 20}});
  const Api = reactive({
    edit: '/sys/dtAlarmSupervise/addAlarmSupervise',
  });

  const formState = reactive({
    alarmType: '',
    deviceTypeName: '',
    deviceName: '',
    deviceLocation: '',
    alarmStatus: '',
    overdueFlag: '',
    alarmDescription: '',
    superviseDesc: '请尽快处理',
    superviseEndTime: '',
  });
  const validatorRules = {
    superviseDesc: [{required: true, message: '请输入督办描述', trigger: 'change'}],
    superviseEndTime: [{required: true, message: '请选择督办截止时间',  trigger: 'change'}],
  };
  const useForm = Form.useForm;
  const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});
  const {createMessage} = useMessage();
  // 开始时间
  function onTimeSelect(e) {
  }
  function datetimePickerFormat(aDate, timeAppend) {
    aDate = aDate != undefined ? aDate : new Date()
    let year = aDate.getFullYear()
    let month = aDate.getMonth() + 1
    month = month < 10 ? ('0' + month) : month
    let day = aDate.getDate()
    day = day < 10 ? ('0' + day) : day
    if (!timeAppend) {
      let hh = aDate.getHours() < 10
        ? '0' + aDate.getHours()
        : aDate.getHours()
      let mm = aDate.getMinutes() < 10
        ? '0' + aDate.getMinutes()
        : aDate.getMinutes()
      let ss = aDate.getSeconds() < 10
        ? '0' + aDate.getSeconds()
        : aDate.getSeconds()
      timeAppend = hh + ':' + mm + ':' + ss
    }
    return year + '-' + month + '-' + day + ' ' + timeAppend
  }

  onMounted(() => {
    //初始化字典选项
  });

  function add() {
    edit({});
  }

  function edit(record) {
    nextTick(() => {
      resetFields();
      Object.assign(formState, record);
      formState.superviseEndTime = datetimePickerFormat(undefined, "");
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    await validate();
    confirmLoading.value = true;
    let httpurl = '';
    let method = '';
    //时间格式化
    let model = {
      alarmId:formState.id,
      superviseDesc:formState.superviseDesc,
      superviseEndTime:formState.superviseEndTime,
    };
    httpurl += Api.edit;
    method = 'post';
    defHttp.request({url: httpurl, params: model, method: method,}, {isTransformResponse: false}).then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        emits('ok');
      } else {
        createMessage.warning(res.message);
      }
    }).finally(() => {
      confirmLoading.value = false;
    });

  }

  defineExpose({
    add,
    edit,
    submitForm,
  });

</script>

<style scoped>
  .antd-modal-form {
    padding: 24px 24px 24px 24px;
  }
</style>
