<template>
    <a-radio-group
            :disabled="disabled"
            :show-arrow="true"
            :size="size"
            @change="onChange"
            :value="arrayValue"
    >
        <a-radio :value="'小火'">小火</a-radio>
        <a-radio :value="'中火'">中火</a-radio>
        <a-radio :value="'大火'">大火</a-radio>
    </a-radio-group>
</template>
<script lang="ts">
    import {computed, defineComponent, onMounted, ref, nextTick, watch, reactive} from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {propTypes} from '/@/utils/propTypes';
    import {useMessage} from '/@/hooks/web/useMessage';
    import {useUserStore} from '/@/store/modules/user';

    export default defineComponent({
        name: 'FireSizeSelect',
        components: {},
        inheritAttrs: false,
        props: {
            value: propTypes.oneOfType([propTypes.string, propTypes.array]),
            spliter: {
                type: String,
                required: false,
                default: ',',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        emits: ['options-change', 'change', 'input', 'update:value'],
        setup(props, {emit, refs}) {
            const arrayValue = ref<any[]>(!props.value ? [] : props.value);
            const userStore = useUserStore();
            const Api = reactive<any>({});
            onMounted(() => {
            });

            watch(() => props.value, (val) => {
                    arrayValue.value = val;
                }
            );

            function onChange(selectedValue) {
                emit('change', selectedValue.target.value);
                emit('update:value', selectedValue.target.value);
            }

            return {
                onChange,
                arrayValue,
            };
        },
    });
</script>
<style lang="less" scoped>
    // ::v-deep .ant-select-selection-item {
    //   color: rgba(0, 0, 0, 0.25) !important;
    // }
</style>
