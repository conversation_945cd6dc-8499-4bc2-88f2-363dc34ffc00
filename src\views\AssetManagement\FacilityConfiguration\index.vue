<!-- 采集参数配置 FacilityConfiguration -->
<template>
    <a-card :bordered="false">
        <!--自定义查询区域-->
        <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
            <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
                <a-row :gutter="24">
                    <a-col :span="6">
                        <a-form-item label="设备分类" name="deviceCategory" :labelCol="labelCol">
                            <JSelectMultiple @change="getSelectCategory" v-model:value="formState.deviceCategory"
                                placeholder="请选择设备分类" :options="deviceCategoryOptions" mode="default"
                                :triggerChange="false">
                            </JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="设备类型" name="deviceType" :labelCol="labelCol">
                            <JSelectMultiple v-model:value="formState.deviceType" placeholder="请选择设备类型"
                                :options="deviceTypeOptions" mode="default" :triggerChange="false"></JSelectMultiple>
                        </a-form-item>
                    </a-col>

                    <a-col :lg="6">
                        <a-form-item label="参数编码">
                            <a-input placeholder="请输入参数编码" v-model:value="formState.paramCode"></a-input>
                        </a-form-item>
                    </a-col>
                    <a-col :lg="6">
                        <a-form-item label="参数分类">
                            <JSelectMultiple v-model:value="formState.paramType" placeholder="请选择参数分类"
                                :options="paramTypeOptions" mode="default" triggerChange="false"></JSelectMultiple>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
            <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery"
                style="margin-right: 5px">查询</a-button>
            <a-button @click="handleAdd" type="primary" preIcon="ant-design:plus" style="margin-right: 5px">新增</a-button>
        </div>
        <!-- table区域-begin -->
        <JVxeTable ref="tableRef" bordered row-number keep-source resizable :maxHeight="484" :loading="loading"
            :dataSource="dataSource" :columns="columns" :pagination="pagination" style="margin-top: 8px" row-selection
            @pageChange="handlePageChange">
            <template #action="props">
                <a @click="handleEdit(props.row)">编辑</a>
                <a-divider type="vertical" />
                <Popconfirm title="请确认是否删除？" @confirm="handleDel(props.row)">
                    <a>删除</a>
                </Popconfirm>
            </template>
        </JVxeTable>
    </a-card>
    <DeptModal ref="DeptModal1" @ok="handleSuccess" />
</template>
<!--巡检项次-->
<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue';
import { Popconfirm } from 'ant-design-vue';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import { loadCategoryData } from '/@/api/common/api';
import { initDictOptions } from '/@/utils/dict';
import DeptModal from './components/DeptModal.vue';
import { defHttp } from '/@/utils/http/axios';
import { deviceCategoryList, getDeviceTypeListById, getDictItemList } from '@/hooks/params/param'
import {filterObj, getFileAccessHttpUrl} from '/@/utils/common/compUtils';

const deviceCategoryOptions = ref<any>([])
const deviceTypeOptions = ref<any>([])
const paramTypeOptions = ref<any>([])

const iSorter = ref<any>({column: 'createTime', order: 'desc'});
const iFilters = ref<any>({});

onMounted(() => {
    dictOptions.value['kaiguan'] = [
        { text: '是', value: '1' },
        { text: '否', value: '2' },
    ];
    // 设备分类
    deviceCategoryList.then(e => deviceCategoryOptions.value = e)

    getDictItemList('paramType').then(e => paramTypeOptions.value = e)

    //初始加载页面
    loadData();
    //初始化字典选项
    initDictConfig();
});

function getSelectCategory(e) {
    formState.deviceType = ''
    deviceTypeOptions.length = 0
    getDeviceTypeListById(e).then(e => deviceTypeOptions.value = e)
}
const DeptModal1 = ref();
function handleAdd() {
    DeptModal1.value.disableSubmit = false;
    DeptModal1.value.add();
}
function handleEdit(record) {
    DeptModal1.value.disableSubmit = false;
    DeptModal1.value.edit(record);
}
function handleDel(e) {
    defHttp.get({ url: Api.delete, params: { id: e.id } }).then((e) => {
        loadData();
    });
}

//-----自定义查询----begin--------
const labelCol = reactive({
    xs: { span: 24 },
    sm: { span: 10 },
})
const wrapperCol = reactive({
    xs: { span: 24 },
    sm: { span: 14 },
})
const formState = ref<any>({
    deviceCategory: '',
    deviceType: '',
    paramCode: '',
    paramType: '',
    pageNo: '1',
    pageSize: '10',
});
const loading = ref<boolean>(false);
const dictOptions = ref<any>([]);

//表头
const columns = ref([
    {
        title: '设备分类',
        key: 'deviceCategoryName',
        align: "center",
        minWidth: 150,
    },
    {
        title: '设备类型',
        key: 'deviceTypeName',
        minWidth: 100,
        align: "center",
    },
    {
        title: '参数编码',
        key: 'paramCode',
        minWidth: 150,
        align: "center",
    },
    {
        title: '编码别名',
        key: 'aliasCode',
        minWidth: 150,
        align: "center",
    },
    {
        title: '参数名称',
        key: 'paramName',
        minWidth: 120,
        align: "center",
    }, {
        title: '名称别名',
        key: 'aliasName',
        minWidth: 120,
        align: "center",
    },
    {
        title: '参数分类',
        key: 'paramType_dictText',
        minWidth: 120,
        align: "center",
    },
    {
        title: '参数描述',
        key: 'paramDesc',
        minWidth: 150,
        align: "center",
    },
    {
        title: '单位',
        key: 'unit',
        minWidth: 100,
        align: "center",
    }, {
        title: '公式',
        key: 'formula',
        minWidth: 150,
        align: "center",
    }, {
        title: '序值',
        key: 'paramSort',
        minWidth: 150,
        align: "center",
    }, {
        title: '操作',
        type: "slot",
        key: 'action',
        align: 'center',
        fixed: 'right',
        width: 150,
        slotName: 'action',
    },
]);
const dataSource = ref<any>([]);
const Api = reactive<any>({
    list: '/sys/dtParamTemplate/pageList',
    delete: '/sys/dtParamTemplate/delParamTemplate',
});

const pagination = ref<any>({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30'],
    showTotal: (total, range) => {
        return range[0] + '-' + range[1] + ' 共' + total + '条';
    },
    showQuickJumper: true,
    showSizeChanger: true,
    total: dataSource.value.length,
});

const selectedRowKeys = ref<any>([]);
const selectionRows = ref<any>([]);
// 当分页参数变化时触发的事件
function handlePageChange(event) {
    // 重新赋值
    pagination.value.current = event.current;
    pagination.value.pageSize = event.pageSize;
    // formState.value.pageNo = event.current;
    // formState.value.pageSize = event.pageSize;
    // 查询数据
    loadData();
}

/**
 * 获取查询参数
 */
function getQueryParams() {
  let params = Object.assign(formState.value, iSorter.value, iFilters.value);
  params.pageNo = pagination.value.current;
  params.pageSize = pagination.value.pageSize;
  return filterObj(params);
}

function loadData(arg) {
    if (arg === 1) {
        pagination.value.current = 1;
    }
    loading.value = true;
    // let params = formState.value
    let params = getQueryParams();
    defHttp.get({ url: Api.list, params }, { isTransformResponse: false }).then((res) => {
        if (res.success) {
            dataSource.value = res.result.records;
            if (res.result && res.result.total) {
                pagination.value.total = res.result.total;
            } else {
                pagination.value.total = 0;
            }
        }
    })
        .finally(() => {
            loading.value = false;
        });
}
//查询
function searchQuery() {
    loadData(1);
    selectedRowKeys.value = [];
    selectionRows.value = [];
}

/**
 * 初始化字典选项
 */
async function initDictConfig() {
}
/**
 * 保存表单后回调事件
 */
function handleSuccess() {
    selectedRowKeys.value = [];
    selectionRows.value = [];
    loadData(1);
}


</script>
<style lang="less" scoped></style>
