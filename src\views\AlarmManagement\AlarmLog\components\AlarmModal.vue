<template>
  <BasicModal :title="title" :width="width" :visible="visible" :height="600" @ok="handleOk" :okText="'保存'"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" @cancel="handleCancel" :cancelText="cancelText">
    <AlarmForm ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></AlarmForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, nextTick, defineExpose, defineEmits } from 'vue';
import AlarmForm from './AlarmForm.vue';
import { BasicModal } from '/@/components/Modal';
import { useParamStore } from '/@/store/modules/getParam';
const paramStore = useParamStore();
const title = ref<string>('新增');
const cancelText = ref<string>('关闭');
const width = ref<number>(600);
const visible = ref<boolean>(false);
const disableSubmit = ref<boolean>(false);
const realForm = ref();
const emit = defineEmits(['register', 'ok']);

function add() {
  visible.value = true;
  nextTick(() => {
    realForm.value.add();
  });
}

function edit(record) {
  visible.value = true;
  nextTick(() => {
    realForm.value.edit(record);
  });
}

function handleOk() {
  realForm.value.submitForm()
}

function submitCallback() {
  handleCancel();
  emit('ok');
}

function handleCancel() {
  visible.value = false;
}

defineExpose({
  add,
  edit,
  disableSubmit,
});
</script>

<style lang="less" scoped></style>
