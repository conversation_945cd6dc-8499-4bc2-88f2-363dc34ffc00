<template>
  <a-spin :spinning="confirmLoading">
    <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="12">
            <a-form-item label="告警类型"  :labelCol="labelCol">
              <span v-if="formState.alarmType == '1'">疑似火警</span>
              <span v-if="formState.alarmType == '2'">故障</span>
              <span v-if="formState.alarmType == '3'">隐患</span>
              <span v-if="formState.alarmType == '4'">其他</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备名称"  :labelCol="labelCol">
              <span>{{ formState.deviceName }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="设备位置" :labelCol="labelCols">
              <span>{{ formState.deviceLocation }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="告警内容" :labelCol="labelCol">
              <span>{{ formState.alarmDescription }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="督办内容" :labelCol="labelCol">
              <span>{{ formState.superviseDesc }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="汇报内容" :labelCol="labelCols">
              <span>{{ formState.reportContent }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="汇报时间" :labelCol="labelCols">
              <span>{{ formState.reportTime }}</span>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import {Form} from 'ant-design-vue';
  import { defineComponent, ref, reactive, onMounted, defineProps, defineExpose, UnwrapRef, nextTick, defineEmits } from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import JFormContainer from '@/components/Form/src/jeecg/components/JFormContainer.vue';

  const props = defineProps(["disabled"])
  const emits = defineEmits(['success', 'register', 'ok'])

  const confirmLoading = ref<boolean>(false);
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 6 } });
  const labelCols = ref<any>({ xs: { span: 24 }, sm: { span: 3 } });

  const validatorRules = {
  };

  const Api = reactive({
    add: '/sys/dtInspectPlan/addInspectPlan',
  });

  const formState = reactive({
    alarmType: '',
    deviceName: '',
    deviceLocation: '',
    alarmDescription: '',
    reportContent: '',
    reportTime: '',
  });
  const useForm = Form.useForm;
  const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

  onMounted(() => {
    //初始化字典选项
  });

  function add() {
    edit({});
  }

  function edit(record) {
    nextTick(() => {
      resetFields();
      Object.assign(formState, record);
    });
  }

  /**
   * 提交数据
   */
  function submitForm() {
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style scoped>
  .antd-modal-form {
    padding: 24px 24px 24px 24px;
  }
</style>
