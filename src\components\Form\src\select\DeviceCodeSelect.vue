<template>
  <a-select
    :placeholder="placeholder"
    v-bind="attrs"
    allowClear
    v-model:value="state"
    :filterOption="handleFilterOption"
    :getPopupContainer="getPopupContainer"
    :style="style"
    @change="handleChange"
  >
    <a-select-option v-if="showChooseOption" :value="null">请选择…</a-select-option>
    <template v-for="item in dictOptions" :key="`${item.value}`">
      <a-select-option :value="item.value" :oneCollect="item.oneCollect" :twoCollect="item.twoCollect">
          <span
            :class="[useDicColor && item.color ? 'colorText' : '']"
            :style="{ backgroundColor: `${useDicColor && item.color}` }"
            :title="item.label"
          >
            {{ item.label }}
          </span>
      </a-select-option>
    </template>
  </a-select>
</template>
<script lang="ts">
  import {
    defineComponent,
    PropType,
    ref,
    reactive,
    watchEffect,
    computed,
    unref,
    watch,
    onMounted,
    nextTick
  } from 'vue';
  import {propTypes} from '/src/utils/propTypes';
  import {useAttrs} from '/src/hooks/core/useAttrs';
  import {defHttp} from '/@/utils/http/axios';
  import {loadDictItem, loadTreeData} from '/src/api/common/api';
  import {get, omit} from 'lodash-es';
  import {useRuleFormItem} from '/src/hooks/component/useFormItem';
  import {CompTypeEnum} from '/src/enums/CompTypeEnum';
  import {LoadingOutlined} from '@ant-design/icons-vue';

  export default defineComponent({
    name: 'DeviceCodeSelect',
    inheritAttrs: false,
    components: {LoadingOutlined},
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.number, propTypes.array]),
      pcode: propTypes.string,
      type: propTypes.string,
      placeholder: propTypes.string,
      stringToNumber: propTypes.bool,
      useDicColor: propTypes.bool.def(false),
      getPopupContainer: {
        type: Function,
        default: (node) => node?.parentNode,
      },
      // 是否显示【请选择】选项
      showChooseOption: propTypes.bool.def(true),
      // 下拉项-online使用
      options: {
        type: Array,
        default: [],
        required: false,
      },
      style: propTypes.any,
    },
    emits: ['options-change', 'change', 'update:value'],
    setup(props, {emit, refs}) {
      const dictOptions = ref<any[]>([]);
      const attrs = useAttrs();
      const [state, , , formItemContext] = useRuleFormItem(props, 'value', 'change');
      const getBindValue = Object.assign({}, unref(props), unref(attrs));
      // 是否正在加载回显数据
      const loadingEcho = ref<boolean>(false);
      // 是否是首次加载回显，只有首次加载，才会显示 loading
      let isFirstLoadEcho = true;
      const Api = reactive<any>({
        getSelectDeviceList: '/sys/dtDevice/getSelectDeviceList',
      });

      //组件类型
      const compType = computed(() => {
        return !props.type || props.type === 'list' ? 'select' : props.type;
      });
      onMounted(() => {
        loadingEcho.value = isFirstLoadEcho;
        isFirstLoadEcho = false;
        initDictData().finally(() => {
          loadingEcho.value = isFirstLoadEcho;
        });
      });

      //update-begin-author:taoyan date:20220404 for: 使用useRuleFormItem定义的value，会有一个问题，如果不是操作设置的值而是代码设置的控件值而不能触发change事件
      // 此处添加空值的change事件,即当组件调用地代码设置value为''也能触发change事件
      watch(
        () => props.value,
        () => {
          if (props.value === '') {
            emit('change', '');
            nextTick(() => formItemContext.onFieldChange());
          }
        }
      );

      //update-end-author:taoyan date:20220404 for: 使用useRuleFormItem定义的value，会有一个问题，如果不是操作设置的值而是代码设置的控件值而不能触发change事件
      async function initDictData(param) {
        dictOptions.value = []
        if(!state.value||state.value.length==0){
          emit('change', '');
          emit('update:value', '')
        }
        let params = {
          sysOrgCode:param?param.sysOrgCode:undefined,
          oneType:param?param.oneTypes:undefined,
        }
        defHttp.get({url: Api.getSelectDeviceList, params}, {isTransformResponse: false}).then((res) => {
          //update-end---author:wangshuai---date:2024-12-09---for:不要使用getAction online里面的，要用defHttp---
          if (res.success) {
            for (let item of res.result) {
              dictOptions.value.push({
                label: "["+item['deviceCode']+"]"+item['deviceName'],
                value: item['deviceCode'],
                oneCollect: item['oneCollect'],
                twoCollect: item['twoCollect'],
              });
            }
          } else {
          }
        })
      }

      function handleChange(e, label) {
        const {mode} = unref<Recordable>(getBindValue);
        let changeValue: any;
        // 兼容多选模式

        //update-begin---author:wangshuai ---date:20230216  for：[QQYUN-4290]公文发文：选择机关代字报错,是因为值改变触发了change事件三次，导致数据发生改变------------
        //采用一个值，不然的话value值变换触发多个change
        if (mode === 'multiple') {
          changeValue = e?.target?.value ?? e;
          // 过滤掉空值
          if (changeValue == null || changeValue === '') {
            changeValue = [];
          }
          if (Array.isArray(changeValue)) {
            changeValue = changeValue.filter((item) => item != null && item !== '');
          }
        } else {
          changeValue = e?.target?.value ?? e;
        }
        state.value = changeValue;

        //update-begin---author:wangshuai ---date:20230403  for：【issues/4507】JDictSelectTag组件使用时，浏览器给出警告提示：Expected Function, got Array------------
        emit('change1', label)
        emit('update:value', changeValue)
        //update-end---author:wangshuai ---date:20230403  for：【issues/4507】JDictSelectTag组件使用时，浏览器给出警告提示：Expected Function, got Array述------------
        //update-end---author:wangshuai ---date:20230216  for：[QQYUN-4290]公文发文：选择机关代字报错,是因为值改变触发了change事件三次，导致数据发生改变------------

        // nextTick(() => formItemContext.onFieldChange());
      }

      /** 用于搜索下拉框中的内容 */
      function handleFilterOption(input, option) {
        // update-begin--author:liaozhiyang---date:20230914---for：【QQYUN-6514】 配置的时候，Y轴不能输入多个字段了，控制台报错
        if (typeof option.children === 'function') {
          // 在 label 中搜索
          let labelIf = option.children()[0]?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          if (labelIf) {
            return true;
          }
        }
        // update-end--author:liaozhiyang---date:20230914---for：【QQYUN-6514】 配置的时候，Y轴不能输入多个字段了，控制台报错
        // 在 value 中搜索
        return (option.value || '').toString().toLowerCase().indexOf(input.toLowerCase()) >= 0;
      }

      return {
        state,
        compType,
        attrs,
        loadingEcho,
        getBindValue,
        dictOptions,
        CompTypeEnum,
        handleChange,
        handleFilterOption,
        initDictData,
      };
    },
  });
</script>
<style scoped lang="less">
  // update-begin--author:liaozhiyang---date:20230110---for：【QQYUN-7799】字典组件（原生组件除外）加上颜色配置
  .colorText {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    padding: 0 6px;
    border-radius: 8px;
    background-color: red;
    color: #fff;
    font-size: 12px;
  }

  // update-begin--author:liaozhiyang---date:20230110---for：【QQYUN-7799】字典组件（原生组件除外）加上颜色配置
</style>
