<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="12">
            <a-form-item label="所属单位" :labelCol="labelCol" name="unitName" :wrapperCol="wrapperCol"
                         v-bind="validateInfos.unitName">
              <a-input placeholder="请输入所属单位" :disabled="true"
                       v-model:value="formState.unitName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="建筑物名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <BuildingIdSelect @change="getBuildingName" v-model:value="formState.buildingId"
                               placeholder="请选择建筑物名称" mode="default" :triggerChange="false">
              </BuildingIdSelect>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所属楼层" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <FloorIdSelect ref="FloorIdSelect1" @change="getFloorName" v-model:value="formState.floorId"
                               placeholder="请选择所属楼层" mode="default" :triggerChange="false">
              </FloorIdSelect>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备设施编号" :labelCol="labelCol" :wrapperCol="wrapperCol" name="deviceCode"
                         v-bind="validateInfos.deviceCode">
              <a-input placeholder="请输入设备设施编号" :disabled="is"
                       v-model:value="formState.deviceCode"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备设施名称" :labelCol="labelCol" :wrapperCol="wrapperCol" name="deviceName"
                         v-bind="validateInfos.deviceName">
              <a-input placeholder="请输入设备设施名称" v-model:value="formState.deviceName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备型号" :labelCol="labelCol" :wrapperCol="wrapperCol" name="deviceModel"
                         v-bind="validateInfos.deviceModel">
              <a-input placeholder="请输入设备型号" v-model:value="formState.deviceModel"></a-input>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="设备分类" name="deviceCategory" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         v-bind="validateInfos.deviceCategory">
              <DeviceCategorySelect @change="getSelectCategory" v-model:value="formState.deviceCategory"
                               placeholder="请选择设备分类" mode="default" :triggerChange="false">
              </DeviceCategorySelect>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备类型" name="deviceType" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         v-bind="validateInfos.deviceType">
              <DeviceTypeSelect ref="DeviceTypeSelect1" v-model:value="formState.deviceType" placeholder="请选择设备类型"
                              mode="default" :triggerChange="false"></DeviceTypeSelect>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所在区域" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <JAreaLinkage placeholder="请选择所在区域" v-model:value="formState.deviceArea"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="生产厂家" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入生产厂家" v-model:value="formState.producer"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="位置描述" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入位置描述" v-model:value="formState.deviceLocation"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="出厂日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker style="width: 100%;" v-model:value="formState.productionDate" show-time
                             format="YYYY-MM-DD"
                             valueFormat="YYYY-MM-DD" placeholder="请选择出厂日期"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="安装日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                             v-model:value="formState.installDate"
                             style="width: 100%;" show-time placeholder="请选择安装日期"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="维保周期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <JSelectMultiple dict-code="cycle" v-model:value="formState.maintenanceCycle"
                               placeholder="请选择维保周期"
                               mode="default" :triggerChange="false"></JSelectMultiple>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="质保日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-date-picker format="YYYY-MM-DD" valueFormat="YYYY-MM-DD"
                             v-model:value="formState.warrantyDate"
                             style="width: 100%;" show-time placeholder="请选择质保日期"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="选择视频通道" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input placeholder="请输入选择视频通道" v-model:value="formState.videoChannel"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="是否远程控制" :labelCol="labelCols" :wrapperCol="wrapperCols">
              <JSwitch v-model:value="formState.status" :options="['1', '0']" :labelOptions="['是', '否']">
              </JSwitch>
            </a-form-item>
          </a-col>
          <br/>
          <a-col :span="12">
            <a-form-item label="设备设施图片" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <JImageUpload
                      :url-params="`?businessCategory=deviceManagement1&businessType=deviceManagement2&businessId=deviceManagement3`"
                      :fileMax="1" v-model:value="formState.devicePicture"></JImageUpload>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import {defineComponent, ref, reactive, onMounted, defineExpose,getCurrentInstance, defineEmits, nextTick} from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import JImageUpload from '/@/components/Form/src/jeecg/components/JImageUpload.vue';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import BuildingIdSelect from '/@/components/Form/src/jeecg/components/serchselect/BuildingIdSelect.vue';
  import FloorIdSelect from '/@/components/Form/src/jeecg/components/serchselect/FloorIdSelect.vue';
  import DeviceCategorySelect from '/@/components/Form/src/jeecg/components/serchselect/DeviceCategorySelect.vue';
  import DeviceTypeSelect from '/@/components/Form/src/jeecg/components/serchselect/DeviceTypeSelect.vue';
  import {JAreaLinkage} from '/@/components/Form';
  import {Form} from 'ant-design-vue';
  import {useUserStore} from '/@/store/modules/user';
  import {useMessage} from '/@/hooks/web/useMessage';
  import { getDeviceTypeListById, floorList} from '@/hooks/params/param'
  //update-begin---author:wangshuai ---date:20220616  for：报表示例验证修改--------------
  //update-end---author:wangshuai ---date:20220616  for：报表示例验证修改--------------


  const emit = defineEmits(['success', 'register', 'ok'])
  const userStore = useUserStore();
  //update-begin---author:wangshuai ---date:20220616  for：报表示例验证修改--------------
  const formState = reactive({
    buildingId: '',	// 建筑物ID
    buildingName: '',	// 建筑物名称
    deviceArea: '',	// 所在区域
    deviceCategory: '',	// 设备分类ID
    deviceCode: '',	// 设备编码
    deviceLocation: '',	// 位置描述
    deviceModel: '',	// 设备型号
    deviceName: '',	// 设备名称
    devicePicture: '',	// 设备图片
    deviceType: '',	// 设备类型ID
    floorId: '',	// 楼层ID
    floorName: '',	// 楼层名称
    installDate: '',	// 安装日期
    maintenanceCycle: '',	// 维保周期
    producer: '',	// 生产厂家
    productionDate: '',	// 出厂日期
    remark: '',	// 备注
    remoteControlFlag: '',	// 是否远程控制(0否 1是)
    status: '',	// 设备状态(0停用 1启用)
    orgCode: userStore.getLoginInfo.unitInfo.orgCode,	// 机构编码
    unitId: userStore.getLoginInfo.unitInfo.id,	// 单位ID
    unitName: userStore.getLoginInfo.unitInfo.unitName,	// 单位名称
    videoChannel: '',	// 视频通道
    warrantyDate: '',	// 质保日期
  });
  //update-end---author:wangshuai ---date:20220616  for：报表示例验证修改--------------
  const {createMessage} = useMessage();
  const formRef = ref();
  const useForm = Form.useForm;
  const url = reactive({
    add: '/sys/dtDevice/addDtDevice',
    edit: '/sys/dtDevice/editDtDevice',
    getBuildingList: '/sys/dtBuilding/getBuildingList'
  });
  const labelCol = ref<any>({xs: {span: 24}, sm: {span: 8}});
  const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 16}});
  const labelCols = ref<any>({xs: {span: 24}, sm: {span: 4}});
  const wrapperCols = ref<any>({xs: {span: 24}, sm: {span: 16}});
  const confirmLoading = ref<boolean>(false);
  const FloorIdSelect1 = ref();
  const DeviceTypeSelect1 = ref();
  //表单验证
  const validatorRules = {
    unitType: [{required: true, message: '请选择单位类型', trigger: 'change'}],
    unitName: [{required: true, message: '请输入单位名称'}],
    deviceCode: [{required: true, message: '请输入设备设施编号'}],
    deviceName: [{required: true, message: '请输入设备设施名称'}],
    deviceModel: [{required: true, message: '请输入设备型号'}],
    deviceCategory: [{required: true, message: '请选择设备分类', trigger: 'change'}],
    deviceType: [{required: true, message: '请选择设备类型', trigger: 'change'}],
  };
  //update-begin---author:wangshuai ---date:20220616  for：报表示例验证修改------------
  const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

  onMounted(() => {

  })
  //update-end---author:wangshuai ---date:20220616  for：报表示例验证修改------------
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  let is = ref(0)

  /**
   * 编辑
   */
  function edit(record) {
    if (record.id) {
      is.value = 1
      DeviceTypeSelect1.value.loadDictOptions(record.deviceCategory)
      FloorIdSelect1.value.loadDictOptions(record.buildingId)
    } else {
      is.value = 0
    }
    nextTick(() => {
      resetFields();
      //赋值
      Object.assign(formState, record);
    });
  }
  function getBuildingName(e,n) {
    FloorIdSelect1.value.loadDictOptions(e)
    formState.buildingName = n
  }
  function getFloorName(e,n) {
    formState.floorName = n
  }

  function getSelectCategory(e) {
    formState.deviceType = ''
    DeviceTypeSelect1.value.arrayValue.length = 0
    DeviceTypeSelect1.value.loadDictOptions(e)
  }
  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    //update-begin---author:wangshuai ---date:20220616  for：报表示例验证修改------------
    await validate();
    confirmLoading.value = true;
    let httpurl = '';
    let method = '';
    //时间格式化
    formState.deviceArea = formState.deviceArea ? formState.deviceArea.toString() : ""
    let model = formState;
    if (!model.id) {
      httpurl += url.add;
      method = 'post';
    } else {
      httpurl += url.edit;
      method = 'put';
    }
    defHttp.request({url: httpurl, params: model, method: method,}, {isTransformResponse: false})
            .then((res) => {
              if (res.success) {
                createMessage.success(res.message);
                emit('ok');
              } else {
                createMessage.warning(res.message);
              }
            })
            .finally(() => {
              confirmLoading.value = false;
            });
    //update-end---author:wangshuai ---date:20220616  for：报表示例验证修改--------------
  }
  defineExpose({
    add,
    edit,
    submitForm,
  });

</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 24px 24px 24px 24px;
  }

  .uploadBoxs {
    width: 50%;
    height: 150px;
    cursor: pointer;
    border: 1px dashed #1296db;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border-radius: 10px;

    img {
      width: 30px;
      height: 30px;
    }
  }
</style>
