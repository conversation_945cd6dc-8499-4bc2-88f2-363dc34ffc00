<!-- 消防设备设施管理 FacilityManagement -->
<template>
    <a-card :bordered="false">
        <!--自定义查询区域-->
        <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
            <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
                <a-row :gutter="24">
                    <a-col :lg="6">
                        <a-form-item label="建筑物名称">
                            <JSelectMultiple v-model:value="queryParam.buildingId" placeholder="请选择建筑物名称"
                                :options="buildingListOptions" mode="default" @change="chooseFlor" triggerChange="false">
                            </JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :lg="6">
                        <a-form-item label="所属楼层">
                            <JSelectMultiple v-model:value="queryParam.floorId" placeholder="请选择建所属楼层"
                                :options="floorOptions" mode="default" triggerChange="false"></JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <!--<a-col :lg="6">-->
                        <!--<a-form-item label="设备分类">-->
                            <!--<JSelectMultiple v-model:value="queryParam.deviceCategory" placeholder="请选择设备分类"-->
                                <!--:options="deviceCategoryOptions" mode="default" @change="getSelectCategory"-->
                                <!--triggerChange="false"></JSelectMultiple>-->
                        <!--</a-form-item>-->
                    <!--</a-col>-->
                    <a-col :lg="6">
                        <a-form-item label="设备类型">
                            <JSelectMultiple v-model:value="queryParam.deviceType" placeholder="请选择设备类型"
                                :options="deviceTypeOptions" mode="default" triggerChange="false"></JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :lg="6">
                        <a-form-item label="设备编码">
                            <a-input placeholder="请输入设备编码" v-model:value="queryParam.deviceCode"></a-input>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
            <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery"
                style="margin-right: 5px">查询</a-button>
            <a-button @click="handleAdd" type="primary" preIcon="ant-design:plus" style="margin-right: 5px">新增</a-button>
        </div>
        <!-- table区域-begin -->
        <JVxeTable ref="tableRef" bordered row-number keep-source resizable :maxHeight="484" :loading="loading"
            :dataSource="dataSource" :columns="columns" :pagination="pagination" style="margin-top: 8px" row-selection
            @pageChange="handlePageChange">
            <template #deviceState="props">
                <span v-if="props == '1'">在线</span>
                <span v-if="props == '2'">离线</span>
                <span v-if="props == '3'">故障</span>
            </template>
            <template #action="props">
                <!--<a @click="handleDetail(props.row)">实时数据</a>-->
                <!--<a-divider type="vertical" />-->
                <a @click="handleEdit(props.row)">编辑</a>
                <a-divider type="vertical" />
                <Popconfirm title="请确认是否删除？" @confirm="delRow(props.row)">
                    <a>删除</a>
                </Popconfirm>
            </template>
        </JVxeTable>
    </a-card>
    <DeptModal ref="DeptModal1" @ok="handleSuccess" />
</template>
<!--巡检项次-->
<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { Popconfirm } from 'ant-design-vue';
import { loadCategoryData } from '/@/api/common/api';
import { initDictOptions } from '/@/utils/dict';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import DeptModal from './components/DeptModal.vue';
import { useUserStore } from '/@/store/modules/user';
import { floorList } from '/@/hooks/params/param'
import { getRegionName } from '/@/hooks/region/getRegion'
import { useMessage } from '/@/hooks/web/useMessage';
import {filterObj, getFileAccessHttpUrl} from '/@/utils/common/compUtils';

const userStore = useUserStore();
const DeptModal1 = ref();
function handleAdd() {
    DeptModal1.value.disableSubmit = false;
    DeptModal1.value.add();
}
function handleEdit(e) {
    DeptModal1.value.disableSubmit = false;
    DeptModal1.value.edit(e);
}

const iSorter = ref<any>({column: 'createTime', order: 'desc'});
const iFilters = ref<any>({});

//-----自定义查询----begin--------
const labelCol = reactive({
    xs: { span: 24 },
    sm: { span: 10 },
})
const wrapperCol = reactive({
    xs: { span: 24 },
    sm: { span: 14 },
})
const queryParam = ref<any>({
    buildingId: '',
    deviceCategory: '',
    deviceCode: '',
    floorId: '',
    deviceType: '',
    deviceSystem: '',
    pageNo: '1',
    pageSize: '10'
});
const loading = ref<boolean>(false);
const dictOptions = ref<any>([]);
const buildingListOptions = ref<any>([]);
const floorOptions = ref<any>([]);
const deviceCategoryOptions = ref<any>([]);
const deviceTypeOptions = ref<any>([]);
//表头
const columns = ref([
    {
        title: '单位名称',
        key: 'unitName',
        align: "center",
        minWidth: 250,
    },
    {
        title: '建筑物名称',
        key: 'buildingName',
        minWidth: 150,
        align: "center",
    },
    {
        title: '楼层',
        key: 'floorName',
        minWidth: 100,
        align: "center",
    },
    {
        title: '设备编码',
        key: 'deviceCode',
        minWidth: 150,
        align: "center",
    },
    {
        title: '设备名称',
        key: 'deviceName',
        minWidth: 150,
        align: "center",
    },
    {
        title: '设备分类',
        key: 'deviceCategoryName',
        minWidth: 150,
        align: "center",
    },
    {
        title: '设备类型',
        key: 'deviceTypeName',
        minWidth: 150,
        align: "center",
    },
    {
        title: '位置描述',
        key: 'deviceLocation',
        minWidth: 150,
        align: "center",
    },
    {
        title: '操作',
        type: "slot",
        key: 'action',
        align: 'center',
        fixed: 'right',
        width: 200,
        slotName: 'action',
    },
]);
const dataSource = ref<any>([]);
const { createMessage } = useMessage();
onMounted(() => {
    dictOptions.value['kaiguan'] = [
        { text: '是', value: '1' },
        { text: '否', value: '2' },
    ];
    getTimeProps()

    getSelectCategory('deviceType');
  //初始化字典选项
    initDictConfig();

    //初始加载页面
    loadData();

});
const Api = reactive<any>({
    list: '/sys/dtDevice/pageList',
    getBuildingList: '/sys/dtBuilding/getBuildingList',
    getFloor: '/sys/dtBuildingFloor/getDtBuildingFloorModelList',
    getDeviceCategoryList: '/sys/dtDeviceCategory/getDeviceCategoryList',
    getDeviceTypeList: '/sys/dtDeviceCategory/getDeviceTypeList',
    del: '/sys/dtDevice/delDtDevice',

});
function delRow(e) {
    defHttp.delete({ url: Api.del, data: { id: e.id } }, { isTransformResponse: false,joinParamsToUrl: true }).then((res) => {
        if(res.success){
            loadData();
            createMessage.success(res.message);
        }else {
            createMessage.warning(res.message);
        }
    });
}
function chooseFlor(e) {
    floorList(e).then(e => floorOptions.value = e)
}
// 获取条件
function getTimeProps() {
    // 建筑物名称
    defHttp.get({ url: Api.getBuildingList }).then((e) => {
        e.forEach(i => {
            i.label = i.buildingName
            i.value = i.id
        });
        buildingListOptions.value = e
    });
    // 建筑物楼层
    defHttp.get({ url: Api.getFloor }).then((e) => {

        floorOptions.value = e
    });
    defHttp.get({ url: Api.getDeviceCategoryList }).then((e) => {
        e.forEach(i => {
            i.label = i.deviceCategory
            i.value = i.id
        });
        deviceCategoryOptions.value = e
    });
}

const getSelectCategory = (e: Object): void => {
    deviceTypeOptions.length = 0
    defHttp.get({ url: Api.getDeviceTypeList, params: { parentId: e } }).then((res) => {
        if (res.length != 0) {
            res.forEach(i => {
                i.label = i.deviceType
                i.value = i.id
            });
            deviceTypeOptions.value = res
            queryParam.deviceType = ''
        }
    });
}
const pagination = ref<any>({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30'],
    showTotal: (total, range) => {
        return range[0] + '-' + range[1] + ' 共' + total + '条';
    },
    showQuickJumper: true,
    showSizeChanger: true,
    total: dataSource.value.length,
});

const selectedRowKeys = ref<any>([]);
const selectionRows = ref<any>([]);

// 当分页参数变化时触发的事件
function handlePageChange(event) {
    // 重新赋值
    pagination.value.current = event.current;
    pagination.value.pageSize = event.pageSize;
    // queryParam.value.pageNo = event.current;
    // queryParam.value.pageSize = event.pageSize;
    // 查询数据
    loadData();
}

function handleDetail(record) {
}

/**
 * 获取查询参数
 */
function getQueryParams() {
  let params = Object.assign(queryParam.value, iSorter.value, iFilters.value);
  params.pageNo = pagination.value.current;
  params.pageSize = pagination.value.pageSize;
  return filterObj(params);
}

function loadData(arg) {
    if (arg === 1) {
        pagination.value.current = 1;
    }
    loading.value = true;
    // let params = queryParam.value
    let params = getQueryParams();
    defHttp.get({ url: Api.list, params }).then((res) => {
        // if (res.success) {
        res.records.forEach(s => {
            s.deviceAreas = s.deviceArea?getRegionName(s.deviceArea):""
        });
        dataSource.value = res.records;

        if (res && res.total) {
            pagination.value.total = res.total;
        } else {
            pagination.value.total = 0;
        }
        // }
    })
        .finally(() => {
            loading.value = false;
        });
}
//查询
function searchQuery() {
    loadData(1);
    selectedRowKeys.value = [];
    selectionRows.value = [];
}


/**
 * 初始化字典选项
 */
async function initDictConfig() {
    queryParam.value.unitId = userStore.getLoginInfo.unitInfo.id;

}
/**
 * 保存表单后回调事件
 */
function handleSuccess() {
    selectedRowKeys.value = [];
    selectionRows.value = [];
    loadData(1);
}


</script>
<style lang="less" scoped>
.jeecg-basic-table-form-container {
    padding: 0px;

    .table-page-search-submitButtons {
        display: block;
        margin-bottom: 0;
        white-space: nowrap;
    }

    .ant-form-item {
        margin-bottom: 8px !important;
    }
}
</style>
