<!-- 逾期推送管理 OverduePushManagement -->
<template>
    <a-card :bordered="false">
        <!--自定义查询区域-->
        <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
            <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
            </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
            <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleAdd" style="margin-right: 5px">
                新增
            </a-button>
        </div>
        <!-- table区域-begin -->
        <JVxeTable
                ref="tableRef"
                bordered
                row-number
                keep-source
                resizable
                :maxHeight="484"
                :loading="loading"
                :dataSource="dataSource"
                :columns="columns"
                :pagination="pagination"
                style="margin-top: 8px"
                @pageChange="handlePageChange">
            <template #ruleStatus="props">
                <JSwitch v-model:value="props.row.ruleStatus" :options="['1', '0']"
                         :labelOptions="['启用', '停用']" @change="JSwitchClick(props.row)">
                </JSwitch>
            </template>
            <template #action="props">
                <a @click="handleEdit(props.row)">编辑</a>
                <a-divider type="vertical"/>
                <Popconfirm title="是否删除？" @confirm="handleDelete(props.row)">
                    <a>删除</a>
                </Popconfirm>
            </template>
        </JVxeTable>
    </a-card>
    <DeptModal ref="DeptModal1" @ok="loadData"/>
</template>

<script lang="ts" setup>
    import {onMounted, ref, reactive} from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {Popconfirm} from 'ant-design-vue';
    import {filterObj} from '/@/utils/common/compUtils';
    import {useUserStore} from '/@/store/modules/user';
    import DeptModal from './components/DeptModal.vue';
    import {useMessage} from '/@/hooks/web/useMessage';
    import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';

    const userStore = useUserStore();
    const alarmTypeOptions = ref<any>([])
    const DeptModal1 = ref();
    const labelCol = reactive({
        xs: {span: 24},
        sm: {span: 8},
    })
    const wrapperCol = reactive({
        xs: {span: 24},
        sm: {span: 16},
    })
    const queryParam = ref<any>({});
    const loading = ref<boolean>(false);
    const columns = ref([
        {
            title: '逾期级别',
            key: 'overdueLevel',
            minWidth: 150,
            align: "center",
        },
        {
            title: '逾期时间',
            key: 'overdueDuration',
            minWidth: 150,
            align: "center",
        },
        // {
        //     title: '通知人员',
        //     key: 'noticeStaff',
        //     minWidth: 180,
        //     align: "center",
        // },
        // {
        //     title: '通知内容',
        //     key: 'noticeContent',
        //     minWidth: 250,
        //     align: "center",
        // },
        {
            title: '状态',
            key: 'ruleStatus',
            minWidth: 150,
            align: "center",
            type: 'slot',
            slotName: 'ruleStatus',
        },
        {
            title: '创建时间',
            key: 'createTime',
            minWidth: 150,
            align: "center",
        },
        {
            title: '操作',
            type: "slot",
            key: 'action',
            align: 'center',
            fixed: 'right',
            width: 200,
            slotName: 'action',
        },
    ]);
    const Api = reactive<any>({
        list: '/sys/dtAlarmRemindRule/pageList',
        delete: '/sys/dtAlarmRemindRule/alarmRemindRuleService',
        editAlarmRemindRule: '/sys/dtAlarmRemindRule/editAlarmRemindRule',
    });
    const dataSource = ref<any>([{}]);
    const pagination = ref<any>({
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条';
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
    });
    const iSorter = ref<any>({column: 'createTime', order: 'desc'});
    const iFilters = ref<any>({});
    const {createMessage} = useMessage();

    //新增
    function handleAdd() {
        DeptModal1.value.disableSubmit = false;
        DeptModal1.value.add();
    }

    //新增
    function handleEdit(record) {
        DeptModal1.value.disableSubmit = false;
        DeptModal1.value.edit(record);
    }

    /**
     * 获取查询参数
     */
    function getQueryParams() {
        let params = Object.assign(queryParam.value, iSorter.value, iFilters.value);
        params.pageNo = pagination.value.current;
        params.pageSize = pagination.value.pageSize;
        return filterObj(params);
    }

    // 当分页参数变化时触发的事件
    function handlePageChange(event) {
        // 重新赋值
        pagination.value.current = event.current;
        pagination.value.pageSize = event.pageSize;
        // 查询数据
        loadData();
    }

    function loadData(arg) {
        if (arg === 1) {
            pagination.value.current = 1;
        }
        loading.value = true;
        let params = getQueryParams();
        defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                dataSource.value = res.result.records;
                if (res.result && res.result.total) {
                    pagination.value.total = res.result.total;
                } else {
                    pagination.value.total = 0;
                }
            }
        }).finally(() => {
            loading.value = false;
        });
    }

    function handleDelete(e) {
        defHttp.delete({url: Api.delete, data: {id: e.id}}, {
            isTransformResponse: false,
            joinParamsToUrl: true
        }).then((res) => {
            if (res.success) {
                loadData();
                createMessage.success(res.message);
            } else {
                createMessage.warning(res.message);
            }
        });
    }

    function JSwitchClick(n) {
        let params = {
            id: n.id,
            ruleStatus: n.ruleStatus == '0' ? '1' : '0'
        }
        defHttp.put({url: Api.editAlarmRemindRule, data: params}, {
            isTransformResponse: false,
            joinParamsToUrl: true
        }).then((res) => {
            if (res.success) {
                loadData();
                createMessage.success(res.message);
            } else {
                createMessage.warning(res.message);
            }
        });
    }

    onMounted(() => {
        //初始加载页面
        loadData();
    });

</script>

<style scoped>

</style>
