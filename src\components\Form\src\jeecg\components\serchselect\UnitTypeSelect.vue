<!--字典下拉多选-->
<template>
  <a-select :value="arrayValue" @change="onChange" :mode="mode" :filter-option="filterOption" :disabled="disabled"
            :placeholder="placeholder" allowClear showSearch :getPopupContainer="getParentContainer">
    <a-select-option v-for="(item, index) in dictOptions" :key="index" :title="item.label" :longitude="item.longitude" :latitude="item.latitude" :responseStatus="item.responseStatus" :header="item.header" :phone="item.phone" :unitAddress="item.unitAddress"
                     :getPopupContainer="getParentContainer"
                     :value="item.value">
      {{ item.label }}
    </a-select-option>
  </a-select>
</template>
<script lang="ts">
  import {computed, defineComponent, onMounted, ref, nextTick, watch, reactive} from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import {propTypes} from '/@/utils/propTypes';
  import {useMessage} from '/@/hooks/web/useMessage';
  import {useUserStore} from '/@/store/modules/user';

  export default defineComponent({
    name: 'UnitTypeSelect',
    components: {},
    inheritAttrs: false,
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.array]),
      placeholder: {
        type: String,
        default: '请选择',
        required: false,
      },
      readOnly: {
        type: Boolean,
        required: false,
        default: false,
      },
      options: {
        type: Array,
        default: () => [],
        required: false,
      },
      spliter: {
        type: String,
        required: false,
        default: ',',
      },
      popContainer: {
        type: String,
        default: '',
        required: false,
      },
      reportld: {
        type: String,
        required: false,
      },
      rescueType: {
        type: String,
        required: false,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      mode: {
        type: Boolean,
        default: "multiple",
      },
    },
    emits: ['options-change', 'change', 'input', 'update:value'],
    setup(props, {emit, refs}) {
      const arrayValue = ref<any[]>(!props.value ? [] : props.value.split(props.spliter));
      const dictOptions = ref<any[]>([]);
      const userStore = useUserStore();
      const Api = reactive<any>({
        getFireUnitList: '/sys/kanban/getFireUnitList',
      });
      onMounted(() => {
      });

      watch(() => props.value, (val) => {
          if (!val) {
            arrayValue.value = [];
          } else {
            arrayValue.value = props.value;
          }
        }
      );

      //适用于 动态改变下拉选项的操作
      watch(() => props.options, () => {
        if (props.dictCode) {
          // nothing to do
        } else {
          dictOptions.value = props.options;
        }
      });

      function onChange(selectedValue, label) {
        if (props.mode == "multiple") {
          emit('change', selectedValue.join(props.spliter));
          emit('update:value', selectedValue.join(props.spliter));
        } else {
          emit('change', selectedValue,label.longitude,label.latitude,label.title,label.header,label.phone,label.unitAddress,label.responseStatus);
          emit('update:value', selectedValue);
        }
      }

      function getParentContainer(node) {
        if (document.getElementsByClassName('full')[0]) {
          return document.getElementsByClassName('full')[0]
        } else {
          return document.getElementsByClassName('kanban')[0]
        }
      }

      function loadDictOptions(param) {
        dictOptions.value = [];
        let params = {
          reportId: param.reportld,
          rescueType: props.rescueType,
        }
        defHttp.get({url: Api.getFireUnitList, params}, {isTransformResponse: false}).then((res) => {
          if (res.success) {
            dictOptions.value = res.result.map((item) => ({
              value: item.id,
              label: item.unitName+"("+(item.distance/1000).toFixed(2)+"km)",
              latitude: item.latitude,
              longitude: item.longitude,
              header: item.header,
              phone: item.phone,
              unitAddress: item.unitAddress,
              distance: item.distance,
              responseStatus: item.responseStatus,
            }));
            emit('change',  dictOptions.value[0].value,dictOptions.value[0].longitude,dictOptions.value[0].latitude,dictOptions.value[0].label,dictOptions.value[0].header,dictOptions.value[0].phone,dictOptions.value[0].unitAddress,dictOptions.value[0].responseStatus);
            emit('update:value',  dictOptions.value[0].value);
          }
        })
      }

      function filterOption(input, option) {
        return option.children()[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      }

      return {
        dictOptions,
        onChange,
        arrayValue,
        getParentContainer,
        filterOption,
        loadDictOptions,
      };
    },
  });
</script>
<style lang="less" scoped>
  // ::v-deep .ant-select-selection-item {
  //   color: rgba(0, 0, 0, 0.25) !important;
  // }
</style>
