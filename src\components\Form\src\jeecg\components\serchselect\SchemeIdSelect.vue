<!--字典下拉多选-->
<template>
  <a-select :value="arrayValue" @change="onChange" :mode="mode" :filter-option="filterOption" :disabled="disabled"
            :placeholder="placeholder" allowClear showSearch :getPopupContainer="getParentContainer">
    <a-select-option v-for="(item, index) in dictOptions" :key="index" :title="item.label"
                     :getPopupContainer="getParentContainer"
                     :value="item.value">
      {{ item.label }}
    </a-select-option>
  </a-select>
</template>
<script lang="ts">
  import {computed, defineComponent, onMounted, ref, nextTick, watch, reactive} from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import {propTypes} from '/@/utils/propTypes';
  import {useMessage} from '/@/hooks/web/useMessage';
  import {useUserStore} from '/@/store/modules/user';

  export default defineComponent({
    name: 'SchemeIdSelect',
    components: {},
    inheritAttrs: false,
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.array]),
      placeholder: {
        type: String,
        default: '请选择',
        required: false,
      },
      readOnly: {
        type: Boolean,
        required: false,
        default: false,
      },
      options: {
        type: Array,
        default: () => [],
        required: false,
      },
      spliter: {
        type: String,
        required: false,
        default: ',',
      },
      popContainer: {
        type: String,
        default: '',
        required: false,
      },
      parentUnitId: {
        type: String,
        required: false,
      },
      unitType: {
        type: String,
        required: false,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      mode: {
        type: Boolean,
        default: "multiple",
      },
    },
    emits: ['options-change', 'change', 'input', 'update:value'],
    setup(props, {emit, refs}) {
      const arrayValue = ref<any[]>(!props.value ? [] : props.value.split(props.spliter));
      const dictOptions = ref<any[]>([]);
      const userStore = useUserStore();
      const parentUnitId = ref();
      const Api = reactive<any>({
        getPreSchemeList: '/sys/kanban/getPreSchemeList',
      });
      onMounted(() => {
        // loadDictOptions();
      });

      watch(() => props.value, (val) => {
          if (!val) {
            arrayValue.value = [];
          } else {
            arrayValue.value = props.value;
          }
        }
      );

      //适用于 动态改变下拉选项的操作
      watch(() => props.options, () => {
        if (props.dictCode) {
          // nothing to do
        } else {
          dictOptions.value = props.options;
        }
      });
      function loadDictOptions(unitId) {
        dictOptions.value = [];
        let params = {
          unitId: unitId,
        }
        defHttp.get({url: Api.getPreSchemeList, params}, {isTransformResponse: false}).then((res) => {
          if (res.success) {
            dictOptions.value = res.result.map((item) => ({
              value: item.id,
              label: item.schemeName
            }));
            emit('change',  dictOptions.value[0].value);
            emit('update:value',  dictOptions.value[0].value);
          }
        })
      }
      function onChange(selectedValue, label) {
        if (props.mode == "multiple") {
          emit('change', selectedValue.join(props.spliter));
          emit('update:value', selectedValue.join(props.spliter));
        } else {
          emit('change', selectedValue);
          emit('update:value', selectedValue);
        }
      }

      function getParentContainer(node) {
        if (document.getElementsByClassName('full')[0]) {
          return document.getElementsByClassName('full')[0]
        } else {
          return document.getElementsByClassName('kanban')[0]
        }
      }



      function filterOption(input, option) {
        return option.children()[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      }

      return {
        dictOptions,
        onChange,
        arrayValue,
        getParentContainer,
        filterOption,
        loadDictOptions,
        parentUnitId,
      };
    },
  });
</script>
<style lang="less" scoped>
  // ::v-deep .ant-select-selection-item {
  //   color: rgba(0, 0, 0, 0.25) !important;
  // }
</style>
