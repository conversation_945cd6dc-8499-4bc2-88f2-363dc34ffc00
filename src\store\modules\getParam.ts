import { store } from '/@/store';
import { defineStore } from 'pinia';
interface ParamState {
  isFlag?: boolean;
  ids: string;
}
export const useParamStore = defineStore({
  id: 'app-param',
  state: (): ParamState => ({
    isFlag: false,
    ids: ''
  }),
  getters: {
  },
  actions: {
  },
});

// Need to be used outside the setup
export function useParamStoreWithOut() {
  return useParamStore(store);
}
