<template>
    <a-card :bordered="false">
        <!--自定义查询区域-->
        <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
            <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
                <a-row :gutter="24">
                    <a-col :lg="6">
                        <a-form-item label="单位名称">
                            <a-input placeholder="请输入单位名称" v-model:value="queryParam.systemName"></a-input>
                        </a-form-item>
                    </a-col>
                    <a-col :lg="6">
                        <a-form-item label="开始时间">
                            <DateTimeSelect v-model:value="queryParam.startTime" placeholder="请选择开始时间"
                                        @change="onStartDateTimeSelect"></DateTimeSelect>
                        </a-form-item>
                    </a-col>
                    <a-col :lg="6">
                        <a-form-item label="结束时间">
                            <DateTimeSelect v-model:value="queryParam.endTime" placeholder="请选择结束时间"
                                        @change="onEndDateTimeSelect"></DateTimeSelect>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
            <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery"
                      style="margin-right: 5px">查询
            </a-button>
        </div>
        <!-- table区域-begin -->
        <JVxeTable ref="tableRef" bordered row-number keep-source resizable :maxHeight="484"
                   :loading="loading" :dataSource="dataSource" :columns="columns" :pagination="pagination"
                   style="margin-top: 8px"
                   @pageChange="handlePageChange">
            <template #systemName="props">
                <a href="#" @click="handleDetail(props.row)">{{props.row.systemName}}</a>
            </template>
        </JVxeTable>
    </a-card>
    <DeptModal ref="DeptModal1" @ok="handleSuccess"/>
</template>

<script lang="ts" setup>
    import {onMounted, ref, reactive} from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {filterObj} from '/@/utils/common/compUtils';
    import {initDictOptions} from '/@/utils/dict';
    import DateTimeSelect from '/@/components/Form/src/jeecg/components/serchselect/DateTimeSelect.vue';
    import DeptModal from './components/DeptModal.vue';

    const DeptModal1 = ref();

    const labelCol = reactive({
        xs: {span: 24},
        sm: {span: 8},
    })
    const wrapperCol = reactive({
        xs: {span: 24},
        sm: {span: 16},
    })
    const queryParam = ref<any>({
        systemName: '',
        status: '',
    });
    const loading = ref<boolean>(false);
    const dictOptions = ref<any>([]);
    const getInput = (e: string): void => {
        console.log(e);
    }
    const columns = ref([
        {
            title: '单位名称',
            key: 'systemName',
            minWidth: 150,
            align: "center",
        },
        {
            title: '评分时间',
            key: 'buildingType_dictText',
            minWidth: 180,
            align: "center",
        },
        {
            title: '安全评分',
            key: 'systemName',
            minWidth: 80,
            align: "center",
            type: "slot",
            slotName: 'systemName',
        },
    ]);
    const Api = reactive<any>({
        list: '/sys/dtAssessSystem/pageList',
    });
    const dataSource = ref<any>([]);
    const pagination = ref<any>({
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条';
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: dataSource.value.length,
    });

    const iSorter = ref<any>({column: 'createTime', order: 'desc'});
    const iFilters = ref<any>({});

    /**
     * 获取查询参数
     */
    function getQueryParams() {
        let params = Object.assign(queryParam.value, iSorter.value, iFilters.value);
        params.pageNo = pagination.value.current;
        params.pageSize = pagination.value.pageSize;
        return filterObj(params);
    }
    // 开始时间
    function onStartDateTimeSelect(e) {
    }

    // 结束时间
    function onEndDateTimeSelect(e) {
    }
    function handleDetail(e) {
        DeptModal1.value.disableSubmit = true;
        DeptModal1.value.edit(e);
    }

    // 当分页参数变化时触发的事件
    function handlePageChange(event) {
        // 重新赋值
        pagination.value.current = event.current;
        pagination.value.pageSize = event.pageSize;
        // 查询数据
        loadData();
    }


    function loadData(arg) {
        if (arg === 1) {
            pagination.value.current = 1;
        }
        loading.value = true;
        let params = getQueryParams();
        defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                dataSource.value = res.result.records;
                if (res.result && res.result.total) {
                    pagination.value.total = res.result.total;
                } else {
                    pagination.value.total = 0;
                }
            }
        }).finally(() => {
            loading.value = false;
        });
    }

    //查询
    function searchQuery() {
        loadData(1);
    }

    async function initDictConfig() {
        queryParam.value.startTime = datetimePickerFormat(undefined, "00:00:00");
        queryParam.value.endTime = datetimePickerFormat(undefined, "23:59:59");
    }
    function datetimePickerFormat(aDate, timeAppend) {
        aDate = aDate != undefined ? aDate : new Date()
        let year = aDate.getFullYear()
        let month = aDate.getMonth() + 1
        month = month < 10 ? ('0' + month) : month
        let day = aDate.getDate()
        day = day < 10 ? ('0' + day) : day
        if (!timeAppend) {
            let hh = aDate.getHours() < 10
                ? '0' + aDate.getHours()
                : aDate.getHours()
            let mm = aDate.getMinutes() < 10
                ? '0' + aDate.getMinutes()
                : aDate.getMinutes()
            let ss = aDate.getSeconds() < 10
                ? '0' + aDate.getSeconds()
                : aDate.getSeconds()
            timeAppend = hh + ':' + mm + ':' + ss
        }
        return year + '-' + month + '-' + day + ' ' + timeAppend
    }
    function handleSuccess() {
        loadData(1);
    }

    onMounted(() => {
        //初始加载页面
        loadData();
        //初始化字典选项
        initDictConfig();
    });
</script>
<style lang="less" scoped>
    .jeecg-basic-table-form-container {
        padding: 0px;

        .table-page-search-submitButtons {
            display: block;
            margin-bottom: 0;
            white-space: nowrap;
        }

        .ant-form-item {
            margin-bottom: 8px !important;
        }
    }
</style>
