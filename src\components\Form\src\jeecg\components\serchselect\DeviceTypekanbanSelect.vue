<!--字典下拉多选-->
<template>
  <a-select :value="arrayValue" @change="onChange" :mode="mode" :filter-option="filterOption" :disabled="disabled"
            :placeholder="placeholder" allowClear showSearch :getPopupContainer="getParentContainer">
    <a-select-option v-for="(item, index) in dictOptions" :key="index" :title="item.label" :longitude="item.longitude" :latitude="item.latitude" :header="item.header" :phone="item.phone" :unitAddress="item.unitAddress"
                     :getPopupContainer="getParentContainer"
                     :value="item.value">
      {{ item.label }}
    </a-select-option>
  </a-select>
</template>
<script lang="ts">
import {computed, defineComponent, onMounted, ref, nextTick, watch, reactive} from 'vue';
import {defHttp} from '/@/utils/http/axios';
import {propTypes} from '/@/utils/propTypes';
import {useMessage} from '/@/hooks/web/useMessage';
import {useUserStore} from '/@/store/modules/user';

export default defineComponent({
  name: 'UnitTypeSelect',
  components: {},
  inheritAttrs: false,
  props: {
    value: propTypes.oneOfType([propTypes.string, propTypes.array]),
    placeholder: {
      type: String,
      default: '请选择',
      required: false,
    },
    readOnly: {
      type: Boolean,
      required: false,
      default: false,
    },
    options: {
      type: Array,
      default: () => [],
      required: false,
    },
    spliter: {
      type: String,
      required: false,
      default: ',',
    },
    popContainer: {
      type: String,
      default: '',
      required: false,
    },
    unitId: {
      type: String,
      required: false,
    },
    parentUnitId: {
      type: String,
      required: false,
    },
    rescueType: {
      type: String,
      required: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    mode: {
      type: Boolean,
      default: "multiple",
    },
  },
  emits: ['options-change', 'change', 'input', 'update:value'],
  setup(props, {emit, refs}) {
    const arrayValue = ref<any[]>(!props.value ? [] : props.value.split(props.spliter));
    const dictOptions = ref<any[]>([]);
    const userStore = useUserStore();
    const parentUnitId = ref();
    const Api = reactive<any>({
      getDeviceTypeCountByUnitId: '/sys/kanban/getDeviceTypeCountByUnitId',
    });
    onMounted(() => {
    });

    watch(() => props.value, (val) => {
        if (!val) {
          arrayValue.value = [];
        } else {
          arrayValue.value = props.value.split(props.spliter);
        }
      }
    );

    function onChange(selectedValue, label) {
      if (props.mode == "multiple") {
        emit('change', selectedValue.join(props.spliter));
        emit('update:value', selectedValue.join(props.spliter));
      } else {
        emit('change', selectedValue);
        emit('update:value', selectedValue);
      }
    }

    function getParentContainer(node) {
      if (document.getElementsByClassName('full')[0]) {
        return document.getElementsByClassName('full')[0]
      } else {
        return document.getElementsByClassName('kanban')[0]
      }
    }

    function loadDictOptions(param) {
      dictOptions.value = [];
      let params = {
        unitId: param,
      }
      defHttp.get({url: Api.getDeviceTypeCountByUnitId, params}, {isTransformResponse: false}).then((res) => {
        if (res.success) {
          dictOptions.value = res.result.map((item) => ({
            value: item.deviceType,
            label: item.deviceTypeName
          }));
        }
      })
    }

    function filterOption(input, option) {
      return option.children()[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    }

    return {
      dictOptions,
      onChange,
      arrayValue,
      getParentContainer,
      filterOption,
      loadDictOptions,
      parentUnitId,
    };
  },
});
</script>
<style lang="less" scoped>
// ::v-deep .ant-select-selection-item {
//   color: rgba(0, 0, 0, 0.25) !important;
// }
</style>
