<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <a-row>
        <a-col :span="24">
          <a-form-item label="分类名称:" :labelCol="labelCol" name="deviceCategory">
            <a-input placeholder="请输入分类名称" v-model:value="formState.deviceCategory"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="序值" :labelCol="labelCol" name="dataSort">
            <a-input placeholder="请输入序值" v-model:value="formState.dataSort"></a-input>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import { ref, reactive, defineEmits, defineExpose } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface';

const emits = defineEmits(['success', 'register', 'ok'])

const formState = reactive({
  deviceCategory: '',
  dataSort: "",
});
const formRef = ref();
const Api = reactive({
  edit: '/sys/dtDeviceCategory/editDeviceCategory',
});
const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 6 } });
const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
const confirmLoading = ref<boolean>(false);
//表单验证
const validatorRules = {
  deviceCategory: [{ required: true, message: '请输入分类名称' }],
  dataSort: [{ required: true, message: '请输入序值' }],
};
let ids = ref('')
function edit(e) {
  ids = ref(e.id)
  Object.assign(formState, e)
}
function submitForm() {
  let params = reactive({
    id: ids,
    dataSort: formState.dataSort,
    deviceCategory: formState.deviceCategory,
    deviceType: '',
    logoUrl: '',
  })
  formRef.value.validate().then(() => {
    defHttp.put({ url: Api.edit, params }).then((e) => {
      emits('ok');
    });
  }).catch((error: ValidateErrorEntity<any>) => {
    console.log('error', error);
  });
}
defineExpose({
  edit,
  submitForm
});
</script>

<style lang="less" scoped> .antd-modal-form {
   padding: 24px 24px 24px 24px;
 }
</style>
