<template>
  <span v-for="tag in tagsData">
    <a-checkable-tag
      :key="tag"
      :checked="selectedTags.indexOf(tag) > -1"
      @change="checked => handleChange(tag, checked)"
    >{{ tag }}</a-checkable-tag>
  </span>
</template>
<script lang="ts">
  import {
    defineComponent,
    PropType,
    ref,
    reactive,
    watchEffect,
    computed,
    unref,
    watch,
    onMounted,
    nextTick
  } from 'vue';
  import {propTypes} from '/src/utils/propTypes';
  import {LoadingOutlined} from '@ant-design/icons-vue';
  import {Tag} from 'ant-design-vue';

  export default defineComponent({
    name: 'ReasonTagSelect',
    inheritAttrs: false,
    components: {LoadingOutlined},
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.number, propTypes.array]),
    },
    emits: ['options-change', 'change', 'update:value'],
    setup(props, {emit, refs}) {
      const tagsData = ['Movies', 'Books', 'Music', 'Sports'];
      const selectedTags = ref([])
      //update-begin-author:taoyan date:20220404 for: 使用useRuleFormItem定义的value，会有一个问题，如果不是操作设置的值而是代码设置的控件值而不能触发change事件
      // 此处添加空值的change事件,即当组件调用地代码设置value为''也能触发change事件
      watch(() => props.value, (val) => {
          if (!val) {
            selectedTags.value = [];
          }
        }
      );

      function handleChange(tag, checked) {
        const nextSelectedTags = checked ? [...selectedTags.value, tag] : selectedTags.value.filter(t => t !== tag)
        selectedTags.value = nextSelectedTags
        emit('change', nextSelectedTags, tag, checked);
        emit('update:value', tag);
      }

      return {
        handleChange,
        selectedTags,
        tagsData,
      };
    },
  });
</script>


<style scoped lang="less">
  // update-begin--author:liaozhiyang---date:20230110---for：【QQYUN-7799】字典组件（原生组件除外）加上颜色配置
  .colorText {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    padding: 0 6px;
    border-radius: 8px;
    background-color: red;
    color: #fff;
    font-size: 12px;
  }

  // update-begin--author:liaozhiyang---date:20230110---for：【QQYUN-7799】字典组件（原生组件除外）加上颜色配置
</style>
