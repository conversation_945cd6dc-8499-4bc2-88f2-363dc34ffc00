<template>
  <TreeSelect
    :class="prefixCls"
    :value="treeValue"
    :treeData="treeData"
    allowClear
    labelInValue
    treeDefaultExpandAll
    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
    style="width: 100%"
    v-bind="attrs"
    @change="onChange"
  >
  </TreeSelect>
</template>

<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { propTypes } from '/@/utils/propTypes';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { TreeSelect } from 'ant-design-vue';

  enum Api {
    view = '/sys/sysDepart/queryMyDepartTreeSync',
    root = '/sys/sysDepart/queryMyDepartTreeSync',
    children = '/sys/sysDepart/queryMyDepartTreeSync',
  }

  const { prefixCls } = useDesign('j-tree-dict');
  const props = defineProps({
    // v-model:value
    value: propTypes.string.def(''),
    field: propTypes.string.def('id'),
    parentCode: propTypes.string.def(''),
    async: propTypes.bool.def(false),
  });
  const attrs = useAttrs();
  const emit = defineEmits(['change', 'update:value']);

  const treeData = ref<any[]>([]);
  const treeValue = ref<any>(null);

  watch(
    () => props.value,
    () => loadViewInfo(),
    { deep: true, immediate: true }
  );
  watch(
    () => props.parentCode,
    () => loadRoot(),
    { deep: true, immediate: true }
  );

  async function loadViewInfo() {
    if (!props.value || props.value == '0') {
      treeValue.value = { value: null, label: null };
    } else {
      let params = { field: props.field, val: props.value };
      let result = await defHttp.get({ url: Api.view, params });
      treeValue.value = {
        value: props.value,
        label: result.name,
      };
    }
  }

  async function loadRoot() {
    let params = {
      // async: props.async,
      // pcode: props.parentCode,
    };
    let result = await defHttp.get({ url: Api.root, params });
    treeData.value = [...result];
    handleTreeNodeValue(result);
  }

  function handleTreeNodeValue(result) {
    let storeField = 'orgCode';
    for (let i of result) {
      i.value = i[storeField];
      i.isLeaf = i.leaf;
      if (i.children && i.children.length > 0) {
        handleTreeNodeValue(i.children);
      }
    }
  }

  function onChange(value) {
    if (!value) {
      emitValue('');
    } else {
      emitValue(value.value);
    }
    treeValue.value = value;
  }

  function emitValue(value) {
    emit('change', value);
    emit('update:value', value);
  }
</script>

<style lang="less">
  //noinspection LessUnresolvedVariable
  @prefix-cls: ~'@{namespace}-j-tree-dict';

  .@{prefix-cls} {
  }
</style>
