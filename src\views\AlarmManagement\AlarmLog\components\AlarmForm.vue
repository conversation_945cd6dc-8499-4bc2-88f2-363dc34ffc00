<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState">
      <JFormContainer :disabled="disabled">
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-item label="选择单位" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="unitId"
                         v-bind="validateInfos.unitId">
              <UnitIdSelect v-model:value="formState.unitId" placeholder="请选择单位名称"
                            mode="default" @change="onUnitIdSelect"
                            :disabled="formState.id"></UnitIdSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="选择设备" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="deviceCode"
                         v-bind="validateInfos.deviceCode">
              <DeviceCodesSelect ref="DeviceCodesSelect1" v-model:value="formState.deviceCode" placeholder="请选择设备编号"
                                mode="default"
                                triggerChange="false"
                                @change="onDeviceCodesSelect"></DeviceCodesSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="告警编码" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="alarmCode"
                         v-bind="validateInfos.alarmCode">
              <a-input v-model:value="formState.alarmCode" placeholder="请输入告警编码"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="告警级别" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="alarmLevel"
                         v-bind="validateInfos.alarmLevel">
              <JSelectMultiple :placeholder="'请选择告警级别'"
                               v-model:value="formState.alarmLevel" @change="onJSelectMultiple"
                               dictCode="alarmLevel" mode="default" :triggerChange="false">
              </JSelectMultiple>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="告警类型" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="alarmType"
                         v-bind="validateInfos.alarmType">
              <AlarmTypeSelect v-model:value="formState.alarmType" placeholder="请选择告警类型"
                               mode="default"></AlarmTypeSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="告警描述" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="alarmDescription"
                         v-bind="validateInfos.alarmDescription">
              <a-textarea v-model:value="formState.alarmDescription" placeholder="请输入告警描述"/>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import {Form} from 'ant-design-vue';
import {ref, nextTick, defineEmits, reactive, defineExpose} from 'vue';
import {getDictItemList} from '@/hooks/params/param'
import MonthSelect from '/@/components/Form/src/jeecg/components/serchselect/MonthSelect.vue';
import {defHttp} from '/@/utils/http/axios';
import UnitIdSelect from "@/components/Form/src/jeecg/components/serchselect/UnitIdSelect.vue";
import DeviceCodesSelect
  from "@/components/Form/src/jeecg/components/serchselect/DeviceCodesSelect.vue";
import JSelectMultiple from "../../../../components/Form/src/jeecg/components/JSelectMultiple.vue";
import AlarmTypeSelect
  from '/@/components/Form/src/jeecg/components/serchselect/AlarmTypeSelect.vue';
import {useMessage} from "@/hooks/web/useMessage";


const {createMessage} = useMessage();
const confirmLoading = ref<boolean>(false);
const formState = reactive({
  alarmCode: '',
  alarmDescription: '',
  alarmLevel: '',
  alarmType: '',
  deviceCode: '',
  unitId: '',
});
const useForm = Form.useForm;
const activeKey = ref('1')
const emits = defineEmits(['success', 'register', 'ok'])
const labelCol = ref<any>({xs: {span: 24}, sm: {span: 6}});
const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 18}});
const Api = reactive<any>({
  add: '/sys/dtAlarmDetail/addAlarmDetail',
});
const DeviceCodesSelect1 = ref()
//表单验证
const validatorRules = {
  alarmCode: [{required: true, message: '请输入告警编码!'}],
  unitId: [{required: true, message: '请选择单位名称!', trigger: 'change'}],
  deviceCode: [{required: true, message: '请选择设备编号!', trigger: 'change'}],
  alarmLevel: [{required: true, message: '请选择告警级别!', trigger: 'change'}],
  alarmType: [{required: true, message: '请选择告警类型!', trigger: 'change'}],
};
const {
  resetFields,
  validate,
  validateInfos
} = useForm(formState, validatorRules, {immediate: false});
function onUnitIdSelect(e){
  formState.deviceCode = undefined
  DeviceCodesSelect1.value.arrayValue = undefined
  DeviceCodesSelect1.value.loadDictOptions(e);
}
function onDeviceCodesSelect(e,n){
  formState.deviceCode = e
}
function add() {
  edit({});
}

function edit(record) {
  nextTick(() => {
    resetFields();
    const tmpData = {};
    Object.keys(formState).forEach((key) => {
      if (record.hasOwnProperty(key)) {
        tmpData[key] = record[key]
      }
    })
    Object.assign(formState, tmpData);
    DeviceCodesSelect1.value.loadDictOptions(record.unitId);
  });
}

async function submitForm() {
  await validate();
  confirmLoading.value = true;
  let httpurl = '';
  let method = '';
  //时间格式化
  let model = Object.assign({}, formState);
  httpurl += Api.add;
  method = 'post';
  defHttp.request({
    url: httpurl,
    params: model,
    method: method,
  }, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      createMessage.success(res.message);
      emits('ok');
    } else {
      createMessage.warning(res.message);
    }
  }).finally(() => {
    confirmLoading.value = false;
  });
}

defineExpose({
  add,
  edit,
  submitForm,
  activeKey
});

</script>

<style lang="less" scoped>
.chooseUnit {
  color: rgb(0, 153, 255);
  cursor: pointer;
}

.antd-modal-form {
  padding: 24px 24px 24px 24px;
}

.uploadBoxs {
  width: 50%;
  height: 150px;
  cursor: pointer;
  border: 1px dashed #1296db;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 10px;

  img {
    width: 30px;
    height: 30px;
  }
}
</style>
