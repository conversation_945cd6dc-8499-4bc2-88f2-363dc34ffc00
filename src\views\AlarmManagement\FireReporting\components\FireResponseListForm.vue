<template>
  <a-spin :spinning="confirmLoading">
    <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="24">
            <a-form-item label="响应主体" name="responseBody" v-bind="validateInfos.responseBody"
                         :labelCol="labelCol" :wrapperCol="wrapperCol">
              <ResponseBodySelect ref="ResponseBodySelect1" v-model:value="formState.responseBody"
                                  placeholder="请选择响应主体"
                                  @change="onResponseBodySelect"></ResponseBodySelect>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="响应内容" name="responseContext" v-bind="validateInfos.responseContext"
                         :labelCol="labelCol" :wrapperCol="wrapperCol">
              <ResponseContextSelect ref="ResponseContextSelect1" v-model:value="formState.responseContext"
                                     placeholder="请选择响应内容"
                                     @change="onResponseContextSelect"></ResponseContextSelect>

            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="响应时间" name="responseTime" :labelCol="labelCol1" :wrapperCol="wrapperCol1">
              <DateTimemmSelect v-model:value="formState.responseTime" placeholder="请选择响应时间"
                                @change="onStartDateTimeSelect"></DateTimemmSelect>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import {Form, Descriptions, Image} from 'ant-design-vue';
  import * as echarts from "echarts";
  import {ref, reactive, onMounted, defineProps, defineExpose, nextTick, defineEmits} from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import {useMessage} from '/@/hooks/web/useMessage';
  import DateTimemmSelect from '/@/components/Form/src/jeecg/components/serchselect/DateTimemmSelect.vue';
  import ResponseBodySelect from '/@/components/Form/src/jeecg/components/serchselect/ResponseBodySelect.vue';
  import ResponseContextSelect from '/@/components/Form/src/jeecg/components/serchselect/ResponseContextSelect.vue';

  const props = defineProps(["disabled"])
  const emit = defineEmits(['success', 'register', 'ok'])
  const confirmLoading = ref<boolean>(false);
  const Api = reactive<any>({
    add: '/sys/dtFireReportResponse/addFireReportResponse',
    checkFireReportResponse: '/sys/dtFireReportResponse/checkFireReportResponse',
  });
  const formState = reactive({
    responseBody: '',
    responseBody1: '',
    responseContext: '',
    responseContext1: '',
    responseTime: '',
  });
  const labelCol = reactive({
    xs: {span: 24},
    sm: {span: 4},
  })
  const wrapperCol = reactive({
    xs: {span: 24},
    sm: {span: 20},
  })
  const labelCol1 = reactive({
    xs: {span: 24},
    sm: {span: 8},
  })
  const wrapperCol1 = reactive({
    xs: {span: 24},
    sm: {span: 16},
  })
  const ResponseBodySelect1 = ref();
  const ResponseContextSelect1 = ref();
  const {createMessage} = useMessage();
  const validatorRules = {
    responseBody: [{required: true, message: '请选择响应主体'}],
    responseContext: [{required: true, message: '请选择响应内容', trigger: 'change'}],
  };
  const useForm = Form.useForm;
  const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

  onMounted(() => {
    //初始化字典选项
  });

  function onResponseBodySelect(e) {
    formState.responseBody = e
  }

  function onResponseContextSelect(e, n) {
    formState.responseContext = e
  }

  function onStartDateTimeSelect() {

  }

  function add() {
    edit({});
  }

  function edit(record) {
    nextTick(() => {
      resetFields();
      Object.assign(formState, record);
      formState.responseTime = datetimePickerFormat(undefined);
      formState.responseContext = "已到达现场"
      ResponseBodySelect1.value.loadData(record)
      ResponseBodySelect1.value.arrayValue1 = undefined
      ResponseContextSelect1.value.arrayValue1 = undefined
    });
  }

  function datetimePickerFormat(aDate, timeAppend) {
    aDate = aDate != undefined ? aDate : new Date()
    let year = aDate.getFullYear()
    let month = aDate.getMonth() + 1
    month = month < 10 ? ('0' + month) : month
    let day = aDate.getDate()
    day = day < 10 ? ('0' + day) : day
    if (!timeAppend) {
      let hh = aDate.getHours() < 10
        ? '0' + aDate.getHours()
        : aDate.getHours()
      let mm = aDate.getMinutes() < 10
        ? '0' + aDate.getMinutes()
        : aDate.getMinutes()
      let ss = aDate.getSeconds() < 10
        ? '0' + aDate.getSeconds()
        : aDate.getSeconds()
      timeAppend = hh + ':' + mm
    }
    return year + '-' + month + '-' + day + ' ' + timeAppend
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    await validate();
    if (formState.responseBody == '其他') {
      if (ResponseBodySelect1.value.arrayValue1) {
        formState.responseBody = ResponseBodySelect1.value.arrayValue1
      } else {
        createMessage.warning("请输入响应主体！");
        return
      }
    }
    if (formState.responseContext == '其他') {
      if (ResponseContextSelect1.value.arrayValue1) {
        formState.responseContext = ResponseContextSelect1.value.arrayValue1
      } else {
        createMessage.warning("请输入响应内容！");
        return
      }
    }
    confirmLoading.value = true;
    let params1 = {
      reportId: formState.id,
      responseBody: formState.responseBody,
      responseContext: formState.responseContext,
    };
    defHttp.put({url: Api.checkFireReportResponse, params: params1}, {isTransformResponse: false}).then((res) => {
      if (res.success) {
        if (res.result) {
          createMessage.warning("响应内容已存在！");
        } else {
          let params = {
            reportId: formState.id,
            responseBody: formState.responseBody,
            responseContext: formState.responseContext,
            responseTime: formState.responseTime + ':00',
          };
          defHttp.post({url: Api.add, params}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
              createMessage.success(res.message);
              emit('ok');
            } else {
              createMessage.warning(res.message);
            }
          }).finally(() => {
            confirmLoading.value = false;
          });
        }
      } else {
        createMessage.warning(res.message);
      }
    }).finally(() => {
      confirmLoading.value = false;
    });


  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  /deep/ .ant-descriptions-header {
    margin-bottom: 4px;
  }

  .charts {
    width: 100%;
    height: 200px;
    border: 1px solid #f0f0f0;
  }

  .video {
    width: 200px;
    height: 100px;
    float: left;
    margin-right: 5px;
    position: relative;
    background: #000;
  }

  .video .videos {
    width: 100%;
    max-height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  ::v-deep .ant-radio-wrapper {
    line-height: 2.3;
  }
</style>
