<!-- 派单记录 DispatchRecord -->
<template>
  <a-card :bordered="false">
    <!--自定义查询区域-->
    <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
      <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="6">
            <a-form-item label="单位名称">
              <a-input placeholder="请输入单位名称" v-model:value="queryParam.unitName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item label="设备名称">
              <a-input placeholder="请输入设备名称" v-model:value="queryParam.deviceName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item label="告警类型">
              <JSelectMultiple :placeholder="'请选择告警类型'"
                               v-model:value="queryParam.alarmType"
                               dictCode="alarmType" mode="default" :triggerChange="false">
              </JSelectMultiple>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item label="开始时间">
              <DateTimeSelect v-model:value="queryParam.startTime" placeholder="请选择开始时间"
                              @change="onStartDateTimeSelect"></DateTimeSelect>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item label="结束时间">
              <DateTimeSelect v-model:value="queryParam.endTime" placeholder="请选择结束时间"
                              @change="onEndDateTimeSelect"></DateTimeSelect>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery" style="margin-right: 5px">查询
            </a-button>
            <a-button preIcon="ant-design:sync-outlined" @click="searchReset" style="margin-left: 8px">重置
            </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 操作按钮区域 -->
    <div class="table-operator">

    </div>
    <!-- table区域-begin -->
    <JVxeTable
      ref="tableRef"
      bordered
      row-number
      keep-source
      resizable
      :maxHeight="484"
      :loading="loading"
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      style="margin-top: 8px"
      @pageChange="handlePageChange">
      <template #alarmType="props">
        <!--1疑似火警 2故障 3隐患 4其他-->
        <span v-if="props.row.alarmType == '1'">疑似火警</span>
        <span v-if="props.row.alarmType == '2'">故障</span>
        <span v-if="props.row.alarmType == '3'">隐患</span>
        <span v-if="props.row.alarmType == '4'">其他</span>
      </template>
      <template #overdueFlag="props">
        <span v-if="props.row.overdueFlag == '0'">否</span>
        <span v-if="props.row.overdueFlag == '1'">是</span>
      </template>
      <template #orderStatus="props">
        <!--1处理中 2待审核 3通过-->
        <span v-if="props.row.orderStatus == '1'">处理中</span>
        <span v-if="props.row.orderStatus == '2'">待审核</span>
        <span v-if="props.row.orderStatus == '3'">通过</span>
      </template>
      <template #action="props">
        <a v-if="hasPermission('AlarmManagement:DispatchRecord:components:MaintenanceModal')">
          <a @click="handleMaintenance(props.row)" v-if="props.row.orderStatus == '1'">维修</a>
        </a>
        <a @click="handleToExamine(props.row)" v-if="props.row.orderStatus == '2'">审核</a>
        <a v-if="hasPermission('AlarmManagement:DispatchRecord:components:ViewModal')">
          <a @click="handleView(props.row)" v-if="props.row.orderStatus == '3'">查看</a>
        </a>
      </template>
    </JVxeTable>
  </a-card>

  <MaintenanceModal ref="MaintenanceModal1" @ok="loadData"/>
  <ToExamineModal ref="ToExamineModal1" @ok="loadData"/>
  <ViewModal ref="ViewModal1"/>
</template>

<script lang="ts" setup>
  import {onMounted, ref, reactive} from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import {filterObj} from '/@/utils/common/compUtils';
  import {initDictOptions} from '/@/utils/dict';
  import {Popconfirm} from 'ant-design-vue';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import DateTimeSelect from '/@/components/Form/src/jeecg/components/serchselect/DateTimeSelect.vue';

  import MaintenanceModal from './components/MaintenanceModal.vue';
  import ToExamineModal from './components/ToExamineModal.vue';
  import ViewModal from './components/ViewModal.vue';
  import {useUserStore} from '/@/store/modules/user';
  import { usePermission } from '/@/hooks/web/usePermission';

  const { hasPermission } = usePermission();

  const userStore = useUserStore();

  const MaintenanceModal1 = ref();
  const ToExamineModal1 = ref();
  const ViewModal1 = ref();


  const labelCol = reactive({
    xs: {span: 24},
    sm: {span: 8},
  })
  const wrapperCol = reactive({
    xs: {span: 24},
    sm: {span: 16},
  })
  const queryParam = ref<any>({});
  const loading = ref<boolean>(false);
  const getInput = (e: string): void => {
    console.log(e);
  }
  const columns = ref([
    {
      title: '单位名称',
      key: 'unitName',
      minWidth: 150,
      align: "center",
    },
    {
      title: '告警内容',
      key: 'alarmDescription',
      minWidth: 200,
      align: "center",
    },
    {
      title: '设备类型',
      key: 'deviceTypeName',
      minWidth: 150,
      align: "center",
    },
    {
      title: '告警类型',
      key: 'alarmType',
      minWidth: 150,
      align: "center",
      type: 'slot',
      slotName: 'alarmType',
    },
    {
      title: '设备名称',
      key: 'deviceName',
      minWidth: 180,
      align: "center",
    },
    {
      title: '告警时间',
      key: 'alarmTime',
      minWidth: 180,
      align: "center",
    },
    {
      title: '派单时间',
      key: 'orderTime',
      minWidth: 180,
      align: "center",
    },
    {
      title: '工单状态',
      key: 'orderStatus',
      minWidth: 150,
      align: "center",
      type: 'slot',
      slotName: 'orderStatus',
    },
    {
      title: '指派人员',
      key: 'repairRealname',
      minWidth: 150,
      align: "center",
    },
    {
      title: '操作',
      type: "slot",
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: 100,
      slotName: 'action',
    },
  ]);
  const Api = reactive<any>({
    list: '/sys/dtAlarmOrder/pageList',
  });
  const dataSource = ref<any>([]);
  const pagination = ref<any>({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30'],
    showTotal: (total, range) => {
      return range[0] + '-' + range[1] + ' 共' + total + '条';
    },
    showQuickJumper: true,
    showSizeChanger: true,
    total: 0,
  });

  const iSorter = ref<any>({column: 'createTime', order: 'desc'});
  const iFilters = ref<any>({});

  /**
   * 获取查询参数
   */
  function getQueryParams() {
    let params = Object.assign(queryParam.value, iSorter.value, iFilters.value);
    params.pageNo = pagination.value.current;
    params.pageSize = pagination.value.pageSize;
    return filterObj(params);
  }

  // 开始时间
  function onStartDateTimeSelect(e) {
  }

  // 结束时间
  function onEndDateTimeSelect(e) {
  }


  // 当分页参数变化时触发的事件
  function handlePageChange(event) {
    // 重新赋值
    pagination.value.current = event.current;
    pagination.value.pageSize = event.pageSize;

    // 查询数据
    loadData();
  }


  function loadData(arg) {
    if (arg === 1) {
      pagination.value.current = 1;
    }
    loading.value = true;
    let params = getQueryParams();
    defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
      if (res.success) {
        dataSource.value = res.result.records;
        if (res.result && res.result.total) {
          pagination.value.total = res.result.total;
        } else {
          pagination.value.total = 0;
        }
      }
    }).finally(() => {
      loading.value = false;
    });
  }

  //查询
  function searchQuery() {
    loadData(1);
  }

  //重置
  function searchReset() {
    queryParam.value = {};
    queryParam.value.unitId = userStore.getLoginInfo.unitInfo.id;
    loadData(1);
  }

  function handleMaintenance(e){
    MaintenanceModal1.value.disableSubmit = false;
    MaintenanceModal1.value.edit(e);
  }

  function handleToExamine(e){
    ToExamineModal1.value.disableSubmit = false;
    ToExamineModal1.value.edit(e);
  }

  //查看
  function handleView(e) {
    ViewModal1.value.disableSubmit = false;
    ViewModal1.value.edit(e);
  }


  function loadDelete(alarmStatus, ids) {
    defHttp.put({url: Api.delete, params: {alarmStatus: alarmStatus, ids: ids}}).then((res) => {
      loadData(1);
    });
  }

  async function initDictConfig() {
    queryParam.value.unitId = userStore.getLoginInfo.unitInfo.id;
  }

  function datetimePickerFormat(aDate, timeAppend) {
    aDate = aDate != undefined ? aDate : new Date()
    let year = aDate.getFullYear()
    let month = aDate.getMonth() + 1
    month = month < 10 ? ('0' + month) : month
    let day = aDate.getDate()
    day = day < 10 ? ('0' + day) : day
    if (!timeAppend) {
      let hh = aDate.getHours() < 10
        ? '0' + aDate.getHours()
        : aDate.getHours()
      let mm = aDate.getMinutes() < 10
        ? '0' + aDate.getMinutes()
        : aDate.getMinutes()
      let ss = aDate.getSeconds() < 10
        ? '0' + aDate.getSeconds()
        : aDate.getSeconds()
      timeAppend = hh + ':' + mm + ':' + ss
    }
    return year + '-' + month + '-' + day + ' ' + timeAppend
  }

  function handleSuccess() {
    loadData(1);
  }

  onMounted(() => {
    //初始化字典选项
    initDictConfig();
    //初始加载页面
    loadData();
  });


</script>
<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0px;

    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 0;
      white-space: nowrap;
    }

    .ant-form-item {
      margin-bottom: 8px !important;
    }
  }
</style>
