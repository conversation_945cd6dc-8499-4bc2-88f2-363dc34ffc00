<template>
    <a-spin :spinning="confirmLoading">
        <a-form class="antd-modal-form" ref="formRef" :model="formState">
            <JFormContainer :disabled="disabled">
                <a-row :gutter="24">
                    <a-col :span="24">
                        <a-form-item label="指挥主体" name="zhzt" :wrapperCol="wrapperCol" :labelCol="labelCol"
                                     v-bind="validateInfos.zhzt">
                            <a-input v-model:value="formState.zhzt" placeholder="请输入指挥主体" :disabled="isFlag"/>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="名称" name="mc" :wrapperCol="wrapperCol" :labelCol="labelCol"
                                     v-bind="validateInfos.mc">
                            <a-input v-model:value="formState.mc" placeholder="请输入名称"/>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="联系方式" name="lxfs" :wrapperCol="wrapperCol" :labelCol="labelCol"
                                     v-bind="validateInfos.lxfs">
                            <a-input v-model:value="formState.lxfs" placeholder="请输入联系方式"/>
                        </a-form-item>
                    </a-col>
                    <a-col :span="24">
                        <a-form-item label="序值" name="sort" :wrapperCol="wrapperCol" :labelCol="labelCol"
                                     v-bind="validateInfos.sort">
                            <a-input-number v-model:value="formState.sort" placeholder="请输入序值" :min="0" :precision="0"/>
                        </a-form-item>
                    </a-col>
                </a-row>
            </JFormContainer>
        </a-form>
    </a-spin>
</template>

<script lang="ts" setup>
    import {Form} from 'ant-design-vue';
    import {ref, nextTick, defineEmits, reactive, defineExpose} from 'vue';
    import {getDictItemList} from '@/hooks/params/param'
    import MonthSelect from '/@/components/Form/src/jeecg/components/serchselect/MonthSelect.vue';
    import {defHttp} from '/@/utils/http/axios';
    import {useMessage} from '/@/hooks/web/useMessage';


    const isFlag = ref(false)
    const {createMessage} = useMessage();
    const emit = defineEmits(['success', 'register', 'ok'])
    const confirmLoading = ref<boolean>(false);
    const formState = reactive({
        id: "",
        zhzt: "",
        mc: "",
        lxfs: "",
        sort: "",
    });
    const useForm = Form.useForm;
    const activeKey = ref('1')
    const loading = ref<boolean>(false);
    const Api = reactive<any>({
        add: '/sys/dtZhjgry/addZhjgry',
        edit: '/sys/dtZhjgry/editZhjgry',
    });
    const validatorRules = {
        zhzt: [{required: true, message: '请输入指挥主体!'}],
        mc: [{required: true, message: '请输入名称!'}],
    };
    //update-begin---author:wangshuai ---date:20220616  for：报表示例验证修改------------
    const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});
    //update-end---author:wangshuai ---date:20220616  for：报表示例验证修改------------
    const labelCol = ref<any>({xs: {span: 24}, sm: {span: 6}});
    const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 18}});

    function add() {
        edit({});
    }

    function edit(record) {
        nextTick(() => {
            resetFields();
            //赋值
            const tmpData = {};
            Object.keys(formState).forEach((key) => {
                if(record.hasOwnProperty(key)){
                    tmpData[key] = record[key]
                }
            })
            Object.assign(formState, tmpData);
        });
    }

    async function submitForm() {
        await validate();
        confirmLoading.value = true;
        let httpurl = '';
        let method = '';
        //时间格式化
        let model = Object.assign({}, formState);
        if (!model.id) {
            httpurl += Api.add;
            method = 'post';
        } else {
            httpurl += Api.edit;
            method = 'put';
        }
        defHttp.request({url: httpurl, params: model, method: method}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                createMessage.success(res.message);
                emit('ok');
            } else {
                createMessage.warning(res.message);
            }
        }).finally(() => {
            confirmLoading.value = false;
        });
    }

    defineExpose({
        add,
        edit,
        submitForm,
        activeKey
    });

</script>

<style lang="less" scoped>
    .chooseUnit {
        color: rgb(0, 153, 255);
        cursor: pointer;
    }

    .antd-modal-form {
        padding: 24px 24px 24px 24px;
    }

    .uploadBoxs {
        width: 50%;
        height: 150px;
        cursor: pointer;
        border: 1px dashed #1296db;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        border-radius: 10px;

        img {
            width: 30px;
            height: 30px;
        }
    }
</style>
