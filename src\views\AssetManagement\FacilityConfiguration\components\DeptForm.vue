<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <a-row>
        <a-col :span="12">
          <a-form-item label="设备分类" name="deviceCategory" :labelCol="labelCol">
            <JSelectMultiple @change="getSelectCategory" :disabled="is == 1" v-model:value="formState.deviceCategory"
              placeholder="请选择设备分类" :options="deviceCategoryOptions" mode="default" :triggerChange="false">
            </JSelectMultiple>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="设备类型" name="deviceType" :labelCol="labelCol">
            <JSelectMultiple v-model:value="formState.deviceType" :disabled="is == 1" placeholder="请选择设备类型"
              :options="deviceTypeOptions" mode="default" :triggerChange="false"></JSelectMultiple>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="参数编码" :labelCol="labelCol" name="paramCode">
            <a-input placeholder="请输入参数编码" :disabled="is == 1" v-model:value="formState.paramCode"></a-input>
          </a-form-item>
        </a-col><a-col :span="12">
          <a-form-item label="参数名称" :labelCol="labelCol" name="paramName">
            <a-input placeholder="请输入参数名称" v-model:value="formState.paramName"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="参数别名" :labelCol="labelCol" name="aliasCode">
            <a-input placeholder="请输入参数别名" :disabled="is == 1" v-model:value="formState.aliasCode"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="名称别名" :labelCol="labelCol">
            <a-input placeholder="请输入名称别名" v-model:value="formState.aliasName"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="参数描述" :labelCol="labelCols">
            <a-input placeholder="请输入参数描述" v-model:value="formState.paramDesc"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="参数分类" :labelCol="labelCol" name="paramType">
            <JSelectMultiple :disabled="is == 1" v-model:value="formState.paramType" placeholder="请选择参数分类"
              :options="paramTypeOptions" mode="default" :triggerChange="false"></JSelectMultiple>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="单位" :labelCol="labelCol">
            <a-input placeholder="请输入单位" v-model:value="formState.unit"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="公式" :labelCol="labelCols">
            <a-textarea placeholder="请输入公式" v-model:value="formState.formula" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="数据类型" :labelCol="labelCol">
            <a-input placeholder="请输入数据类型" v-model:value="formState.dataType"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="数据来源" :labelCol="labelCol">
            <a-input placeholder="请输入数据来源" v-model:value="formState.dataSource"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="序值" :labelCol="labelCol">
            <a-input placeholder="请输入序值" v-model:value="formState.paramSort"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" :labelCol="labelCols">
            <a-textarea placeholder="请输入备注" v-model:value="formState.remark" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, defineEmits, onMounted, defineExpose } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import { deviceCategoryList, getDeviceTypeListById, getDictItemList } from '@/hooks/params/param'
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface';
// 规则级别
const ruleLevelOptions = ref<any>([])
// 设备分类
const deviceCategoryOptions = ref<any>([])
const deviceTypeOptions = ref<any>([])
const paramTypeOptions = ref<any>([])


onMounted(() => {
  // getDictItemList('alarmRuleLevel').then(e => ruleLevelOptions.value = e)
  deviceCategoryList.then(e => deviceCategoryOptions.value = e)
  getDictItemList('paramType').then(e => paramTypeOptions.value = e)
})
function getSelectCategory(e) {
  formState.deviceType = ''
  deviceTypeOptions.length = 0
  getDeviceTypeListById(e).then(e => deviceTypeOptions.value = e)
}

const emit = defineEmits(['success', 'register', 'ok'])


const formState = reactive({
  deviceCategory: '',
  deviceType: '',
  paramCode: '',
  paramName: '',
  aliasCode: '',
  aliasName: '',
  paramDesc: '',
  paramType: '',
  unit: '',
  formula: '',
  dataType: '',
  dataSource: '',
  paramSort: '',
  remark: '',
});
const formRef = ref();
const Api = reactive({
  add: '/sys/dtParamTemplate/addParamTemplate',
  edit: '/sys/dtParamTemplate/editParamTemplate',
});
const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 6 } });
const labelCols = ref<any>({ xs: { span: 24 }, sm: { span: 3 } });
const confirmLoading = ref<boolean>(false);
//表单验证
const validatorRules = {
  deviceCategory: [{ required: true, message: '请选择设备分类', trigger: 'change' }],
  deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  paramCode: [{ required: true, message: '请输入参数编码' }],
  paramName: [{ required: true, message: '请输入参数名称' }],
  aliasCode: [{ required: true, message: '请输入参数别名' }],
  paramType: [{ required: true, message: '请选择参数分类', trigger: 'change' }],
};

function add() {
  is.value = 0
  Object.assign(formState, {
    deviceCategory: '',
    deviceType: '',
    paramCode: '',
    paramName: '',
    aliasCode: '',
    aliasName: '',
    paramDesc: '',
    paramType: '',
    unit: '',
    formula: '',
    dataType: '',
    dataSource: '',
    paramSort: '',
    remark: '',
  });

}
function submitForm() {
  if (!is.value) {
    let params = formState
    formRef.value.validate().then(() => {
      defHttp.post({ url: Api.add, params }).then((e) => {
        emit('ok');
      });
    }).catch((error: ValidateErrorEntity<any>) => {
      console.log('error', error);
    });
  } else {
    let params = formState
    formRef.value.validate().then(() => {
      defHttp.put({ url: Api.edit, params }).then((e) => {
        emit('ok');
      });
    }).catch((error: ValidateErrorEntity<any>) => {
      console.log('error', error);
    });
  }
}

let is = ref(0)
function edit(record) {
  is.value = 1
  console.log(record);

  getDeviceTypeListById(record.deviceCategory).then(e => deviceTypeOptions.value = e)

  nextTick(() => {
    Object.assign(formState, record);
  });
}

defineExpose({
  add,
  edit,
  submitForm,
});

</script>

<style lang="less" scoped>
.antd-modal-form {
  padding: 24px 24px 24px 24px;
}
</style>
