<template>
    <a-month-picker
            :placeholder="placeholder"
            :value="value"
            :size="size"
            :disabled="disabled"
            :allowClear="allowClear"
            style="width: 100%;"
            @change="handleChange"
    />
</template>

<script>
    import {defineComponent, ref, watch} from 'vue';
    import dayjs from 'dayjs';
    /**
     * 用于时间-time组件的范围查询
     */
    export default defineComponent({
        name: "MonthSelect",
        props: ["value", "placeholder", 'disabled', 'size'],
        emits: ['change', 'update:value'],
        setup(props, {emit}) {
            const value = ref(undefined)
            const placeholder = ref("请选择月份")
            watch(() => props.value, (val) => {
                if (val) {
                    value.value = dayjs(val, "YYYY-MM");
                }
            }, {immediate: true});


            function handleChange(date, dateString) {
                value.value = dateString
                emit('change', dateString);
                emit('update:value', dateString);
            }

            return {
                value,
                placeholder,
                handleChange
            }
        }
    });
</script>
