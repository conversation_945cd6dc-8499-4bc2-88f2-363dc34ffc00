<template>
  <a-spin :spinning="confirmLoading">
    <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState"
            :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="24">
            <a-descriptions size="small" :column="2" title="1.火灾上报信息" bordered>
              <a-descriptions-item label="简报" :span="2">{{ formState.reportDesc }}
              </a-descriptions-item>
              <a-descriptions-item label="上报时间">{{ formState.reportTime }}</a-descriptions-item>
              <a-descriptions-item label="上报单位">{{ formState.unitName }}</a-descriptions-item>
              <a-descriptions-item label="火势大小"> {{ formState.fireSize }}</a-descriptions-item>
              <a-descriptions-item label="是否爆炸"> {{ formState.explosionFlag }}
              </a-descriptions-item>
              <a-descriptions-item label="是否蔓延"> {{ formState.spreadFlag }}
              </a-descriptions-item>
              <a-descriptions-item label="是否有人员被困"> {{ formState.stuckFlag }}
              </a-descriptions-item>
              <a-descriptions-item label="是否有人员受伤"> {{ formState.injuredFlag }}
              </a-descriptions-item>
              <a-descriptions-item label="燃烧物质"> {{ formState.material }}</a-descriptions-item>
              <a-descriptions-item label="被困人员数量和位置" :span="2"> {{ formState.position }}
              </a-descriptions-item>
              <a-descriptions-item label="上报人"> {{ formState.reportRealname }}
              </a-descriptions-item>
              <a-descriptions-item label="上报人电话"> {{ formState.reportPhone }}
              </a-descriptions-item>

              <a-descriptions-item label="现场图片" :span="2">
                <Image.PreviewGroup>
                  <Image :src="item" :width="100" v-for="(item, index) in formState.pictureUrl"
                         :key="index"/>
                </Image.PreviewGroup>
              </a-descriptions-item>
              <a-descriptions-item label="现场视频" :span="2">
                <div class="video">
                  <video
                    ref="videoPlayer"
                    muted
                    loop
                    autoplay
                    controls
                    type="video/mp4"
                    class="videos"
                    :src="formState.videoUrl"
                  >
                  </video>
                </div>
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
          <a-col :span="24">
            <a-descriptions title="2.消防安全" bordered size="small">
              <a-descriptions-item label="最新一次火灾上报" :span="1.5">
                {{ data.bestNewReportTime }}
              </a-descriptions-item>
              <a-descriptions-item label="最新火灾风险评分" :span="1.5"> {{ data.bestNewScore }}
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
          <a-col :span="24">
            <a-descriptions title="3.近七天告警趋势" size="small"></a-descriptions>
            <div id="charts1" class="charts"></div>
          </a-col>
          <!--<a-col :lg="24">-->
          <!--<a-descriptions title="4.近七天巡检状况" size="small"></a-descriptions>-->
          <!--<div id="charts2" class="charts"></div>-->
          <!--</a-col>-->
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import {Form, Descriptions, Image} from 'ant-design-vue';
import * as echarts from "echarts";
import {
  defineComponent,
  ref,
  reactive,
  onMounted,
  defineProps,
  defineExpose,
  UnwrapRef,
  nextTick,
  defineEmits
} from 'vue';
import {defHttp} from '/@/utils/http/axios';
import JFormContainer from '@/components/Form/src/jeecg/components/JFormContainer.vue';
import {getFileAccessHttpUrl} from '/@/utils/common/compUtils';

const props = defineProps(["disabled"])
const emits = defineEmits(['success', 'register', 'ok'])
const confirmLoading = ref<boolean>(false);
const validatorRules = {};
const Api = reactive<any>({
  getFireReportBriefing: '/sys/dtFireReport/getFireReportBriefing',
});
const formState = reactive({});
let data = ref([])
const useForm = Form.useForm;
const {
  resetFields,
  validate,
  validateInfos
} = useForm(formState, validatorRules, {immediate: false});
onMounted(() => {
  //初始化字典选项
});

function add() {
  edit({});
}

function edit(record) {
  nextTick(() => {
    loadData(record)
  });
}

function loadData(record) {
  let params = {
    id: record.id
  };
  defHttp.get({
    url: Api.getFireReportBriefing,
    params
  }, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      if (res.result.fireReportModel.pictureUrl) {
        res.result.fireReportModel.pictureUrl = res.result.fireReportModel.pictureUrl.split(",")
        for (let i in res.result.fireReportModel.pictureUrl) {
          res.result.fireReportModel.pictureUrl[i] = getFileAccessHttpUrl(window._CONFIG['imagesUrl'] + res.result.fireReportModel.pictureUrl[i])
        }

      }
      if (res.result.fireReportModel.videoUrl) {
        res.result.fireReportModel.videoUrl = getFileAccessHttpUrl(window._CONFIG['imagesUrl'] + res.result.fireReportModel.videoUrl)
      } else {
        res.result.fireReportModel.videoUrl = ""
      }
      Object.assign(formState, res.result.fireReportModel);
      let vakyes = res.result.alarmUnitCountModel.alarmCountList;
      res.result.alarmUnitCountModel.alarmCountList = vakyes.map(vakyes => vakyes === null ? 0 : vakyes);
      data.value = res.result
    }
  }).finally(() => {
    initChart()
  });
}

function initChart() {
  let chart = echarts.init(document.getElementById("charts1"));
  let option = {
    grid: {
      top: "20%",
      bottom: "5%",
      left: "5%",
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      splitLine: {
        show: false,
      },
      data: data.value.alarmUnitCountModel.dateList,
      axisLabel: {},
      axisTick: {
        show: true
      }
    },
    tooltip: {
      trigger: "axis",
    },
    yAxis: {
      type: 'value',
      name: '告警数量（次）',
      nameTextStyle: {
        color: '#000',
        padding: [0, 0, 0, -10] // 四个数字分别为上右下左与原位置距离
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#013A3F'
        }
      },
      axisLine: {
        show: true
      },
      axisLabel: {
        color: '#000'
      }
    },
    series: [
      {
        name: '',
        type: 'line',
        stack: 'Total',
        data: data.value.alarmUnitCountModel.alarmCountList,
        smooth: true,
        symbol: 'none'
      },
    ]
  };
  chart.clear();
  chart.setOption(option, true);
  let timer = null;
  window.addEventListener('resize', function () {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      let chartDom = document.getElementById('charts1')
      if (chartDom) {
        let myChart = echarts.getInstanceByDom(chartDom);
        if (myChart == null) {
          myChart = echarts.init(chartDom);
        }
        myChart.resize();
      }
    })

  })
}

/**
 * 提交数据
 */
function submitForm() {
}

defineExpose({
  add,
  edit,
  submitForm,
});
</script>

<style lang="less" scoped>
/deep/ .ant-descriptions-header {
  margin-bottom: 4px;
}

.charts {
  width: 100%;
  height: 200px;
  border: 1px solid #f0f0f0;
}

.video {
  width: 200px;
  height: 100px;
  float: left;
  margin-right: 5px;
  position: relative;
  background: #000;
}

.video .videos {
  width: 100%;
  max-height: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
