<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <a-row>
        <a-col :span="24">
          <a-form-item label="分类名称:" :labelCol="labelCol" name="categoryName">
            <a-input placeholder="请输入分类名称" v-model:value="formState.categoryName"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="序值" :labelCol="labelCol" name="sortVal">
            <a-input placeholder="请输入序值" v-model:value="formState.sortVal"></a-input>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import { ref, reactive, defineEmits, defineExpose } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { ValidateErrorEntity } from 'ant-design-vue/es/form/interface';

const emits = defineEmits(['success', 'register', 'ok'])

const formState = reactive({
  categoryName: '',
  sortVal: "",
});
const formRef = ref();
const Api = reactive({
  add: '/sys/dtDeviceCategory/addDeviceCategory',
  edit: '/sys/dtUnit/editDtUnit',
});
const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 6 } });
const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
const confirmLoading = ref<boolean>(false);
//表单验证
const validatorRules = {
  categoryName: [{ required: true, message: '请输入分类名称' }],
  sortVal: [{ required: true, message: '请输入序值' }],
};
function add() {
  Object.assign(formState, {
    categoryName: '',
    sortVal: "",
  })
}
function submitForm() {
  let params = reactive({
    dataFlag: '0',
    dataSort: formState.sortVal,
    deviceCategory: formState.categoryName,
  })
  formRef.value.validate().then(() => {
    defHttp.post({ url: Api.add, params }).then((e) => {
      console.log(111);
      emits('ok');
    });
  }).catch((error: ValidateErrorEntity<any>) => {
    console.log('error', error);
  });
}
defineExpose({
  add,
  submitForm,
});
</script>

<style lang="less" scoped> .antd-modal-form {
   padding: 24px 24px 24px 24px;
 }
</style>
