<template>
  <BasicModal
    :title="title"
    :width="width"
    :visible="visible"
    :height="600"
    @ok="handleOk"
    :okText="'上报'"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    :cancelText="cancelText"
    :getContainer="getPopupContainer">
    <ReportsForm ref="realForm" @ok="submitCallback" :disabled="disableSubmit"/>
  </BasicModal>
</template>

<script lang="ts" setup>
  import {ref, nextTick, defineExpose, defineEmits} from 'vue';
  import {BasicModal} from '/@/components/Modal';
  import {useParamStore} from '/@/store/modules/getParam';
  import ReportsForm from './ReportsForm.vue';

  const realForm = ref();

  const paramStore = useParamStore();
  const title = ref<string>('火灾上报');
  const cancelText = ref<string>('关闭');
  const width = ref<number>(1000);
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const emit = defineEmits(['register', 'ok']);
  function getPopupContainer() {
    if (document.getElementsByClassName('full')[0]) {
      return document.getElementsByClassName('full')[0]
    } else {
      return document.getElementsByClassName('kanban')[0]
    }
  }
  function add() {
    visible.value = true;
    nextTick(() => {
      realForm.value.add();
    });
  }

  function edit(record) {
    visible.value = true;
    nextTick(() => {
      realForm.value.edit(record);
    });
  }

  function handleOk() {
    realForm.value.submitForm();
  }

  function submitCallback() {
    handleCancel();
    emit('ok');
  }

  function handleCancel() {
    visible.value = false;
  }

  defineExpose({
    add,
    edit,
    disableSubmit,
  });
</script>

<style scoped>

</style>
