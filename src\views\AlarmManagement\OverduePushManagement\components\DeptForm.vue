<template>
    <a-spin :spinning="confirmLoading">
        <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
            <JFormContainer :disabled="disabled">
                <a-row>
                    <a-col :span="12">
                        <a-form-item label="逾期级别" :labelCol="labelCol" :wrapperCol="wrapperCol" name="overdueLevel"
                                     v-bind="validateInfos.overdueLevel">
                            <JSelectMultiple :placeholder="'请选择逾期级别'"
                                             v-model:value="formState.overdueLevel"
                                             dictCode="overdueLevel" mode="default" :triggerChange="false">
                            </JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="逾期时间" :labelCol="labelCol" :wrapperCol="wrapperCol" name="overdueDuration"
                                     v-bind="validateInfos.overdueDuration">
                            <a-input-number style="width: 80%" placeholder="请输入逾期时间"
                                            v-model:value="formState.overdueDuration" :min="0"/>
                            小时
                        </a-form-item>
                    </a-col>
                    <!--<a-col :span="12">
                        <a-form-item label="状态" :labelCol="labelCol" :wrapperCol="wrapperCol" name="ruleStatus"
                                     v-bind="validateInfos.ruleStatus">
                            <RuleStatusSelect v-model:value="formState.ruleStatus">
                            </RuleStatusSelect>
                        </a-form-item>
                    </a-col>-->
                    <a-col :span="12">
                        <a-form-item label="优先级" :labelCol="labelCol" :wrapperCol="wrapperCol" name="ruleSort"
                                     v-bind="validateInfos.ruleSort">
                            <a-input-number style="width: 100%" placeholder="请输入优先级"
                                            v-model:value="formState.ruleSort" :min="1" :precision="0"/>
                        </a-form-item>
                    </a-col>
                </a-row>
            </JFormContainer>
        </a-form>
    </a-spin>
</template>

<script lang="ts" setup>
    import {Form} from 'ant-design-vue';
    import {
        defineComponent,
        ref,
        reactive,
        onMounted,
        defineProps,
        defineExpose,
        UnwrapRef,
        nextTick,
        defineEmits
    } from 'vue';
    import {useMessage} from '/@/hooks/web/useMessage';
    import {defHttp} from '/@/utils/http/axios';
    import {useUserStore} from '/@/store/modules/user';
    import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
    import RuleStatusSelect from '/@/components/Form/src/jeecg/components/serchselect/RuleStatusSelect.vue';

    const userStore = useUserStore();
    const props = defineProps(["disabled"])
    const emits = defineEmits(['success', 'register', 'ok'])

    const useForm = Form.useForm;
    const confirmLoading = ref<boolean>(false);
    const labelCol = ref<any>({xs: {span: 24}, sm: {span: 8}});
    const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 16}});
    const labelCol1 = ref<any>({xs: {span: 24}, sm: {span: 4}});
    const wrapperCol1 = ref<any>({xs: {span: 24}, sm: {span: 20}});
    const {createMessage} = useMessage();
    const Api = reactive({
        add: '/sys/dtAlarmRemindRule/addAlarmRemindRule',
        edit: '/sys/dtAlarmRemindRule/editAlarmRemindRule',
    });
    const formState = reactive({
        id: '',
        overdueLevel: '',
        overdueDuration: '',
        ruleStatus: 1,
        ruleSort: '',
    });


    //表单验证
    const validatorRules = {
        overdueLevel: [{required: true, message: '请选择逾期级别!', trigger: 'change'}],
        overdueDuration: [{required: true, message: '请输入逾期时间!'}],
        // ruleStatus: [{required: true, message: '请选择状态!'}],
        ruleSort: [{required: true, message: '请输入优先级!'}],
    };
    const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

    function add() {
        edit({});
    }

    function edit(record) {
        nextTick(() => {
            resetFields();
            const tmpData = {};
            Object.keys(formState).forEach((key) => {
                if(record.hasOwnProperty(key)){
                    tmpData[key] = record[key]
                }
            })
            Object.assign(formState, tmpData);
        });
    }

    /**
     * 提交数据
     */
    async function submitForm() {
        await validate();
        confirmLoading.value = true;
        let httpurl = '';
        let method = '';
        //时间格式化
        let model = {
            overdueLevel: formState.overdueLevel,
            overdueDuration: formState.overdueDuration,
            ruleSort: formState.ruleSort,
            ruleStatus: formState.ruleStatus,
        };
        if (!formState.id) {
            httpurl += Api.add;
            method = 'post';
        } else {
            httpurl += Api.edit;
            method = 'put';
            model.id = formState.id
        }
        defHttp.request({url: httpurl, params: model, method: method,}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                createMessage.success(res.message);
                emits('ok');
            } else {
                createMessage.warning(res.message);
            }
        }).finally(() => {
            confirmLoading.value = false;
        });
    }

    defineExpose({
        add,
        edit,
        submitForm
    });
</script>

<style scoped>
    .antd-modal-form {
        padding: 24px 24px 24px 24px;
    }
</style>
