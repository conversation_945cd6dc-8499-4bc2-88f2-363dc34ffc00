.fullscreen-modal {
  overflow: hidden;

  .ant-modal {
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;

    &-content {
      height: 100%;
    }

    .ant-modal-header,
    .@{namespace}-basic-title {
      cursor: default !important;
    }
  }
}
.ant-modal-root div[aria-hidden="true"] {
  display: none !important;
}
.ant-modal {
  width: 520px;
  padding-bottom: 0;

  .ant-modal-body > .scrollbar {
    padding: 14px;
  }

  &-title {
    font-size: 16px;
    font-weight: bold;
    line-height: 16px;

    .base-title {
      cursor: move !important;
    }
  }

  .ant-modal-body {
    padding: 0;

    > .scrollbar > .scrollbar__bar.is-horizontal {
      display: none;
    }
  }

  &-large {
    top: 60px;

    &--mini {
      top: 16px;
    }
  }

  &-header {
    padding: 16px;
  }

  &-content {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  }

  &-footer {
    button + button {
      margin-left: 10px;
    }
  }

  &-close {
    font-weight: normal;
    outline: none;
  }

  &-close-x {
    display: inline-block;
    width: 96px;
    /*    width: auto;*/
    height: 56px;
    line-height: 56px;
  }

  &-confirm-body {
    .ant-modal-confirm-content {
      // color: #fff;

      > * {
        color: @text-color-help-dark;
      }
    }
  }

  &-confirm-confirm.error .ant-modal-confirm-body > .anticon {
    color: @error-color;
  }

  &-confirm-btns {
    .ant-btn:last-child {
      margin-right: 0;
    }
  }

  &-confirm-info {
    .ant-modal-confirm-body > .anticon {
      color: @warning-color;
    }
  }

  &-confirm-confirm.success {
    .ant-modal-confirm-body > .anticon {
      color: @success-color;
    }
  }
}

/**隐藏样式-modal确定按钮 */
.jee-hidden {
  display: none
}

.ant-modal-confirm .ant-modal-body {
  padding: 24px !important;
}

@media screen and (max-height: 600px) {
  .ant-modal {
    top: 60px;
  }
}

@media screen and (max-height: 540px) {
  .ant-modal {
    top: 30px;
  }
}

@media screen and (max-height: 480px) {
  .ant-modal {
    top: 10px;
  }
}

.kanban .ant-modal {
  //top: 50%;
  //left: 0;
  //transform: translate(0, -50%);
  //transform-origin:center !important;
}

.kanban .ant-select-show-search.ant-select:not(.ant-select-customize-input) .ant-select-selector input {
  color: #fff;
}

.kanban .ant-modal-body {
  height: calc(100% - 120px);
  max-height: calc(100% - 120px);
}

.kanban .ant-modal-content {
  background: url('@/views/bigScreen/zhihuixiaofang/public/img/XMLID_1.png') no-repeat;
  background-size: 100% 100%;
  height: 100%;
}

.kanban .ant-modal-header {
  background: none;
  border-bottom: 1px solid #005288;
}

.kanban .ant-modal-title {
  color: #fff;
}

.kanban .jeecg-basic-title {
  color: #fff;
}

.kanban .anticon[tabindex] {
  color: #fff;
}

.kanban .ant-form-item-label > label {
  color: #fff;
}

.kanban .ant-select:not(.ant-select-customize-input) .ant-select-selector {
  background-color: #052555;
  border: 1px solid #4D5D83;
}

.kanban .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
  color: #fff !important;
}

.kanban .ant-select-dropdown {
  background-color: #052555;
  border: 1px solid #40a9ff;
  text-align: center;
}

.kanban .ant-select-item {
  color: #fff;
}

.kanban .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background-color: #005897;
}

.kanban .ant-btn-primary {
  border: none;
  background: #005897;
}

.kanban .vxe-table--render-default.border--full .vxe-table--header-wrapper {
  background: #052555;
}

.kanban .vxe-table--render-default .vxe-table--body-wrapper {
  background: #052555;

}
.vxe-table--render-default .vxe-table--body-wrapper.fixed-left--wrapper{
  padding-bottom: 10px;
}
.kanban .vxe-table .vxe-table--header-wrapper {
  color: #fff;
}

.kanban .vxe-table--render-default {
  color: #fff;
}

.kanban .vxe-table--render-default .vxe-body--row.row--hover {
  background: none;
}

.kanban .vxe-table--render-default.border--full .vxe-body--column, .kanban .vxe-table--render-default.border--full .vxe-footer--column, .kanban .vxe-table--render-default.border--full .vxe-header--column {
  background-image: linear-gradient(#4D5D83, #4D5D83), linear-gradient(#4D5D83, #4D5D83);
  background-repeat: no-repeat;
  background-size: 1px 100%, 100% 1px;
  background-position: 100% 0, 100% 100%;
}

.kanban .vxe-table--render-default .vxe-table--border-line {
  border: 1px solid #4D5D83;
}

.kanban .vxe-table .vxe-table--header-wrapper .vxe-table--header-border-line {
  border: 1px solid #4D5D83;
}

.kanban .ant-modal-footer {
  border-top: 1px solid #005288;
  //display: none;
}

.kanban .ant-pagination {
  color: #fff;
}

.kanban .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis, .kanban .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
  color: #fff;
}

.kanban .ant-select-single.ant-select-show-arrow .ant-select-selection-item {
  color: #fff !important;
}

.kanban .ant-pagination-item {
  background: none;
  border: 1px solid #4D5D83;
}

.kanban .ant-pagination-item a {
  color: #fff;
}

.kanban .ant-pagination-item-active a {
  color: #fff;
}

.kanban .ant-pagination-item-active {
  font-weight: 500;
  background: #005897;
  border-color: #4D5D83;
}

.kanban .ant-pagination-prev .ant-pagination-item-link, .kanban .ant-pagination-next .ant-pagination-item-link {
  background: none;
  border: 1px solid #4D5D83;
}

.kanban .ant-pagination-prev button, .kanban .ant-pagination-next button {
  color: #fff;
}

.kanban .ant-pagination-options-quick-jumper input {
  color: #fff;
  background: none;
  border: 1px solid #4D5D83;
}

.kanban .ant-select-arrow {
  color: #fff;
}

.kanban .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background: none;
}

.kanban .ant-picker {
  background-color: #052555;
  border: 1px solid #4D5D83;
}

.kanban .ant-picker-panel-container {
  background: #052555;
}

.kanban .ant-picker-header-view button {
  color: #fff;
}

.kanban .ant-picker-header {
  border-bottom: 1px solid #005288;
}

.kanban .ant-picker-header button {
  color: #fff;
}

.kanban .ant-picker-content th {
  color: #fff;
}

.kanban .ant-picker-cell {
  color: #fff;
}

.kanban .ant-picker-panel {
  border: 1px solid #005288;
}

.kanban .ant-picker-panel .ant-picker-footer {
  border-top: 1px solid #005288;
}

.kanban .ant-picker-footer {
  border-bottom: 1px solid #005288;
}

.kanban .ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner, .kanban .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(.ant-picker-cell-range-end):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end) .ant-picker-cell-inner {
  background: #005897;
}

.kanban .ant-picker-input > input {
  color: #fff;
}

.kanban .vxe-table--render-default .vxe-body--row.row--checked, .kanban .vxe-table--render-default .vxe-body--row.row--radio {
  background-color: #005897;
}

.kanban .ant-form-item {
  color: #fff;
}

.kanban .ant-radio-wrapper {
  color: #fff;
}

.kanban .ant-input {
  background-color: #052555;
  border: 1px solid #4D5D83;
  color: #fff !important;
}

.kanban .ant-upload.ant-upload-select-picture-card {
  background-color: #052555;
  border: 1px dashed #4D5D83;
  color: #fff;
}

.kanban .ant-upload {
  color: #fff;
}

.kanban .ant-upload-select-picture-card .ant-upload-text[data-v-2cb96cf9] {
  color: #fff;
}

.kanban .ant-upload-list-item-info .anticon-loading .anticon, .kanban .ant-upload-list-item-info .ant-upload-text-icon .anticon {
  color: #fff;
}

.kanban .ant-btn {
  color: #fff;
  background-color: #005897;
  border: 1px dashed #4D5D83;
}

.kanban .ant-btn[disabled], .kanban .ant-btn[disabled]:hover, .kanban .ant-btn[disabled]:focus, .kanban .ant-btn[disabled]:active {
  color: #fff;
  background-color: #052555;
  border: 1px dashed #4D5D83;
}

.kanban .ant-upload-list-item-info:hover {
  background-color: #005897;
}

.kanban .ant-upload-list-item-card-actions .anticon {
  color: #fff;
}

.kanban .ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input, .kanban .ant-form-item-has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper, .kanban .ant-form-item-has-error :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper, .kanban .ant-form-item-has-error :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:hover, .kanban .ant-form-item-has-error :not(.ant-input-affix-wrapper-disabled):not(.ant-input-affix-wrapper-borderless).ant-input-affix-wrapper:hover, .kanban .ant-form-item-has-error :not(.ant-input-number-affix-wrapper-disabled):not(.ant-input-number-affix-wrapper-borderless).ant-input-number-affix-wrapper:hover {
  background-color: #052555;
}

.kanban .ant-form-item-has-error .ant-input-number:not([disabled]):hover, .kanban .ant-form-item-has-error .ant-picker:not([disabled]):hover {
  background-color: #052555;
  border: 1px solid #4D5D83;
}

.kanban .ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
  background: #005897;
}

.kanban .ant-picker-time-panel-column > li.ant-picker-time-panel-cell .ant-picker-time-panel-cell-inner {
  color: #fff;
}

.kanban .ant-picker-header {
  color: #fff;
}

.kanban .ant-input-affix-wrapper {
  background: #052555;
  border: 1px solid #4D5D83;
}

.kanban input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px #052555 inset !important;
  -webkit-text-fill-color: #fff !important; /* 对于Chrome和Safari */
  color: #fff;
}

.kanban .ant-input-affix-wrapper > input.ant-input {
  color: #fff !important;
}

.kanban .scrollbar__view div:nth-child(1) {
  //min-height: auto !important;
}

.kanban .jeecg-strength-meter-bar {
  background-color: rgba(255, 255, 255, 0.25) !important;
}

.kanban .jeecg-strength-meter-bar::before, .kanban .jeecg-strength-meter-bar::after {
  background-color: rgba(255, 255, 255, 0.25) !important;
}

.kanban .ant-dropdown-menu {
  background: #052555;
  border: 1px solid #4D5D83;
}

.kanban .ant-dropdown-menu-item, .ant-dropdown-menu-submenu-title {
  color: #fff;
}

.kanban .ant-dropdown-menu-item:hover {
  background: #005897;
}

.kanban .ant-card {
  background: none;
  color: #fff;
}

.kanban .ant-tabs {
  color: #fff;
  height: 100%;
}

.kanban .ant-descriptions-title {
  color: #fff;
}

.kanban .ant-descriptions-item-label {
  color: #fff;
}

.kanban .ant-descriptions-item-content {
  color: #fff;
}

.kanban h1, .kanban h2, .kanban h3, .kanban h4, .kanban h5, .kanban h6 {
  color: #fff;
}

.kanban .ant-select-multiple .ant-select-selection-item {
  background: #052555;
  border: 1px solid #4D5D83;
}

.kanban .ant-select-multiple .ant-select-selection-item-content {
  color: #fff !important;
}

.kanban .ant-select-multiple .ant-select-selection-item-remove {
  color: #fff !important;
}
.kanban .jeecg-tree, .kanban .ant-spin-nested-loading{
  background: none;
}
.kanban .ant-tree{
  background: none;
  color:#fff
}
.kanban .ant-tree .ant-tree-node-content-wrapper:hover{
  background: #005897;
}
.kanban .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected{
  background: #005897;
}
.kanban .ant-tabs-content-holder{
  height: calc(100% - 62px - 56.8px - 52.8px);
  overflow-x: hidden;
  overflow-y: auto;
}
.kanban .scroll-container .scrollbar__view{
  height: 100%;
}
.kanban .scroll-container .scrollbar__view > div:first-child{
  height: 100%;
}.kanban .ant-tabs-content{
  height: 100%;
}
.kanban .ant-spin-nested-loading, .kanban .ant-spin-container, .kanban .antd-modal-form, .kanban .ant-card, .kanban .ant-card-body, .kanban .ant-form{
  height: 100%;
}
.kanban .ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-label{
  background: none;
}
.kanban .ant-descriptions-bordered .ant-descriptions-item-label{
  border-right: 1px solid #4d5d83;
}
.kanban .ant-descriptions-bordered .ant-descriptions-row {
  border-bottom: 1px solid #4d5d83;
}
.kanban .ant-descriptions-bordered .ant-descriptions-view {
  border: 1px solid #4d5d83;
}
.kanban .ant-divider-horizontal.ant-divider-with-text {
  border-top-color: rgba(255, 255, 255, 0.06);
  color: rgba(255, 255, 255, 0.85);
}




// ****************************************************************

.danweiguanli .ant-modal {
  //top: 50%;
  //left: 0;
  //transform: translate(0, -50%);
  //transform-origin:center !important;
}

.danweiguanli .ant-modal-body {
  height: calc(100% - 120px);
  max-height: calc(100% - 120px);
}

.danweiguanli .ant-modal-content {
  //background: url('@/views/bigScreen/zhihuixiaofang/public/img/XMLID_1.png') no-repeat;
  //background-size: 100% 100%;
  height: 100%;
}

.danweiguanli .ant-tabs {
  //color: #fff;
  height: 100%;
}

.danweiguanli .jeecg-tree, .danweiguanli .ant-spin-nested-loading{
  background: none;
}
.danweiguanli .ant-tabs-content-holder{
  height: calc(100% - 62px - 56.8px - 52.8px);
  overflow-x: hidden;
  overflow-y: auto;
}
.danweiguanli .scroll-container .scrollbar__view{
  height: 100%;
}
.danweiguanli .scroll-container .scrollbar__view > div:first-child{
  height: 100%;
}.danweiguanli .ant-tabs-content{
   height: 100%;
 }
.danweiguanli .ant-spin-nested-loading, .danweiguanli .ant-spin-container, .danweiguanli .antd-modal-form, .danweiguanli .ant-card, .danweiguanli .ant-card-body, .danweiguanli .ant-form{
  height: 100%;
}
