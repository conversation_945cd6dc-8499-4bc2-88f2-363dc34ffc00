<template>
    <a-date-picker
            :placeholder="placeholder"
            :value="value"
            :size="size"
            :disabled="disabled"
            :allowClear="allowClear"
            format="YYYY-MM-DD"
            style="width: 100%;"
            @change="handleChange"
            :getPopupContainer="getParentContainer"
    />
</template>

<script>
    import {defineComponent, ref, watch} from 'vue';
    import dayjs from 'dayjs';
    /**
     * 用于时间-time组件的范围查询
     */
    export default defineComponent({
        name: "DateSelect1",
        props: ["value", "placeholder", 'disabled', 'size'],
        emits: ['change', 'update:value'],
        setup(props, {emit}) {
            const value = ref(undefined)
            const placeholder = ref("请选择开始时间")
            placeholder.value = props.placeholder
            watch(() => props.value, (val) => {
                if (val) {
                    value.value = dayjs(val, "YYYY-MM-DD");
                }else{
                    value.value = val
                }
            }, {immediate: true});

            function getParentContainer(node) {
                if(document.getElementsByClassName('full')[0]){
                    return document.getElementsByClassName('full')[0]
                }else{
                    return document.getElementsByClassName('kanban')[0]
                }
            }
            function handleChange(date, dateString) {
                value.value = dateString
                emit('change', dateString);
                emit('update:value', dateString);
            }

            return {
                value,
                placeholder,
                getParentContainer,
                handleChange
            }
        }
    });
</script>
