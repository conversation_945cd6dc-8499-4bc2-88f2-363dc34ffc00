<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
  <title>海量点</title>
  <link rel="stylesheet"
        href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css"/>
  <style>
    html, body, #container {
      height: 100%;
      width: 100%;
    }

    .input-card .btn {
      margin-right: 1.2rem;
      width: 9rem;
    }

    .input-card .btn:last-child {
      margin-right: 0;
    }
  </style>
</head>
<body>
<div id="container" class="map" tabindex="0"></div>
<div class="input-card">
  <h4>海量点效果切换</h4>
  <div class="input-item">
    <input type="button" class="btn" value="单一图标" onclick='setStyle(0)'/>
    <input type="button" class="btn" value="多个图标" onclick='setStyle(1)'/>
  </div>
</div>
<script type="text/javascript" src='https://a.amap.com/jsapi_demos/static/citys.js'></script>
<script type="text/javascript"
        src="https://webapi.amap.com/maps?v=2.0&key=f321b651170b76b298d05d0914d8fe99&plugin=AMap.Adaptor"></script>
<script type="text/javascript">
  var map = new AMap.Map('container', {
    zoom: 4,
    center: [102.342785, 35.312316],
    showIndoorMap: false,
    viewMode: '3D',
  });

  // JSAPI 2.0 支持显示设置 zIndex, zIndex 越大约靠前，默认按顺序排列
  var style = [{
    url: 'https://webapi.amap.com/images/mass/mass0.png',
    anchor: new AMap.Pixel(6, 6),
    size: new AMap.Size(11, 11),
    zIndex: 3,
  }, {
    url: 'https://webapi.amap.com/images/mass/mass1.png',
    anchor: new AMap.Pixel(4, 4),
    size: new AMap.Size(7, 7),
    zIndex: 2,
  }, {
    url: 'https://webapi.amap.com/images/mass/mass2.png',
    anchor: new AMap.Pixel(3, 3),
    size: new AMap.Size(5, 5),
    zIndex: 1,
  }
  ];
  var data = [
    {
      lnglat: [117.2277215070910, 34.2009593484226], //经纬度
      name: "天安门",
      style: 0, //该数据的取值为样式数组对应的对象索引
    },
  ];
  var mass = new AMap.MassMarks(data, {
    opacity: 0.8,
    zIndex: 111,
    cursor: 'pointer',
    style: style
  });

  var marker = new AMap.Marker({content: ' ', map: map});

  mass.on('mouseover', function (e) {

    marker.setPosition(e.data.lnglat);
    marker.setLabel({content: e.data.name})
  });

  mass.setMap(map);

  function setStyle(multiIcon) {
    if (multiIcon) {
      mass.setStyle(style);
    } else {
      mass.setStyle(style[2]);
    }
  }
</script>
</body>
</html>
