<template>
  <BasicModal
    :title="title"
    :width="width"
    :visible="visible"
    :height="600"
    @ok="handleOk"
    :okText="'确定'"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }"
    @cancel="handleCancel"
    :cancelText="cancelText">
    <BatchProcessingForm ref="BatchProcessingForm1" @ok="submitCallback" :disabled="disableSubmit"/>
  </BasicModal>
</template>

<script lang="ts" setup>
  import {ref, nextTick, defineExpose, defineEmits} from 'vue';
  import {BasicModal} from '/@/components/Modal';
  import {useParamStore} from '/@/store/modules/getParam';
  import BatchProcessingForm from './BatchProcessingForm.vue';

  const BatchProcessingForm1 = ref();

  const paramStore = useParamStore();
  const title = ref<string>('');
  const cancelText = ref<string>('关闭');
  const width = ref<number>(800);
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const emit = defineEmits(['register', 'ok']);

  function add() {
    visible.value = true;
    nextTick(() => {
      BatchProcessingForm1.value.add();
    });
  }

  function edit(record) {
    visible.value = true;
    nextTick(() => {
      BatchProcessingForm1.value.edit(record);
    });
  }

  function handleOk() {
    BatchProcessingForm1.value.submitForm();
  }

  function submitCallback() {
    handleCancel();
    emit('ok');
  }

  function handleCancel() {
    visible.value = false;
  }

  defineExpose({
    add,
    edit,
    title,
    disableSubmit,
  });
</script>

<style scoped>

</style>
