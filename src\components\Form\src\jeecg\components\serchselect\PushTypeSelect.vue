<template>
  <a-checkbox-group
    v-bind="attrs"
    v-model:value="arrayValue"
    :options="dictOptions"
    @change="onChange">

  </a-checkbox-group>
</template>
<script lang="ts">
import {computed, defineComponent, onMounted, ref, nextTick, watch, reactive} from 'vue';
import {defHttp} from '/@/utils/http/axios';
import {propTypes} from '/@/utils/propTypes';
import {useMessage} from '/@/hooks/web/useMessage';
import {useUserStore} from '/@/store/modules/user';

export default defineComponent({
  name: 'PushTypeSelect',
  components: {},
  inheritAttrs: false,
  props: {
    value: propTypes.oneOfType([propTypes.string, propTypes.array]),
    spliter: {
      type: String,
      required: false,
      default: ',',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['options-change', 'change', 'input', 'update:value'],
  setup(props, {emit, refs}) {
    const arrayValue = ref<any[]>([]);
    const dictOptions = reactive([
      {label: '平台', value: '1'},
      {label: 'APP', value: '2'},
      {label: '短信', value: '3'},
    ]);
    const userStore = useUserStore();
    const Api = reactive<any>({});
    onMounted(() => {
    });

    watch(() => props.value, (val) => {
        let temp = props.value;
        if (!temp && temp !== 0) {
          arrayValue.value = []
        } else {
          temp = temp + '';
          arrayValue.value = temp.split(',')
        }
        //update-end-author:taoyan date:2022-7-4 for:issues/I5E7YX AUTO在线表单进入功能测试之后一直卡在功能测试界面
        //update-begin-author:taoyan date:20220401 for: 调用表单的 resetFields不会清空当前信息，界面显示上一次的数据
        if (props.value === '' || props.value === undefined) {
          arrayValue.value = [];
        }
        //update-end-author:taoyan date:20220401 for: 调用表单的 resetFields不会清空当前信息，界面显示上一次的数据

      }
    );

    function onChange(selectedValue) {
      emit('change', selectedValue.toString());
      emit('update:value', selectedValue);
    }

    return {
      onChange,
      arrayValue,
      dictOptions,
    };
  },
});
</script>
<style lang="less" scoped>
// ::v-deep .ant-select-selection-item {
//   color: rgba(0, 0, 0, 0.25) !important;
// }
</style>
