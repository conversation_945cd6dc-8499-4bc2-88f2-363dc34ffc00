<template>
  <a-spin :spinning="confirmLoading">
    <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="12">
            <a-form-item label="上报人" :wrapperCol="wrapperCol" :labelCol="labelCol">
              <span>{{formState.realname}}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系方式" :wrapperCol="wrapperCol" :labelCol="labelCol">
              <span>{{ formState.phone }}</span>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="简报" :wrapperCol="wrapperCol1" :labelCol="labelCol1" name="reportDesc"
                         v-bind="validateInfos.reportDesc">
              <a-textarea v-model:value="formState.reportDesc" placeholder="请输入简报"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="火势情况" :wrapperCol="wrapperCol" :labelCol="labelCol" name="fireSize"
                         v-bind="validateInfos.fireSize">
              <FireSizeSelect v-model:value="formState.fireSize"></FireSizeSelect>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否蔓延" :wrapperCol="wrapperCol" :labelCol="labelCol" name="spreadFlag"
                         v-bind="validateInfos.spreadFlag">
              <WhetherSelect v-model:value="formState.spreadFlag"></WhetherSelect>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否爆炸" :wrapperCol="wrapperCol" :labelCol="labelCol" name="explosionFlag"
                         v-bind="validateInfos.explosionFlag">
              <WhetherSelect v-model:value="formState.explosionFlag"></WhetherSelect>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否有人员被困" :wrapperCol="wrapperCol" :labelCol="labelCol" name="stuckFlag"
                         v-bind="validateInfos.stuckFlag">
              <WhetherSelect v-model:value="formState.stuckFlag"></WhetherSelect>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="是否有人员受伤" :wrapperCol="wrapperCol" :labelCol="labelCol" name="injuredFlag"
                         v-bind="validateInfos.injuredFlag">
              <WhetherSelect v-model:value="formState.injuredFlag"
                             @change="onWhetherSelect"></WhetherSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="show">
            <a-form-item label="被困人员数量和位置" :wrapperCol="wrapperCol1" :labelCol="labelCol1" name="position"
                         v-bind="validateInfos.position">
              <a-textarea v-model:value="formState.position" placeholder="请输入被困人员数量和位置"/>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="直接影响半径" :wrapperCol="wrapperCol" :labelCol="labelCol" name="directRadius"
                         v-bind="validateInfos.directRadius">
              <a-input placeholder="请输入直接影响半径" v-model:value="formState.directRadius" style="width: 90%;"></a-input> 米
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="间接影响半径" :wrapperCol="wrapperCol" :labelCol="labelCol" name="indirectRadius"
                         v-bind="validateInfos.indirectRadius">
              <a-input placeholder="请输入间接影响半径" v-model:value="formState.indirectRadius" style="width: 90%;"></a-input> 米
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="燃烧物质" :wrapperCol="wrapperCol1" :labelCol="labelCol1" name="material"
                         v-bind="validateInfos.material">
              <MaterialSelect v-model:value="formState.material"></MaterialSelect>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="图片" :wrapperCol="wrapperCol1" :labelCol="labelCol1" name="pictureUrl"
                         v-bind="validateInfos.pictureUrl">
              <JImageUpload ref="JImageUpload1"
                            :fileMax="40" v-model:value="formState.pictureUrl"></JImageUpload>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="视频" :wrapperCol="wrapperCol1" :labelCol="labelCol1" name="videoUrl"
                         v-bind="validateInfos.videoUrl">
              <JUpload v-model:value="formState.videoUrl" maxCount="1"
                       text="上传视频"></JUpload>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import {Form} from 'ant-design-vue';
  import {
    defineComponent,
    ref,
    reactive,
    onMounted,
    defineProps,
    defineExpose,
    UnwrapRef,
    nextTick,
    defineEmits
  } from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import {useMessage} from '/@/hooks/web/useMessage';
  import {useUserStore} from '/@/store/modules/user';
  import FireSizeSelect from '/@/components/Form/src/jeecg/components/serchselect/FireSizeSelect.vue';
  import WhetherSelect from '/@/components/Form/src/jeecg/components/serchselect/WhetherSelect.vue';
  import MaterialSelect from '/@/components/Form/src/jeecg/components/serchselect/MaterialSelect.vue';
  import JImageUpload from '@/components/Form/src/jeecg/components/JImageUpload.vue';
  import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';


  const userStore = useUserStore();
  const confirmLoading = ref<boolean>(false);
  const show = ref<boolean>(true);
  const props = defineProps(["disabled", "realname", "phone"])
  const emits = defineEmits(['success', 'register', 'ok'])
  const labelCol = ref<any>({xs: {span: 24}, sm: {span: 8}});
  const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 16}});
  const labelCol1 = ref<any>({xs: {span: 24}, sm: {span: 4}});
  const wrapperCol1 = ref<any>({xs: {span: 24}, sm: {span: 20}});
  const {createMessage} = useMessage();
  const Api = reactive({
    edit: '/sys/dtFireReport/addFireReport',
  });

  const assignStaffOptions = ref<any>([])

  const formState = reactive({
    realname: userStore.getLoginInfo.userInfo.realname,
    phone: userStore.getLoginInfo.userInfo.phone,
    reportDesc: '',
    fireSize: '小火',
    spreadFlag: '是',
    explosionFlag: '是',
    stuckFlag: '是',
    injuredFlag: '是',
    position: '',
    material: '不明',
    pictureUrl: '',
    videoUrl: '',
    directRadius: '100',
    indirectRadius: '500',
  });
  const validatorRules = {
    reportDesc: [{required: true, message: '请输入简报'}],
    fireSize: [{required: true, message: '请选择火势情况', trigger: 'change'}],
    spreadFlag: [{required: true, message: '请选择是否蔓延', trigger: 'change'}],
    explosionFlag: [{required: true, message: '请选择是否爆炸', trigger: 'change'}],
    stuckFlag: [{required: true, message: '请选择是否有人员被困', trigger: 'change'}],
    injuredFlag: [{required: true, message: '请选择是否有人员受伤', trigger: 'change'}],
    position: [{required: true, message: '请输入被困人员数量和位置'}],
    material: [{required: true, message: '请选择燃烧物质', trigger: 'change'}],
    directRadius: [{required: true, message: '请输入直接影响半径'}],
    indirectRadius: [{required: true, message: '请输入间接影响半径'}],
    pictureUrl: [{required: true, message: '请选择图片'}],
  };
  const useForm = Form.useForm;
  const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

  onMounted(() => {
  });

  function onWhetherSelect(e) {
    if (e == '否') {
      validatorRules.position[0].required = false
    } else {
      validatorRules.position[0].required = true
    }
  }

  function add() {
    edit({});
  }

  function edit(record) {
    nextTick(() => {
      resetFields();
      Object.assign(formState, record);
      formState.realname = userStore.getLoginInfo.userInfo.realname
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    await validate();
    confirmLoading.value = true;
    let httpurl = '';
    let method = '';
    //时间格式化
    let model = {
      alarmId: formState.id,
      reportDesc: formState.reportDesc,
      fireSize: formState.fireSize,
      spreadFlag: formState.spreadFlag,
      explosionFlag: formState.explosionFlag,
      stuckFlag: formState.stuckFlag,
      injuredFlag: formState.injuredFlag,
      position: formState.position,
      material: formState.material,
      pictureUrl: formState.pictureUrl,
      videoUrl: formState.videoUrl,
      directRadius: formState.directRadius,
      indirectRadius: formState.indirectRadius,
      reportPhone: userStore.getLoginInfo.userInfo.phone
    };
    httpurl += Api.edit;
    method = 'post';
    defHttp.request({url: httpurl, params: model, method: method,}, {isTransformResponse: false}).then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        emits('ok');
      } else {
        createMessage.warning(res.message);
      }
    }).finally(() => {
      confirmLoading.value = false;
    });

  }

  defineExpose({
    add,
    edit,
    submitForm,
  });

</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 24px 24px 24px 24px;
  }
</style>
