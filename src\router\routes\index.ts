import type { AppRouteRecordRaw, AppRouteModule } from '/@/router/types';

import { PAGE_NOT_FOUND_ROUTE, REDIRECT_ROUTE } from '/@/router/routes/basic';

import { mainOutRoutes } from './mainOut';
import { PageEnum } from '/@/enums/pageEnum';
import { t } from '/@/hooks/web/useI18n';

const modules = import.meta.glob('./modules/**/*.ts', { eager: true });

const routeModuleList: AppRouteModule[] = [];

// 加入到路由集合中
Object.keys(modules).forEach((key) => {
  const mod = (modules as Recordable)[key].default || {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];
  routeModuleList.push(...modList);
});

export const asyncRoutes = [PAGE_NOT_FOUND_ROUTE, ...routeModuleList];

export const RootRoute: AppRouteRecordRaw = {
  path: '/',
  name: 'Root',
  redirect: PageEnum.BASE_HOME,
  meta: {
    title: 'Root',
  },
};

export const LoginRoute: AppRouteRecordRaw = {
  path: '/login',
  name: 'Login',
  //新版后台登录，如果想要使用旧版登录放开即可
  // component: () => import('/@/views/sys/login/Login.vue'),
  component: () => import('/@/views/system/loginmini/MiniLogin.vue'),
  meta: {
    title: t('routes.basic.login'),
  },
};



export const ifZhxfksh: AppRouteRecordRaw = {
  path: '/ifZhxfksh',
  name: 'ifZhxfksh',
  component: () => import('/@/views/bigScreen/zhihuixiaofang/indexBigScreen.vue'),
  meta: {
    title: "首页",
  },
};
//街道
export const ifZfxfksh: AppRouteRecordRaw = {
  path: '/ifZfxfksh',
  name: 'ifZfxfksh',
  component: () => import('/@/views/bigScreen/intelligentFireProtection/index.vue'),
  meta: {
    title: "街道看板",
  },
};
//单位看板
export const ifLwxfksh: AppRouteRecordRaw = {
  path: '/ifLwxfksh',
  name: 'ifLwxfksh',
  component: () => import('/@/views/bigScreen/monitoringOfNetworkedUnits/index.vue'),
  meta: {
    title: "单位看板",
  },
};
//应急调度
export const Emergency: AppRouteRecordRaw = {
  path: '/Emergency',
  name: 'Emergency',
  component: () => import('/@/views/bigScreen/Emergency/index.vue'),
  meta: {
    title: "应急调度",
  },
};

//地图测试外链
export const MapCeshi: AppRouteRecordRaw = {
  path: '/MapCeshi',
  name: 'MapCeshi',
  component: () => import('/@/views/bigScreen/MapCeshi/index.vue'),
  meta: {
    title: t('routes.basic.MapCeshi'),
  },
};
//单位看板
export const ifLwxfksh3D: AppRouteRecordRaw = {
  path: '/ifLwxfksh3D',
  name: 'ifLwxfksh3D',
  component: () => import('/@/views/bigScreen/monitoringOfNetworkedUnits3Dqianru/index.vue'),
  meta: {
    title: "单位看板",
  },
};
//update-begin---author:wangshuai ---date:20220629  for：auth2登录页面路由------------
export const Oauth2LoginRoute: AppRouteRecordRaw = {
  path: '/oauth2-app/login',
  name: 'oauth2-app-login',
  //新版钉钉免登录，如果想要使用旧版放开即可
  // component: () => import('/@/views/sys/login/OAuth2Login.vue'),
  component: () => import('/@/views/system/loginmini/OAuth2Login.vue'),
  meta: {
    title: t('routes.oauth2.login'),
  },
};
//update-end---author:wangshuai ---date:20220629  for：auth2登录页面路由------------

/**
 * 【通过token直接静默登录】流程办理登录页面 中转跳转
 */
export const TokenLoginRoute: AppRouteRecordRaw = {
  path: '/tokenLogin',
  name: 'TokenLoginRoute',
  component: () => import('/@/views/sys/login/TokenLoginPage.vue'),
  meta: {
    title: '带token登录页面',
    ignoreAuth: true,
  },
};
//3D看板
export const ThreeDKanban: AppRouteRecordRaw = {
  path: '/3DKanban',
  name: '3DKanban',
  //新版后台登录，如果想要使用旧版登录放开即可
  // component: () => import('/@/views/sys/login/Login.vue'),
  component: () => import('/@/views/bigScreen/ifraemBigScreen/index1.vue'),
  meta: {
    title: t('routes.basic.ThreeDKanban'),
  },
};
// Basic routing without permission
export const basicRoutes = [LoginRoute,ifZhxfksh, ifZfxfksh,ifLwxfksh3D, ifLwxfksh,Emergency, MapCeshi, RootRoute,ThreeDKanban, ...mainOutRoutes, REDIRECT_ROUTE, PAGE_NOT_FOUND_ROUTE, TokenLoginRoute, Oauth2LoginRoute];
