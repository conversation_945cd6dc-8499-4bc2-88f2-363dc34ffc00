<!-- 常态化指挥架构 -->
<template>
    <a-card :bordered="false">
        <div class="table-operator">
            <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleAdd" v-if="hasPermission('AlarmManagement:Temporarizationcommandstructure:add')">
                新增
            </a-button>
        </div>
        <!-- table区域-begin -->
        <JVxeTable
                ref="tableRef"
                bordered
                row-number
                row-selection
                keep-source
                resizable
                :maxHeight="484"
                :loading="loading"
                :dataSource="dataSource"
                :columns="columns"
                :pagination="pagination"
                style="margin-top: 8px"
                @selectRowChange="handleSelectRowChange"
                @pageChange="handlePageChange">
            <template #action="props">
                  <a @click="handleEdit(props.row)">编辑</a>
                <a-divider type="vertical"/>
                <Popconfirm title="是否删除？" @confirm="handleDelete(props.row)" v-if="hasPermission('AlarmManagement:Temporarizationcommandstructure:delete')">
                    <a>删除</a>
                </Popconfirm>
            </template>
        </JVxeTable>
    </a-card>
    <DeptModal ref="DeptModal1" @ok="loadData"/>
</template>

<script lang="ts" setup>
    import {onMounted, ref, reactive} from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {filterObj} from '/@/utils/common/compUtils';
    import { useMessage } from '/@/hooks/web/useMessage';
    import {initDictOptions} from '/@/utils/dict';
    import {Popconfirm} from 'ant-design-vue';
    import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
    import DateSelect from '/@/components/Form/src/jeecg/components/serchselect/DateSelect.vue';
    import DeptModal from './components/DeptModal.vue';
    import BatchProcessingModal from './components/BatchProcessingModal.vue';
    import DispatchModal from './components/DispatchModal.vue';
    import SuperviseAndHandleModal from './components/SuperviseAndHandleModal.vue';
    import BatchProcessingAllModal from './components/BatchProcessingAllModal.vue';
    import ViewModal from './components/ViewModal.vue';
    import ReportsModal from './components/ReportsModal.vue';
    import {useUserStore} from '/@/store/modules/user';
    import { usePermission } from '/@/hooks/web/usePermission';

    const { hasPermission } = usePermission();
    const userStore = useUserStore();
    const DeptModal1 = ref();
    const labelCol = reactive({
        xs: {span: 24},
        sm: {span: 8},
    })
    const wrapperCol = reactive({
        xs: {span: 24},
        sm: {span: 16},
    })
    const queryParam = ref<any>({
        startDate:'',
        endDate:'',
    });
    const loading = ref<boolean>(false);
    const columns = ref([
        {
            title: '排序',
            key: 'sort',
            minWidth: 100,
            align: "center",
        },
        {
            title: '指挥主体',
            key: 'zhzt',
            minWidth: 180,
            align: "center",
        },
        {
            title: '名称',
            key: 'mc',
            minWidth: 200,
            align: "center",
        },
        {
            title: '联系方式',
            key: 'lxfs',
            minWidth: 200,
            align: "center",
        },
        {
            title: '操作',
            type: "slot",
            key: 'action',
            align: 'center',
            fixed: 'right',
            width: 145,
            slotName: 'action',
        },
    ]);
    const Api = reactive<any>({
        list: '/sys/dtZhjgry/pageList',
        delete: '/sys/dtZhjgry/delZhjgry',//编辑告警详情状态
    });
    const { createMessage } = useMessage();
    const dataSource = ref<any>([]);
    const pagination = ref<any>({
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '30'],
        showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条';
        },
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
    });
    // 选择的行
    const selectedRowKeys = ref<any>([]);
    const selectionRows = ref<any>([]);
    const iSorter = ref<any>({column: 'createTime', order: 'desc'});
    const iFilters = ref<any>({});
    const tableRef = ref();

    /**
     * 获取查询参数
     */
    function getQueryParams() {
        let params = Object.assign(queryParam.value, iSorter.value, iFilters.value);
        params.pageNo = pagination.value.current;
        params.pageSize = pagination.value.pageSize;
        return filterObj(params);
    }

    // 当分页参数变化时触发的事件
    function handlePageChange(event) {
        // 重新赋值
        pagination.value.current = event.current;
        pagination.value.pageSize = event.pageSize;
        // 查询数据
        loadData();
    }

    //新增
    function handleAdd() {
        DeptModal1.value.disableSubmit = false;
        DeptModal1.value.add();
    }

    //新增
    function handleEdit(record) {
        DeptModal1.value.disableSubmit = false;
        DeptModal1.value.edit(record);
    }
    function loadData(arg) {
        if (arg === 1) {
            pagination.value.current = 1;
        }
        loading.value = true;
        let params = getQueryParams();
        defHttp.get({url: Api.list, params}, {isTransformResponse: false}).then((res) => {
            if (res.success) {
                dataSource.value = res.result.records;
                if (res.result && res.result.total) {
                    pagination.value.total = res.result.total;
                } else {
                    pagination.value.total = 0;
                }
            }
        }).finally(() => {
            loading.value = false;
        });
    }
    //关闭
    function handleDelete(e) {
        defHttp.delete({url: Api.delete, data: {id: e.id}}, {
            isTransformResponse: false,
            joinParamsToUrl: true
        }).then((res) => {
            if (res.success) {
                createMessage.success(res.message);
            } else {
                createMessage.warning(res.message);
            }
        }).finally(() => {
            loadData()
        });
    }

    async function initDictConfig() {

    }

    onMounted(() => {
        //初始化字典选项
        initDictConfig();
        //初始加载页面
        loadData();
    });



</script>
<style lang="less" scoped>
    .jeecg-basic-table-form-container {
        padding: 0px;

        .table-page-search-submitButtons {
            display: block;
            margin-bottom: 0;
            white-space: nowrap;
        }

        .ant-form-item {
            margin-bottom: 8px !important;
        }
    }
    .ant-divider-vertical{
      /*border-left: 1px solid #ffffff;*/
    }
</style>
