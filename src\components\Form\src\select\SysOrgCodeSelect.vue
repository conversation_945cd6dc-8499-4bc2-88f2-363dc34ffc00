<!--下拉树-->
<template>
  <a-tree-select
    allowClear
    labelInValue
    style="width: 100%"
    :disabled="disabled"
    :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
    :placeholder="placeholder"
    :value="treeValue"
    :treeData="treeData"
    :multiple="multiple"
    treeDefaultExpandAll
    @change="onChange"
  >
  </a-tree-select>
</template>
<script lang="ts">
  import {defineComponent, ref, unref, watch, nextTick, onMounted} from 'vue';
  import {useRuleFormItem} from '/@/hooks/component/useFormItem';
  import {propTypes} from '/@/utils/propTypes';
  import {useAttrs} from '/@/hooks/core/useAttrs';
  import {getLabelDepartTree, getDepartByOrgCode} from '/@/api/common/api';
  import {useMessage} from '/@/hooks/web/useMessage';

  const {createMessage, createErrorModal} = useMessage();
  export default defineComponent({
    name: 'SysOrgCodeTreeSelect',
    components: {},
    inheritAttrs: false,
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.array]),
      placeholder: {
        type: String,
        default: '请选择',
        required: false,
      },
      disabled: {
        type: Boolean,
        default: false,
        required: false,
      },
      condition: {
        type: String,
        default: '',
        required: false,
      },
      // 是否支持多选
      multiple: {
        type: [Boolean, String],
        default: false,
      },
      loadTriggleChange: {
        type: Boolean,
        default: false,
        required: false,
      },
      pid: {
        type: String,
        default: '',
        required: false,
      },
      pcode: {
        type: String,
        default: '',
        required: false,
      },
      back: {//返回key
        type: String,
        default: '',
        required: false,
      },
    },
    emits: ['options-change', 'change', 'update:value'],
    setup(props, {emit, refs}) {
      const emitData = ref<any[]>([]);
      const treeData = ref<any[]>([]);
      const treeValue = ref();
      const attrs = useAttrs();
      const [state, , , formItemContext] = useRuleFormItem(props, 'value', 'change', emitData);
      /**
       * 监听value数据并初始化
       */
      onMounted(() => {
        loadRoot()
      });

      function loadRoot() {
        let param = {};
        getLabelDepartTree(param).then((res) => {
          if (res && res.length > 0) {
            addChildren1(res);
            treeData.value = res;
          }
        });
      }
      function addChildren1(children) {
        if (children && children.length > 0) {
          for (let item of children) {
            item.key = item.orgCode;
            item.value = item.orgCode;
            if (item.leaf == false) {
              item.isLeaf = false;
            } else if (item.leaf == true) {
              item.isLeaf = true;
            }
            if (item.children) {
              addChildren1(item.children)
            }
          }
        }
      }
      function onChange(value, label, extra) {
        if (!value) {
          emitValue('');
        } else if (value instanceof Array) {
          emitValue(value.map((item) => item.value).join(','));
        } else {
          emitValue(value.value);
        }
        treeValue.value = value;
      }

      function emitValue(value) {
        emit('change', value);
        emit('update:value', value);
      }


      return {
        state,
        attrs,
        onChange,
        treeData,
        treeValue,
      };
    },
  });
</script>
