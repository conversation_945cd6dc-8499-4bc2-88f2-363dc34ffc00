<!--字典下拉多选-->
<template>
  <a-select :value="arrayValue" @change="onChange" :mode="mode" :filter-option="filterOption" :disabled="disabled"
            :placeholder="placeholder" :allowClear="allowClear" showSearch :getPopupContainer="getParentContainer" >
    <a-select-option value="4">误报</a-select-option>
    <a-select-option value="3">已处置</a-select-option>
  </a-select>
</template>
<script lang="ts">
  import { computed, defineComponent, onMounted, ref, nextTick, watch,reactive } from 'vue';
  import { useRuleFormItem } from '/@/hooks/component/useFormItem';
  import { defHttp } from '/@/utils/http/axios';
  import { propTypes } from '/@/utils/propTypes';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { getDictItems } from '/@/api/common/api';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage, createErrorModal } = useMessage();
  export default defineComponent({
    name: 'ProcessingResultsSelect',
    components: {},
    inheritAttrs: false,
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.array]),
      placeholder: {
        type: String,
        default: '请选择',
        required: false,
      },
      popContainer: {
        type: String,
        default: '',
        required: false,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      mode: {
        type: Boolean,
        default: "multiple",
      },
      allowClear: {
        type: Boolean,
        default: false,
      },
      spliter: {
        type: String,
        required: false,
        default: ',',
      },
    },
    emits: ['options-change', 'change', 'input', 'update:value'],
    setup(props, { emit, refs }) {
      const arrayValue = ref<any[]>(!props.value ? [] : props.value.split(props.spliter));
      const attrs = useAttrs();
      onMounted(() => {
      });

      watch(() => props.value, (val) => {
          if (!val) {
            arrayValue.value = [];
          } else {
            arrayValue.value = props.value.split(props.spliter);
          }
        }
      );

      function onChange(selectedValue) {
        if (props.mode == "multiple") {
          emit('change', selectedValue.join(props.spliter));
          emit('update:value', selectedValue.join(props.spliter));
        } else {
          emit('change', selectedValue);
          emit('update:value', selectedValue);
        }
      }

      function getParentContainer(node) {
        if(document.getElementsByClassName('full')[0]){
          return document.getElementsByClassName('full')[0]
        }else{
          return document.getElementsByClassName('kanban')[0]
        }
      }
      //update-begin-author:taoyan date:2022-5-31 for: VUEN-1145 下拉多选，搜索时，查不到数据
      function filterOption(input, option) {
        return option.children()[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      }
      //update-end-author:taoyan date:2022-5-31 for: VUEN-1145 下拉多选，搜索时，查不到数据

      return {
        attrs,
        onChange,
        arrayValue,
        getParentContainer,
        filterOption,
      };
    },
  });
</script>
<style lang="less" scoped>
  // ::v-deep .ant-select-selection-item {
  //   color: rgba(0, 0, 0, 0.25) !important;
  // }
</style>
