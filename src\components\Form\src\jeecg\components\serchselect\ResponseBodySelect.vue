<template>
    <a-radio-group
            :disabled="disabled"
            :show-arrow="true"
            :size="size"
            @change="onChange"
            :value="arrayValue"
    >
        <a-radio v-for="(item,index) in data" :key="index" :value="item" style="float:left;width: calc(50% - 8px);">{{item}}</a-radio>
        <a-radio value="其他" style="float:left;">其他</a-radio>
        <a-input placeholder="请输入响应内容" v-model:value="arrayValue1" style="float:left;width: calc(30% - 8px);"/>
    </a-radio-group>
</template>
<script lang="ts">
    import {computed, defineComponent, onMounted, ref, nextTick, watch, reactive} from 'vue';
    import {defHttp} from '/@/utils/http/axios';
    import {propTypes} from '/@/utils/propTypes';
    import {useMessage} from '/@/hooks/web/useMessage';
    import {useUserStore} from '/@/store/modules/user';

    export default defineComponent({
        name: 'FireSizeSelect',
        components: {},
        inheritAttrs: false,
        props: {
            value: propTypes.oneOfType([propTypes.string, propTypes.array]),
            spliter: {
                type: String,
                required: false,
                default: ',',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
        },
        emits: ['options-change', 'change', 'input', 'update:value'],
        setup(props, {emit, refs}) {
            const arrayValue = ref<any[]>(!props.value ? [] : props.value);
            const arrayValue1 = ref("");
            const userStore = useUserStore();
            const data = ref<any>([]);
            const Api = reactive<any>({
                getPreSchemeResponseList: '/sys/dtPreScheme/getPreSchemeResponseList',
            });
            onMounted(() => {
            });

            watch(() => props.value, (val) => {
                    arrayValue.value = val;
                }
            );

            function loadData(record) {
                data.value = [];
                let params = {
                    preSchemeId: record.preSchemeId,
                };
                defHttp.get({url: Api.getPreSchemeResponseList, params}, {isTransformResponse: false}).then((res) => {
                    if (res.success) {
                        data.value = res.result
                        arrayValue.value = res.result[0];
                        emit('change', arrayValue.value);
                    }
                }).finally(() => {
                });
            }
            function onChange(selectedValue) {
                emit('change', selectedValue.target.value);
                emit('update:value', selectedValue.target.value);
            }

            return {
                loadData,
                onChange,
                arrayValue,
                arrayValue1,
                data,
            };
        },
    });
</script>
<style lang="less" scoped>
    // ::v-deep .ant-select-selection-item {
    //   color: rgba(0, 0, 0, 0.25) !important;
    // }
</style>
