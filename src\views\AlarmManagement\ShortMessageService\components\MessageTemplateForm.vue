<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="24">
            <a-form-item label="模板名称" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="templateName"
                         v-bind="validateInfos.templateName">
              <a-input placeholder="请输入模板名称" v-model:value="formState.templateName"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="模板内容" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="templateContent"
                         v-bind="validateInfos.templateContent">
              <a-input placeholder="请输入模板内容"
                       v-model:value="formState.templateContent"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="模板CODE" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="templateCode"
                         v-bind="validateInfos.templateCode">
              <a-input placeholder="请输入模板CODE" v-model:value="formState.templateCode" :disabled="formState.id"/>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import {Form} from 'ant-design-vue';
import {
  defineComponent,
  ref,
  reactive,
  onMounted,
  defineProps,
  defineExpose,
  UnwrapRef,
  nextTick,
  defineEmits
} from 'vue';
import {useMessage} from '/@/hooks/web/useMessage';
import {defHttp} from '/@/utils/http/axios';
import {useUserStore} from '/@/store/modules/user';
import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';

const userStore = useUserStore();
const props = defineProps(["disabled"])
const emits = defineEmits(['success', 'register', 'ok'])

const useForm = Form.useForm;
const confirmLoading = ref<boolean>(false);
const labelCol = ref<any>({xs: {span: 24}, sm: {span: 6}});
const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 18}});
const labelCol1 = ref<any>({xs: {span: 24}, sm: {span: 4}});
const wrapperCol1 = ref<any>({xs: {span: 24}, sm: {span: 20}});
const {createMessage} = useMessage();
const Api = reactive({
  add: '/business/dtMessageTemplate/addMessageTemplate',
  edit: '/business/dtMessageTemplate/editMessageTemplate',
});
const formState = reactive({
  id: undefined,
  templateName: '',
  templateContent: '',
  templateCode: '',
});

//表单验证
const validatorRules = {
  templateName: [{required: true, message: '请输入模板名称!'}],
  templateContent: [{required: true, message: '请输入模板内容!'}],
  templateCode: [{required: true, message: '请输入模板CODE!'}],
};
const {
  resetFields,
  validate,
  validateInfos
} = useForm(formState, validatorRules, {immediate: false});

function add() {
  edit({});
}

function edit(record) {
  nextTick(() => {
    resetFields();
    const tmpData = {};
    Object.keys(formState).forEach((key) => {
      if (record.hasOwnProperty(key)) {
        tmpData[key] = record[key]
      }
    })
    Object.assign(formState, tmpData);
  });
}

/**
 * 提交数据
 */
async function submitForm() {
  await validate();
  confirmLoading.value = true;
  let httpurl = '';
  let method = '';
  //时间格式化
  let model = Object.assign({}, formState);
  if (!model.id) {
    httpurl += Api.add;
    method = 'post';
  } else {
    httpurl += Api.edit;
    method = 'put';
  }
  defHttp.request({
    url: httpurl,
    params: model,
    method: method,
  }, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      createMessage.success(res.message);
      emits('ok');
    } else {
      createMessage.warning(res.message);
    }
  }).finally(() => {
    confirmLoading.value = false;
  });
}

defineExpose({
  add,
  edit,
  submitForm
});

</script>

<style scoped>
.antd-modal-form {
  padding: 24px 24px 24px 24px;
}
</style>
