function createUnityInstance(r,n,l){function u(e,t){if(!u.aborted&&n.showBanner)return"error"==t&&(u.aborted=!0),n.showBanner(e,t);switch(t){case"error":console.error(e);break;case"warning":console.warn(e);break;default:console.log(e)}}function o(e){var t=e.reason||e.error,r=t?t.toString():e.message||e.reason||"",n=t&&t.stack?t.stack.toString():"";(r+="\n"+(n=n.startsWith(r)?n.substring(r.length):n).trim())&&c.stackTraceRegExp&&c.stackTraceRegExp.test(r)&&h(r,e.filename||t&&(t.fileName||t.sourceURL)||"",e.lineno||t&&(t.lineNumber||t.line)||0)}function e(e,t,r){var n=e[t];void 0!==n&&n||(console.warn('Config option "'+t+'" is missing or empty. Falling back to default value: "'+r+'". Consider updating your WebGL template to include the missing config option.'),e[t]=r)}l=l||function(){};var t,c={canvas:r,webglContextAttributes:{preserveDrawingBuffer:!1},cacheControl:function(e){return e==c.dataUrl?"must-revalidate":"no-store"},streamingAssetsUrl:"StreamingAssets",downloadProgress:{},deinitializers:[],intervals:{},setInterval:function(e,t){e=window.setInterval(e,t);return this.intervals[e]=!0,e},clearInterval:function(e){delete this.intervals[e],window.clearInterval(e)},preRun:[],postRun:[],print:function(e){console.log(e)},printErr:function(e){console.error(e),"string"==typeof e&&-1!=e.indexOf("wasm streaming compile failed")&&(-1!=e.toLowerCase().indexOf("mime")?u('HTTP Response Header "Content-Type" configured incorrectly on the server for file '+c.codeUrl+' , should be "application/wasm". Startup time performance will suffer.',"warning"):u('WebAssembly streaming compilation failed! This can happen for example if "Content-Encoding" HTTP header is incorrectly enabled on the server for file '+c.codeUrl+", but the file is not pre-compressed on disk (or vice versa). Check the Network tab in browser Devtools to debug server header configuration.","warning"))},locateFile:function(e){return e},disabledCanvasEvents:["contextmenu","dragstart"]};for(t in e(n,"companyName","Unity"),e(n,"productName","WebGL Player"),e(n,"productVersion","1.0"),n)c[t]=n[t];c.streamingAssetsUrl=new URL(c.streamingAssetsUrl,document.URL).href;var a=c.disabledCanvasEvents.slice();function i(e){e.preventDefault()}a.forEach(function(e){r.addEventListener(e,i)}),window.addEventListener("error",o),window.addEventListener("unhandledrejection",o);var s="",d="",f=(document.addEventListener("webkitfullscreenchange",function(e){document.webkitCurrentFullScreenElement===r?r.style.width&&(s=r.style.width,d=r.style.height,r.style.width="100%",r.style.height="100%"):s&&(r.style.width=s,r.style.height=d,d=s="")}),{Module:c,SetFullscreen:function(){if(c.SetFullscreen)return c.SetFullscreen.apply(c,arguments);c.print("Failed to set Fullscreen mode: Player not loaded yet.")},SendMessage:function(){if(c.SendMessage)return c.SendMessage.apply(c,arguments);c.print("Failed to execute SendMessage: Player not loaded yet.")},Quit:function(){return new Promise(function(e,t){c.shouldQuit=!0,c.onQuit=e,a.forEach(function(e){r.removeEventListener(e,i)}),window.removeEventListener("error",o),window.removeEventListener("unhandledrejection",o)})}});function h(e,t,r){c.startupErrorHandler?c.startupErrorHandler(e,t,r):c.errorHandler&&c.errorHandler(e,t,r)||(console.log("Invoking error handler due to\n"+e),"function"==typeof dump&&dump("Invoking error handler due to\n"+e),-1!=e.indexOf("UnknownError")||-1!=e.indexOf("Program terminated with exit(0)")||h.didShowErrorMessage||(-1!=(e="An error occurred running the Unity content on this page. See your browser JavaScript console for more info. The error was:\n"+e).indexOf("DISABLE_EXCEPTION_CATCHING")?e="An exception has occurred, but exception handling has been disabled in this build. If you are the developer of this content, enable exceptions in your project WebGL player settings to be able to catch the exception or see the stack trace.":-1!=e.indexOf("Cannot enlarge memory arrays")?e="Out of memory. If you are the developer of this content, try allocating more memory to your WebGL build in the WebGL player settings.":-1==e.indexOf("Invalid array buffer length")&&-1==e.indexOf("Invalid typed array length")&&-1==e.indexOf("out of memory")&&-1==e.indexOf("could not allocate memory")||(e="The browser could not allocate enough memory for the WebGL content. If you are the developer of this content, try allocating less memory to your WebGL build in the WebGL player settings."),alert(e),h.didShowErrorMessage=!0))}function p(e,t){if("symbolsUrl"!=e){var r=c.downloadProgress[e],n=(r=r||(c.downloadProgress[e]={started:!1,finished:!1,lengthComputable:!1,total:0,loaded:0}),"object"!=typeof t||"progress"!=t.type&&"load"!=t.type||(r.started||(r.started=!0,r.lengthComputable=t.lengthComputable,r.total=t.total),r.loaded=t.loaded,"load"==t.type&&(r.finished=!0)),0),o=0,a=0,i=0,s=0;for(e in c.downloadProgress){if(!(r=c.downloadProgress[e]).started)return;a++,r.lengthComputable?(n+=r.loaded,o+=r.total,i++):r.finished||s++}l(.9*(a?(a-s-(o?i*(o-n)/o:0))/a:0))}}c.SystemInfo=function(){var e,t,r,n,o=navigator.userAgent+" ",a=[["Firefox","Firefox"],["OPR","Opera"],["Edg","Edge"],["SamsungBrowser","Samsung Browser"],["Trident","Internet Explorer"],["MSIE","Internet Explorer"],["Chrome","Chrome"],["CriOS","Chrome on iOS Safari"],["FxiOS","Firefox on iOS Safari"],["Safari","Safari"]];function i(e,t,r){return(e=RegExp(e,"i").exec(t))&&e[r]}for(var s=0;s<a.length;++s)if(t=i(a[s][0]+"[/ ](.*?)[ \\)]",o,1)){e=a[s][1];break}"Safari"==e&&(t=i("Version/(.*?) ",o,1)),"Internet Explorer"==e&&(t=i("rv:(.*?)\\)? ",o,1)||t);for(var l=[["Windows (.*?)[;)]","Windows"],["Android ([0-9_.]+)","Android"],["iPhone OS ([0-9_.]+)","iPhoneOS"],["iPad.*? OS ([0-9_.]+)","iPadOS"],["FreeBSD( )","FreeBSD"],["OpenBSD( )","OpenBSD"],["Linux|X11()","Linux"],["Mac OS X ([0-9_.]+)","MacOS"],["bot|google|baidu|bing|msn|teoma|slurp|yandex","Search Bot"]],d=0;d<l.length;++d)if(u=i(l[d][0],o,1)){r=l[d][1],u=u.replace(/_/g,".");break}var u={"NT 5.0":"2000","NT 5.1":"XP","NT 5.2":"Server 2003","NT 6.0":"Vista","NT 6.1":"7","NT 6.2":"8","NT 6.3":"8.1","NT 10.0":"10"}[u]||u,c=((c=document.createElement("canvas"))&&(gl=c.getContext("webgl2"),glVersion=gl?2:0,gl||(gl=c&&c.getContext("webgl"))&&(glVersion=1),gl&&(n=gl.getExtension("WEBGL_debug_renderer_info")&&gl.getParameter(37446)||gl.getParameter(7937))),"undefined"!=typeof SharedArrayBuffer),f="object"==typeof WebAssembly&&"function"==typeof WebAssembly.compile;return{width:screen.width,height:screen.height,userAgent:o.trim(),browser:e||"Unknown browser",browserVersion:t||"Unknown version",mobile:/Mobile|Android|iP(ad|hone)/.test(navigator.appVersion),os:r||"Unknown OS",osVersion:u||"Unknown OS Version",gpu:n||"Unknown GPU",language:navigator.userLanguage||navigator.language,hasWebGL:glVersion,hasCursorLock:!!document.body.requestPointerLock,hasFullscreen:!!document.body.requestFullscreen||!!document.body.webkitRequestFullscreen,hasThreads:c,hasWasm:f,hasWasmThreads:!1}}(),c.abortHandler=function(e){return h(e,"",0),!0},Error.stackTraceLimit=Math.max(Error.stackTraceLimit||0,50),c.XMLHttpRequest=function(){var i={name:"UnityCache",version:2},s={name:"XMLHttpRequest",version:1},l={name:"WebAssembly",version:1};function d(e){console.log("[UnityCache] "+e)}function a(e){return a.link=a.link||document.createElement("a"),a.link.href=e,a.link.href}function e(){var r=this;function n(e){if(void 0===r.database)for(r.database=e,r.database||d("indexedDB database could not be opened");r.queue.length;){var t=r.queue.shift();r.database?r.execute.apply(r,t.arguments):"function"==typeof t.onerror&&t.onerror(new Error("operation cancelled"))}}r.queue=[];try{var o=window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB;var a=setTimeout(function(){void 0===r.database&&n(null)},2e3),e=o.open(i.name);e.onupgradeneeded=function(e){var t=e.target.result.createObjectStore(s.name,{keyPath:"url"});["version","company","product","updated","revalidated","accessed"].forEach(function(e){t.createIndex(e,e)})},e.onsuccess=function(e){clearTimeout(a);var t,e=e.target.result;e.version<i.version?(e.close(),(t=o.open(i.name,i.version)).onupgradeneeded=function(e){e=e.target.result;e.objectStoreNames.contains(l.name)||e.createObjectStore(l.name)},t.onsuccess=function(e){n(e.target.result)},t.onerror=function(){n(null)}):n(e)},e.onerror=function(){clearTimeout(a),n(null)}}catch(e){clearTimeout(a),n(null)}}e.prototype.execute=function(e,t,r,n,o){if(this.database)try{var a=this.database.transaction([e],-1!=["put","delete","clear"].indexOf(t)?"readwrite":"readonly").objectStore(e),i=("openKeyCursor"==t&&(a=a.index(r[0]),r=r.slice(1)),a[t].apply(a,r));"function"==typeof n&&(i.onsuccess=function(e){n(e.target.result)}),i.onerror=o}catch(e){"function"==typeof o&&o(e)}else void 0===this.database?this.queue.push({arguments:arguments,onerror:o}):"function"==typeof o&&o(new Error("indexedDB access denied"))};var u=new e;function c(e,t,r,n,o){var a={url:e,version:s.version,company:t,product:r,updated:n,revalidated:n,accessed:n,responseHeaders:{},xhr:{}};return o&&(["Last-Modified","ETag"].forEach(function(e){a.responseHeaders[e]=o.getResponseHeader(e)}),["responseURL","status","statusText","response"].forEach(function(e){a.xhr[e]=o[e]})),a}function r(e){this.cache={enabled:!1},e&&(this.cache.control=e.cacheControl,this.cache.company=e.companyName,this.cache.product=e.productName),this.xhr=new XMLHttpRequest(e),this.xhr.addEventListener("load",function(){var e=this.xhr,t=this.cache;t.enabled&&!t.revalidated&&(304==e.status?(t.result.revalidated=t.result.accessed,t.revalidated=!0,u.execute(s.name,"put",[t.result]),d("'"+t.result.url+"' successfully revalidated and served from the indexedDB cache")):200==e.status?(t.result=c(t.result.url,t.company,t.product,t.result.accessed,e),t.revalidated=!0,u.execute(s.name,"put",[t.result],function(e){d("'"+t.result.url+"' successfully downloaded and stored in the indexedDB cache")},function(e){d("'"+t.result.url+"' successfully downloaded but not stored in the indexedDB cache due to the error: "+e)})):d("'"+t.result.url+"' request failed with status: "+e.status+" "+e.statusText))}.bind(this))}r.prototype.send=function(e){var n=this.xhr,o=this.cache,a=arguments;if(o.enabled=o.enabled&&"arraybuffer"==n.responseType&&!e,!o.enabled)return n.send.apply(n,a);u.execute(s.name,"get",[o.result.url],function(e){var t,r;e&&e.version==s.version?(o.result=e,o.result.accessed=Date.now(),"immutable"==o.control?(o.revalidated=!0,u.execute(s.name,"put",[o.result]),n.dispatchEvent(new Event("load")),d("'"+o.result.url+"' served from the indexedDB cache without revalidation")):(e=o.result.url,(r=window.location.href.match(/^[a-z]+:\/\/[^\/]+/))&&!e.lastIndexOf(r[0],0)||!o.result.responseHeaders["Last-Modified"]&&!o.result.responseHeaders.ETag?(o.result.responseHeaders["Last-Modified"]?(n.setRequestHeader("If-Modified-Since",o.result.responseHeaders["Last-Modified"]),n.setRequestHeader("Cache-Control","no-cache")):o.result.responseHeaders.ETag&&(n.setRequestHeader("If-None-Match",o.result.responseHeaders.ETag),n.setRequestHeader("Cache-Control","no-cache")),n.send.apply(n,a)):((t=new XMLHttpRequest).open("HEAD",o.result.url),t.onload=function(){o.revalidated=["Last-Modified","ETag"].every(function(e){return!o.result.responseHeaders[e]||o.result.responseHeaders[e]==t.getResponseHeader(e)}),o.revalidated?(o.result.revalidated=o.result.accessed,u.execute(s.name,"put",[o.result]),n.dispatchEvent(new Event("load")),d("'"+o.result.url+"' successfully revalidated and served from the indexedDB cache")):n.send.apply(n,a)},t.send()))):n.send.apply(n,a)},function(e){n.send.apply(n,a)})},r.prototype.open=function(e,t,r,n,o){return this.cache.result=c(a(t),this.cache.company,this.cache.product,Date.now()),this.cache.enabled=-1!=["must-revalidate","immutable"].indexOf(this.cache.control)&&"GET"==e&&this.cache.result.url.match("^https?://")&&(void 0===r||r)&&void 0===n&&void 0===o,this.cache.revalidated=!1,this.xhr.open.apply(this.xhr,arguments)},r.prototype.setRequestHeader=function(e,t){return this.cache.enabled=!1,this.xhr.setRequestHeader.apply(this.xhr,arguments)};var t,n=new XMLHttpRequest;for(t in n)r.prototype.hasOwnProperty(t)||!function(t){Object.defineProperty(r.prototype,t,"function"==typeof n[t]?{value:function(){return this.xhr[t].apply(this.xhr,arguments)}}:{get:function(){return(this.cache.revalidated&&this.cache.result.xhr.hasOwnProperty(t)?this.cache.result:this).xhr[t]},set:function(e){this.xhr[t]=e}})}(t);return r}();var m={gzip:{require:function(e){var t,r={"inflate.js":function(e,t,r){"use strict";var c=e("./zlib/inflate"),f=e("./utils/common"),h=e("./utils/strings"),p=e("./zlib/constants"),n=e("./zlib/messages"),o=e("./zlib/zstream"),a=e("./zlib/gzheader"),m=Object.prototype.toString;function i(e){if(!(this instanceof i))return new i(e);this.options=f.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;if(t.raw&&0<=t.windowBits&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(0<=t.windowBits&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),15<t.windowBits&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new o,this.strm.avail_out=0,(e=c.inflateInit2(this.strm,t.windowBits))!==p.Z_OK)throw new Error(n[e]);this.header=new a,c.inflateGetHeader(this.strm,this.header)}function s(e,t){if((t=new i(t)).push(e,!0),t.err)throw t.msg||n[t.err];return t.result}i.prototype.push=function(e,t){var r,n,o,a,i,s=this.strm,l=this.options.chunkSize,d=this.options.dictionary,u=!1;if(this.ended)return!1;n=t===~~t?t:!0===t?p.Z_FINISH:p.Z_NO_FLUSH,"string"==typeof e?s.input=h.binstring2buf(e):"[object ArrayBuffer]"===m.call(e)?s.input=new Uint8Array(e):s.input=e,s.next_in=0,s.avail_in=s.input.length;do{if(0===s.avail_out&&(s.output=new f.Buf8(l),s.next_out=0,s.avail_out=l),(r=c.inflate(s,p.Z_NO_FLUSH))===p.Z_NEED_DICT&&d&&(i="string"==typeof d?h.string2buf(d):"[object ArrayBuffer]"===m.call(d)?new Uint8Array(d):d,r=c.inflateSetDictionary(this.strm,i)),r===p.Z_BUF_ERROR&&!0===u&&(r=p.Z_OK,u=!1),r!==p.Z_STREAM_END&&r!==p.Z_OK)return this.onEnd(r),!(this.ended=!0)}while(!s.next_out||0!==s.avail_out&&r!==p.Z_STREAM_END&&(0!==s.avail_in||n!==p.Z_FINISH&&n!==p.Z_SYNC_FLUSH)||("string"===this.options.to?(i=h.utf8border(s.output,s.next_out),o=s.next_out-i,a=h.buf2string(s.output,i),s.next_out=o,s.avail_out=l-o,o&&f.arraySet(s.output,s.output,i,o,0),this.onData(a)):this.onData(f.shrinkBuf(s.output,s.next_out))),0===s.avail_in&&0===s.avail_out&&(u=!0),(0<s.avail_in||0===s.avail_out)&&r!==p.Z_STREAM_END);return(n=r===p.Z_STREAM_END?p.Z_FINISH:n)===p.Z_FINISH?(r=c.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===p.Z_OK):n!==p.Z_SYNC_FLUSH||(this.onEnd(p.Z_OK),!(s.avail_out=0))},i.prototype.onData=function(e){this.chunks.push(e)},i.prototype.onEnd=function(e){e===p.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=f.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Inflate=i,r.inflate=s,r.inflateRaw=function(e,t){return(t=t||{}).raw=!0,s(e,t)},r.ungzip=s},"utils/common.js":function(e,t,r){"use strict";var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array,o=(r.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var n in r)r.hasOwnProperty(n)&&(e[n]=r[n])}}return e},r.shrinkBuf=function(e,t){if(e.length!==t){if(e.subarray)return e.subarray(0,t);e.length=t}return e},{arraySet:function(e,t,r,n,o){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+n),o);else for(var a=0;a<n;a++)e[o+a]=t[r+a]},flattenChunks:function(e){for(var t,r,n,o=0,a=0,i=e.length;a<i;a++)o+=e[a].length;for(n=new Uint8Array(o),a=t=0,i=e.length;a<i;a++)r=e[a],n.set(r,t),t+=r.length;return n}}),a={arraySet:function(e,t,r,n,o){for(var a=0;a<n;a++)e[o+a]=t[r+a]},flattenChunks:function(e){return[].concat.apply([],e)}};r.setTyped=function(e){e?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,o)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,a))},r.setTyped(n)},"utils/strings.js":function(e,t,r){"use strict";var l=e("./common"),o=!0,a=!0;try{String.fromCharCode.apply(null,[0])}catch(e){o=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){a=!1}for(var d=new l.Buf8(256),n=0;n<256;n++)d[n]=252<=n?6:248<=n?5:240<=n?4:224<=n?3:192<=n?2:1;function u(e,t){if(t<65537&&(e.subarray&&a||!e.subarray&&o))return String.fromCharCode.apply(null,l.shrinkBuf(e,t));for(var r="",n=0;n<t;n++)r+=String.fromCharCode(e[n]);return r}d[254]=d[254]=1,r.string2buf=function(e){for(var t,r,n,o,a=e.length,i=0,s=0;s<a;s++)55296==(64512&(r=e.charCodeAt(s)))&&s+1<a&&56320==(64512&(n=e.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(n-56320),s++),i+=r<128?1:r<2048?2:r<65536?3:4;for(t=new l.Buf8(i),s=o=0;o<i;s++)55296==(64512&(r=e.charCodeAt(s)))&&s+1<a&&56320==(64512&(n=e.charCodeAt(s+1)))&&(r=65536+(r-55296<<10)+(n-56320),s++),r<128?t[o++]=r:(r<2048?t[o++]=192|r>>>6:(r<65536?t[o++]=224|r>>>12:(t[o++]=240|r>>>18,t[o++]=128|r>>>12&63),t[o++]=128|r>>>6&63),t[o++]=128|63&r);return t},r.buf2binstring=function(e){return u(e,e.length)},r.binstring2buf=function(e){for(var t=new l.Buf8(e.length),r=0,n=t.length;r<n;r++)t[r]=e.charCodeAt(r);return t},r.buf2string=function(e,t){for(var r,n,o=t||e.length,a=new Array(2*o),i=0,s=0;s<o;)if((r=e[s++])<128)a[i++]=r;else if(4<(n=d[r]))a[i++]=65533,s+=n-1;else{for(r&=2===n?31:3===n?15:7;1<n&&s<o;)r=r<<6|63&e[s++],n--;1<n?a[i++]=65533:r<65536?a[i++]=r:(r-=65536,a[i++]=55296|r>>10&1023,a[i++]=56320|1023&r)}return u(a,i)},r.utf8border=function(e,t){for(var r=(t=(t=t||e.length)>e.length?e.length:t)-1;0<=r&&128==(192&e[r]);)r--;return!(r<0)&&0!==r&&r+d[e[r]]>t?r:t}},"zlib/inflate.js":function(e,t,r){"use strict";var B=e("../utils/common"),O=e("./adler32"),H=e("./crc32"),A=e("./inffast"),I=e("./inftrees"),N=0,D=-2,F=1,n=852,o=592;function z(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function a(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new B.Buf16(320),this.work=new B.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function i(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=F,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new B.Buf32(n),t.distcode=t.distdyn=new B.Buf32(o),t.sane=1,t.back=-1,N):D}function s(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,i(e)):D}function l(e,t){var r,n;return!e||!e.state||(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||15<t))?D:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,s(e))}function d(e,t){var r;return e?(r=new a,(e.state=r).window=null,(r=l(e,t))!==N&&(e.state=null),r):D}var P,Z,M=!0;function j(e,t,r,n){var o;return null===(e=e.state).window&&(e.wsize=1<<e.wbits,e.wnext=0,e.whave=0,e.window=new B.Buf8(e.wsize)),n>=e.wsize?(B.arraySet(e.window,t,r-e.wsize,e.wsize,0),e.wnext=0,e.whave=e.wsize):(n<(o=e.wsize-e.wnext)&&(o=n),B.arraySet(e.window,t,r-n,o,e.wnext),(n-=o)?(B.arraySet(e.window,t,r-n,n,0),e.wnext=n,e.whave=e.wsize):(e.wnext+=o,e.wnext===e.wsize&&(e.wnext=0),e.whave<e.wsize&&(e.whave+=o))),0}r.inflateReset=s,r.inflateReset2=l,r.inflateResetKeep=i,r.inflateInit=function(e){return d(e,15)},r.inflateInit2=d,r.inflate=function(e,t){var r,n,o,a,i,s,l,d,u,c,f,h,p,m,b,g,w,v,y,k,x,_,S,E,C=0,U=new B.Buf8(4),L=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return D;12===(r=e.state).mode&&(r.mode=13),i=e.next_out,o=e.output,l=e.avail_out,a=e.next_in,n=e.input,s=e.avail_in,d=r.hold,u=r.bits,c=s,f=l,_=N;e:for(;;)switch(r.mode){case F:if(0===r.wrap)r.mode=13;else{for(;u<16;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}if(2&r.wrap&&35615===d)U[r.check=0]=255&d,U[1]=d>>>8&255,r.check=H(r.check,U,2,0),u=d=0,r.mode=2;else if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&d)<<8)+(d>>8))%31)e.msg="incorrect header check",r.mode=30;else if(8!=(15&d))e.msg="unknown compression method",r.mode=30;else{if(u-=4,x=8+(15&(d>>>=4)),0===r.wbits)r.wbits=x;else if(x>r.wbits){e.msg="invalid window size",r.mode=30;break}r.dmax=1<<x,e.adler=r.check=1,r.mode=512&d?10:12,u=d=0}}break;case 2:for(;u<16;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}if(r.flags=d,8!=(255&r.flags)){e.msg="unknown compression method",r.mode=30;break}if(57344&r.flags){e.msg="unknown header flags set",r.mode=30;break}r.head&&(r.head.text=d>>8&1),512&r.flags&&(U[0]=255&d,U[1]=d>>>8&255,r.check=H(r.check,U,2,0)),u=d=0,r.mode=3;case 3:for(;u<32;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}r.head&&(r.head.time=d),512&r.flags&&(U[0]=255&d,U[1]=d>>>8&255,U[2]=d>>>16&255,U[3]=d>>>24&255,r.check=H(r.check,U,4,0)),u=d=0,r.mode=4;case 4:for(;u<16;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}r.head&&(r.head.xflags=255&d,r.head.os=d>>8),512&r.flags&&(U[0]=255&d,U[1]=d>>>8&255,r.check=H(r.check,U,2,0)),u=d=0,r.mode=5;case 5:if(1024&r.flags){for(;u<16;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}r.length=d,r.head&&(r.head.extra_len=d),512&r.flags&&(U[0]=255&d,U[1]=d>>>8&255,r.check=H(r.check,U,2,0)),u=d=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&((h=s<(h=r.length)?s:h)&&(r.head&&(x=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),B.arraySet(r.head.extra,n,a,h,x)),512&r.flags&&(r.check=H(r.check,n,h,a)),s-=h,a+=h,r.length-=h),r.length))break e;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===s)break e;for(h=0;x=n[a+h++],r.head&&x&&r.length<65536&&(r.head.name+=String.fromCharCode(x)),x&&h<s;);if(512&r.flags&&(r.check=H(r.check,n,h,a)),s-=h,a+=h,x)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===s)break e;for(h=0;x=n[a+h++],r.head&&x&&r.length<65536&&(r.head.comment+=String.fromCharCode(x)),x&&h<s;);if(512&r.flags&&(r.check=H(r.check,n,h,a)),s-=h,a+=h,x)break e}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;u<16;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}if(d!==(65535&r.check)){e.msg="header crc mismatch",r.mode=30;break}u=d=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=12;break;case 10:for(;u<32;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}e.adler=r.check=z(d),u=d=0,r.mode=11;case 11:if(0===r.havedict)return e.next_out=i,e.avail_out=l,e.next_in=a,e.avail_in=s,r.hold=d,r.bits=u,2;e.adler=r.check=1,r.mode=12;case 12:if(5===t||6===t)break e;case 13:if(r.last)d>>>=7&u,u-=7&u,r.mode=27;else{for(;u<3;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}switch(r.last=1&d,--u,3&(d>>>=1)){case 0:r.mode=14;break;case 1:var T,T=R=void 0,R=r;if(M){for(P=new B.Buf32(512),Z=new B.Buf32(32),T=0;T<144;)R.lens[T++]=8;for(;T<256;)R.lens[T++]=9;for(;T<280;)R.lens[T++]=7;for(;T<288;)R.lens[T++]=8;for(I(1,R.lens,0,288,P,0,R.work,{bits:9}),T=0;T<32;)R.lens[T++]=5;I(2,R.lens,0,32,Z,0,R.work,{bits:5}),M=!1}if(R.lencode=P,R.lenbits=9,R.distcode=Z,R.distbits=5,r.mode=20,6!==t)break;d>>>=2,u-=2;break e;case 2:r.mode=17;break;case 3:e.msg="invalid block type",r.mode=30}d>>>=2,u-=2}break;case 14:for(d>>>=7&u,u-=7&u;u<32;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}if((65535&d)!=(d>>>16^65535)){e.msg="invalid stored block lengths",r.mode=30;break}if(r.length=65535&d,u=d=0,r.mode=15,6===t)break e;case 15:r.mode=16;case 16:if(h=r.length){if(0===(h=l<(h=s<h?s:h)?l:h))break e;B.arraySet(o,n,a,h,i),s-=h,a+=h,l-=h,i+=h,r.length-=h}else r.mode=12;break;case 17:for(;u<14;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}if(r.nlen=257+(31&d),d>>>=5,u-=5,r.ndist=1+(31&d),d>>>=5,u-=5,r.ncode=4+(15&d),d>>>=4,u-=4,286<r.nlen||30<r.ndist){e.msg="too many length or distance symbols",r.mode=30;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;u<3;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}r.lens[L[r.have++]]=7&d,d>>>=3,u-=3}for(;r.have<19;)r.lens[L[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,S={bits:r.lenbits},_=I(0,r.lens,0,19,r.lencode,0,r.work,S),r.lenbits=S.bits,_){e.msg="invalid code lengths set",r.mode=30;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;g=(C=r.lencode[d&(1<<r.lenbits)-1])>>>16&255,w=65535&C,!((b=C>>>24)<=u);){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}if(w<16)d>>>=b,u-=b,r.lens[r.have++]=w;else{if(16===w){for(E=b+2;u<E;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}if(d>>>=b,u-=b,0===r.have){e.msg="invalid bit length repeat",r.mode=30;break}x=r.lens[r.have-1],h=3+(3&d),d>>>=2,u-=2}else if(17===w){for(E=b+3;u<E;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}x=0,h=3+(7&(d>>>=b)),d>>>=3,u=u-b-3}else{for(E=b+7;u<E;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}x=0,h=11+(127&(d>>>=b)),d>>>=7,u=u-b-7}if(r.have+h>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=30;break}for(;h--;)r.lens[r.have++]=x}}if(30===r.mode)break;if(0===r.lens[256]){e.msg="invalid code -- missing end-of-block",r.mode=30;break}if(r.lenbits=9,S={bits:r.lenbits},_=I(1,r.lens,0,r.nlen,r.lencode,0,r.work,S),r.lenbits=S.bits,_){e.msg="invalid literal/lengths set",r.mode=30;break}if(r.distbits=6,r.distcode=r.distdyn,S={bits:r.distbits},_=I(2,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,S),r.distbits=S.bits,_){e.msg="invalid distances set",r.mode=30;break}if(r.mode=20,6===t)break e;case 20:r.mode=21;case 21:if(6<=s&&258<=l){e.next_out=i,e.avail_out=l,e.next_in=a,e.avail_in=s,r.hold=d,r.bits=u,A(e,f),i=e.next_out,o=e.output,l=e.avail_out,a=e.next_in,n=e.input,s=e.avail_in,d=r.hold,u=r.bits,12===r.mode&&(r.back=-1);break}for(r.back=0;g=(C=r.lencode[d&(1<<r.lenbits)-1])>>>16&255,w=65535&C,!((b=C>>>24)<=u);){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}if(g&&0==(240&g)){for(v=b,y=g,k=w;g=(C=r.lencode[k+((d&(1<<v+y)-1)>>v)])>>>16&255,w=65535&C,!(v+(b=C>>>24)<=u);){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}d>>>=v,u-=v,r.back+=v}if(d>>>=b,u-=b,r.back+=b,r.length=w,0===g){r.mode=26;break}if(32&g){r.back=-1,r.mode=12;break}if(64&g){e.msg="invalid literal/length code",r.mode=30;break}r.extra=15&g,r.mode=22;case 22:if(r.extra){for(E=r.extra;u<E;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}r.length+=d&(1<<r.extra)-1,d>>>=r.extra,u-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;g=(C=r.distcode[d&(1<<r.distbits)-1])>>>16&255,w=65535&C,!((b=C>>>24)<=u);){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}if(0==(240&g)){for(v=b,y=g,k=w;g=(C=r.distcode[k+((d&(1<<v+y)-1)>>v)])>>>16&255,w=65535&C,!(v+(b=C>>>24)<=u);){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}d>>>=v,u-=v,r.back+=v}if(d>>>=b,u-=b,r.back+=b,64&g){e.msg="invalid distance code",r.mode=30;break}r.offset=w,r.extra=15&g,r.mode=24;case 24:if(r.extra){for(E=r.extra;u<E;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}r.offset+=d&(1<<r.extra)-1,d>>>=r.extra,u-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=30;break}r.mode=25;case 25:if(0===l)break e;if(r.offset>(h=f-l)){if((h=r.offset-h)>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=30;break}p=h>r.wnext?(h-=r.wnext,r.wsize-h):r.wnext-h,h>r.length&&(h=r.length),m=r.window}else m=o,p=i-r.offset,h=r.length;for(l-=h=l<h?l:h,r.length-=h;o[i++]=m[p++],--h;);0===r.length&&(r.mode=21);break;case 26:if(0===l)break e;o[i++]=r.length,l--,r.mode=21;break;case 27:if(r.wrap){for(;u<32;){if(0===s)break e;s--,d|=n[a++]<<u,u+=8}if(f-=l,e.total_out+=f,r.total+=f,f&&(e.adler=r.check=(r.flags?H:O)(r.check,o,f,i-f)),f=l,(r.flags?d:z(d))!==r.check){e.msg="incorrect data check",r.mode=30;break}u=d=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;u<32;){if(0===s)break e;s--,d+=n[a++]<<u,u+=8}if(d!==(4294967295&r.total)){e.msg="incorrect length check",r.mode=30;break}u=d=0}r.mode=29;case 29:_=1;break e;case 30:_=-3;break e;case 31:return-4;default:return D}return e.next_out=i,e.avail_out=l,e.next_in=a,e.avail_in=s,r.hold=d,r.bits=u,(r.wsize||f!==e.avail_out&&r.mode<30&&(r.mode<27||4!==t))&&j(e,e.output,e.next_out,f-e.avail_out)?(r.mode=31,-4):(c-=e.avail_in,f-=e.avail_out,e.total_in+=c,e.total_out+=f,r.total+=f,r.wrap&&f&&(e.adler=r.check=(r.flags?H:O)(r.check,o,f,e.next_out-f)),e.data_type=r.bits+(r.last?64:0)+(12===r.mode?128:0)+(20===r.mode||15===r.mode?256:0),(0==c&&0===f||4===t)&&_===N?-5:_)},r.inflateEnd=function(e){var t;return e&&e.state?((t=e.state).window&&(t.window=null),e.state=null,N):D},r.inflateGetHeader=function(e,t){return e&&e.state&&0!=(2&(e=e.state).wrap)?((e.head=t).done=!1,N):D},r.inflateSetDictionary=function(e,t){var r,n=t.length;return!e||!e.state||0!==(r=e.state).wrap&&11!==r.mode?D:11===r.mode&&O(1,t,n,0)!==r.check?-3:j(e,t,n,n)?(r.mode=31,-4):(r.havedict=1,N)},r.inflateInfo="pako inflate (from Nodeca project)"},"zlib/constants.js":function(e,t,r){"use strict";t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},"zlib/messages.js":function(e,t,r){"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},"zlib/zstream.js":function(e,t,r){"use strict";t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},"zlib/gzheader.js":function(e,t,r){"use strict";t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},"zlib/adler32.js":function(e,t,r){"use strict";t.exports=function(e,t,r,n){for(var o=65535&e|0,a=e>>>16&65535|0,i=0;0!==r;){for(r-=i=2e3<r?2e3:r;a=a+(o=o+t[n++]|0)|0,--i;);o%=65521,a%=65521}return o|a<<16|0}},"zlib/crc32.js":function(e,t,r){"use strict";var s=function(){for(var e=[],t=0;t<256;t++){for(var r=t,n=0;n<8;n++)r=1&r?3988292384^r>>>1:r>>>1;e[t]=r}return e}();t.exports=function(e,t,r,n){var o=s,a=n+r;e^=-1;for(var i=n;i<a;i++)e=e>>>8^o[255&(e^t[i])];return-1^e}},"zlib/inffast.js":function(e,t,r){"use strict";t.exports=function(e,t){var r,n,o,a,i,s,l=e.state,d=e.next_in,u=e.input,c=d+(e.avail_in-5),f=e.next_out,h=e.output,p=f-(t-e.avail_out),m=f+(e.avail_out-257),b=l.dmax,g=l.wsize,w=l.whave,v=l.wnext,y=l.window,k=l.hold,x=l.bits,_=l.lencode,S=l.distcode,E=(1<<l.lenbits)-1,C=(1<<l.distbits)-1;e:do{for(x<15&&(k+=u[d++]<<x,x+=8,k+=u[d++]<<x,x+=8),r=_[k&E];;){if(k>>>=n=r>>>24,x-=n,0==(n=r>>>16&255))h[f++]=65535&r;else{if(!(16&n)){if(0==(64&n)){r=_[(65535&r)+(k&(1<<n)-1)];continue}if(32&n){l.mode=12;break e}e.msg="invalid literal/length code",l.mode=30;break e}for(o=65535&r,(n&=15)&&(x<n&&(k+=u[d++]<<x,x+=8),o+=k&(1<<n)-1,k>>>=n,x-=n),x<15&&(k+=u[d++]<<x,x+=8,k+=u[d++]<<x,x+=8),r=S[k&C];;){if(k>>>=n=r>>>24,x-=n,!(16&(n=r>>>16&255))){if(0==(64&n)){r=S[(65535&r)+(k&(1<<n)-1)];continue}e.msg="invalid distance code",l.mode=30;break e}if(a=65535&r,x<(n&=15)&&(k+=u[d++]<<x,(x+=8)<n&&(k+=u[d++]<<x,x+=8)),b<(a+=k&(1<<n)-1)){e.msg="invalid distance too far back",l.mode=30;break e}if(k>>>=n,x-=n,(n=f-p)<a){if(w<(n=a-n)&&l.sane){e.msg="invalid distance too far back",l.mode=30;break e}if(s=y,(i=0)===v){if(i+=g-n,n<o){for(o-=n;h[f++]=y[i++],--n;);i=f-a,s=h}}else if(v<n){if(i+=g+v-n,(n-=v)<o){for(o-=n;h[f++]=y[i++],--n;);if(i=0,v<o){for(o-=n=v;h[f++]=y[i++],--n;);i=f-a,s=h}}}else if(i+=v-n,n<o){for(o-=n;h[f++]=y[i++],--n;);i=f-a,s=h}for(;2<o;)h[f++]=s[i++],h[f++]=s[i++],h[f++]=s[i++],o-=3;o&&(h[f++]=s[i++],1<o&&(h[f++]=s[i++]))}else{for(i=f-a;h[f++]=h[i++],h[f++]=h[i++],h[f++]=h[i++],2<(o-=3););o&&(h[f++]=h[i++],1<o&&(h[f++]=h[i++]))}break}}break}}while(d<c&&f<m);k&=(1<<(x-=(o=x>>3)<<3))-1,e.next_in=d-=o,e.next_out=f,e.avail_in=d<c?c-d+5:5-(d-c),e.avail_out=f<m?m-f+257:257-(f-m),l.hold=k,l.bits=x}},"zlib/inftrees.js":function(e,t,r){"use strict";var A=e("../utils/common"),I=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],N=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],D=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],F=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(e,t,r,n,o,a,i,s){for(var l,d,u,c,f,h,p,m,b,g=s.bits,w=0,v=0,y=0,k=0,x=0,_=0,S=0,E=0,C=0,U=0,L=null,T=0,R=new A.Buf16(16),B=new A.Buf16(16),O=null,H=0,w=0;w<=15;w++)R[w]=0;for(v=0;v<n;v++)R[t[r+v]]++;for(x=g,k=15;1<=k&&0===R[k];k--);if(k<x&&(x=k),0===k)o[a++]=20971520,o[a++]=20971520,s.bits=1;else{for(y=1;y<k&&0===R[y];y++);for(x<y&&(x=y),w=E=1;w<=15;w++)if((E=(E<<=1)-R[w])<0)return-1;if(0<E&&(0===e||1!==k))return-1;for(B[1]=0,w=1;w<15;w++)B[w+1]=B[w]+R[w];for(v=0;v<n;v++)0!==t[r+v]&&(i[B[t[r+v]]++]=v);if(h=0===e?(L=O=i,19):1===e?(L=I,T-=257,O=N,H-=257,256):(L=D,O=F,-1),w=y,f=a,S=v=U=0,u=-1,c=(C=1<<(_=x))-1,1===e&&852<C||2===e&&592<C)return 1;for(;;){for(b=i[v]<h?(m=0,i[v]):i[v]>h?(m=O[H+i[v]],L[T+i[v]]):(m=96,0),l=1<<(p=w-S),y=d=1<<_;o[f+(U>>S)+(d-=l)]=p<<24|m<<16|b|0,0!==d;);for(l=1<<w-1;U&l;)l>>=1;if(U=0!==l?(U&l-1)+l:0,v++,0==--R[w]){if(w===k)break;w=t[r+i[v]]}if(x<w&&(U&c)!==u){for(f+=y,E=1<<(_=w-(S=0===S?x:S));_+S<k&&!((E-=R[_+S])<=0);)_++,E<<=1;if(C+=1<<_,1===e&&852<C||2===e&&592<C)return 1;o[u=U&c]=x<<24|_<<16|f-a|0}}0!==U&&(o[f+U]=w-S<<24|64<<16|0),s.bits=x}return 0}}};for(t in r)r[t].folder=t.substring(0,t.lastIndexOf("/")+1);function n(e,t){var r=t.match(/^\//)?null:e?t.match(/^\.\.?\//)?o(e.folder+t):a(e,t):o(t);if(r)return r.exports||(r.parent=e,r(n.bind(null,r),r,r.exports={})),r.exports;throw"module not found: "+t}var o=function(e){var t=[];return(e=e.split("/").every(function(e){return".."==e?t.pop():"."==e||""==e||t.push(e)})?t.join("/"):null)?r[e]||r[e+".js"]||r[e+"/index.js"]:null},a=function(e,t){return e?o(e.folder+"node_modules/"+t)||a(e.parent,t):null};return n(null,e)},decompress:function(e){this.exports||(this.exports=this.require("inflate.js"));try{return this.exports.inflate(e)}catch(e){}},hasUnityMarker:function(e){var t=10,r="UnityWeb Compressed Content (gzip)";if(t>e.length||31!=e[0]||139!=e[1])return!1;var n=e[3];if(4&n){if(t+2>e.length)return!1;if((t+=2+e[t]+(e[t+1]<<8))>e.length)return!1}if(8&n){for(;t<e.length&&e[t];)t++;if(t+1>e.length)return!1;t++}return 16&n&&String.fromCharCode.apply(null,e.subarray(t,t+r.length+1))==r+"\0"}}};function b(d){return new Promise(function(s,e){p(d);var l=c.companyName&&c.productName?new c.XMLHttpRequest({companyName:c.companyName,productName:c.productName,cacheControl:c.cacheControl(c[d])}):new XMLHttpRequest;l.open("GET",c[d]),l.responseType="arraybuffer",l.addEventListener("progress",function(e){p(d,e)}),l.addEventListener("load",function(e){p(d,e);var t,r,n,o=new Uint8Array(l.response),a=c[d],i=s;for(t in m)if(m[t].hasUnityMarker(o))return void(a&&console.log('You can reduce startup time if you configure your web server to add "Content-Encoding: '+t+'" response header when serving "'+a+'" file.'),(t=m[t]).worker||(r=URL.createObjectURL(new Blob(["this.require = ",t.require.toString(),"; this.decompress = ",t.decompress.toString(),"; this.onmessage = ",function(e){e={id:e.data.id,decompressed:this.decompress(e.data.compressed)};postMessage(e,e.decompressed?[e.decompressed.buffer]:[])}.toString(),"; postMessage({ ready: true });"],{type:"application/javascript"})),t.worker=new Worker(r),t.worker.onmessage=function(e){e.data.ready?URL.revokeObjectURL(r):(this.callbacks[e.data.id](e.data.decompressed),delete this.callbacks[e.data.id])},t.worker.callbacks={},t.worker.nextCallbackId=0),n=t.worker.nextCallbackId++,t.worker.callbacks[n]=i,t.worker.postMessage({id:n,compressed:o},[o.buffer]));i(o)}),l.addEventListener("error",function(e){var t="Failed to download file "+c[d];"file:"==location.protocol?u(t+". Loading web pages via a file:// URL without a web server is not supported by this browser. Please use a local development web server to host Unity content, or use the Unity Build and Run option.","error"):console.error(t)}),l.send()})}function g(){Promise.all([b("frameworkUrl").then(function(e){var s=URL.createObjectURL(new Blob([e],{type:"application/javascript"}));return new Promise(function(a,e){var i=document.createElement("script");i.src=s,i.onload=function(){if("undefined"==typeof unityFramework||!unityFramework){var e,t=[["br","br"],["gz","gzip"]];for(e in t){var r,n=t[e];if(c.frameworkUrl.endsWith("."+n[0]))return r="Unable to parse "+c.frameworkUrl+"!","file:"==location.protocol?void u(r+" Loading pre-compressed (brotli or gzip) content via a file:// URL without a web server is not supported by this browser. Please use a local development web server to host compressed Unity content, or use the Unity Build and Run option.","error"):(r+=' This can happen if build compression was enabled but web server hosting the content was misconfigured to not serve the file with HTTP Response Header "Content-Encoding: '+n[1]+'" present. Check browser Console and Devtools Network tab to debug.',"br"==n[0]&&"http:"==location.protocol&&(n=-1!=["localhost","127.0.0.1"].indexOf(location.hostname)?"":"Migrate your server to use HTTPS.",r=/Firefox/.test(navigator.userAgent)?"Unable to parse "+c.frameworkUrl+'!<br>If using custom web server, verify that web server is sending .br files with HTTP Response Header "Content-Encoding: br". Brotli compression may not be supported in Firefox over HTTP connections. '+n+' See <a href="https://bugzilla.mozilla.org/show_bug.cgi?id=1670675">https://bugzilla.mozilla.org/show_bug.cgi?id=1670675</a> for more information.':"Unable to parse "+c.frameworkUrl+'!<br>If using custom web server, verify that web server is sending .br files with HTTP Response Header "Content-Encoding: br". Brotli compression may not be supported over HTTP connections. Migrate your server to use HTTPS.'),void u(r,"error"))}u("Unable to parse "+c.frameworkUrl+"! The file is corrupt, or compression was misconfigured? (check Content-Encoding HTTP Response Header on web server)","error")}var o=unityFramework;unityFramework=null,i.onload=null,URL.revokeObjectURL(s),a(o)},i.onerror=function(e){u("Unable to load file "+c.frameworkUrl+"! Check that the file exists on the remote server. (also check browser Console and Devtools Network tab to debug)","error")},document.body.appendChild(i),c.deinitializers.push(function(){document.body.removeChild(i)})})}),b("codeUrl")]).then(function(e){c.wasmBinary=e[1],e[0](c)});var e=b("dataUrl");c.preRun.push(function(){c.addRunDependency("dataUrl"),e.then(function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),r=0,n="UnityWebData1.0\0";if(!String.fromCharCode.apply(null,e.subarray(r,r+n.length))==n)throw"unknown data format";var o=t.getUint32(r+=n.length,!0);for(r+=4;r<o;){var a=t.getUint32(r,!0),i=(r+=4,t.getUint32(r,!0)),s=(r+=4,t.getUint32(r,!0)),l=(r+=4,String.fromCharCode.apply(null,e.subarray(r,r+s)));r+=s;for(var d=0,u=l.indexOf("/",d)+1;0<u;d=u,u=l.indexOf("/",d)+1)c.FS_createPath(l.substring(0,d),l.substring(d,u-1),!0,!0);c.FS_createDataFile(l,null,e.subarray(a,a+i),!0,!0,!0)}c.removeRunDependency("dataUrl")})})}return new Promise(function(e,t){c.SystemInfo.hasWebGL?c.SystemInfo.hasWasm?(1==c.SystemInfo.hasWebGL&&c.print('Warning: Your browser does not support "WebGL 2.0" Graphics API, switching to "WebGL 1.0"'),c.startupErrorHandler=t,l(0),c.postRun.push(function(){l(1),delete c.startupErrorHandler,e(f)}),g()):t("Your browser does not support WebAssembly."):t("Your browser does not support WebGL.")})}