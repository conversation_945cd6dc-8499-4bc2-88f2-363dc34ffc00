<template>
  <a-spin :spinning="confirmLoading">
    <a-form slot="detail" class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="24">
            <a-form-item label="告警类型"   :labelCol="labelCols">
              <span v-if="formState.alarmType == '1'">疑似火警</span>
              <span v-if="formState.alarmType == '2'">故障</span>
              <span v-if="formState.alarmType == '3'">隐患</span>
              <span v-if="formState.alarmType == '4'">其他</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备名称"   :labelCol="labelCol">
              <span>{{formState.deviceName}}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="设备位置" :labelCol="labelCol">
              <span>{{formState.deviceLocation}}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="告警内容" :labelCol="labelCol">
              <span>{{formState.alarmDescription}}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="告警时间" :labelCol="labelCol">
              <span>{{formState.alarmTime}}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="处理过程" :labelCol="labelCol">
              <span>{{formState.repairProcess}}</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="维修时间" :labelCol="labelCol">
              <span>{{formState.repairTime}}</span>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="处理照片" :labelCol="labelCols">
              <Image :width="200" :src="formState.repairPicture"/>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
  import {Form, Image} from 'ant-design-vue';
  import { defineComponent, ref, reactive, onMounted, defineProps, defineExpose, UnwrapRef, nextTick, defineEmits } from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import JFormContainer from '@/components/Form/src/jeecg/components/JFormContainer.vue';

  const props = defineProps(["disabled"])
  const emits = defineEmits(['success', 'register', 'ok'])

  const confirmLoading = ref<boolean>(false);
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 6 } });
  const labelCols = ref<any>({ xs: { span: 24 }, sm: { span: 3 } });
  const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 16}});

  const formState = reactive({
    // *********************************
    alarmType: '', //告警类型
    deviceName: '', //设备名称
    deviceLocation: '', //设备位置
    alarmDescription: '', //告警内容
    alarmTime: '', //告警时间
    repairProcess: '', //维修时间
    repairTime: '', //处理过程
    repairPicture: '', //处理照片
  });
  const validatorRules = {
  };

  const useForm = Form.useForm;
  const {resetFields, validate, validateInfos} = useForm(formState, validatorRules, {immediate: false});

  const Api = reactive({
  });


  function add() {
    edit({});
  }

  function edit(record) {
    nextTick(() => {
      resetFields();
      Object.assign(formState, record);
      formState.repairPicture=formState.repairPicture?window._CONFIG['imagesUrl']+formState.repairPicture:""
    });
  }

  /**
   * 提交数据
   */
  function submitForm() {
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style scoped>
  .antd-modal-form {
    padding: 24px 24px 24px 24px;
  }
</style>
