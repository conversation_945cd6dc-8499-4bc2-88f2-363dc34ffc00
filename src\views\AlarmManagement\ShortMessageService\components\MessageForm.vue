<template>
  <a-spin :spinning="confirmLoading">
    <a-form class="antd-modal-form" ref="formRef" :model="formState" :rules="validatorRules">
      <JFormContainer :disabled="disabled">
        <a-row>
          <a-col :span="24">
            <a-form-item label="短信平台" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="messagePlatform"
                         v-bind="validateInfos.messagePlatform">
              {{ formState.messagePlatform }}
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="AccessKey ID" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="messageKey"
                         v-bind="validateInfos.messageKey">
              <a-input placeholder="请输入AccessKey ID" v-model:value="formState.messageKey"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="AccessKey Secret" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="messageSecret"
                         v-bind="validateInfos.messageSecret">
              <a-input placeholder="请输入AccessKey Secret"
                       v-model:value="formState.messageSecret"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="Sign" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="messageSign"
                         v-bind="validateInfos.messageSign">
              <a-input placeholder="请输入Sign" v-model:value="formState.messageSign"/>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="短信服务开关" :labelCol="labelCol" :wrapperCol="wrapperCol"
                         name="serverSwitch"
                         v-bind="validateInfos.serverSwitch">
              <JSwitch v-model:value="formState.serverSwitch" :options="['1', '0']"
                       :labelOptions="['开启', '关闭']" checkedChildren="开启"
                       unCheckedChildren="关闭">
              </JSwitch>
            </a-form-item>
          </a-col>
        </a-row>
      </JFormContainer>
    </a-form>
  </a-spin>
</template>

<script lang="ts" setup>
import {Form} from 'ant-design-vue';
import {
  defineComponent,
  ref,
  reactive,
  onMounted,
  defineProps,
  defineExpose,
  UnwrapRef,
  nextTick,
  defineEmits
} from 'vue';
import {useMessage} from '/@/hooks/web/useMessage';
import {defHttp} from '/@/utils/http/axios';
import {useUserStore} from '/@/store/modules/user';
import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';

const userStore = useUserStore();
const props = defineProps(["disabled"])
const emits = defineEmits(['success', 'register', 'ok'])

const useForm = Form.useForm;
const confirmLoading = ref<boolean>(false);
const labelCol = ref<any>({xs: {span: 24}, sm: {span: 8}});
const wrapperCol = ref<any>({xs: {span: 24}, sm: {span: 16}});
const labelCol1 = ref<any>({xs: {span: 24}, sm: {span: 4}});
const wrapperCol1 = ref<any>({xs: {span: 24}, sm: {span: 20}});
const {createMessage} = useMessage();
const Api = reactive({
  edit: '/business/dtMessageConfig/editMessageConfig',
});
const formState = reactive({
  id: '',
  messagePlatform: '',
  messageKey: '',
  messageSecret: '',
  messageSign: '',
  serverSwitch: 1,
});

//表单验证
const validatorRules = {
  messageKey: [{required: true, message: '请输入AccessKey ID!'}],
  messageSecret: [{required: true, message: '请输入AccessKey Secret!'}],
};
const {
  resetFields,
  validate,
  validateInfos
} = useForm(formState, validatorRules, {immediate: false});

function add() {
  edit({});
}

function edit(record) {
  nextTick(() => {
    resetFields();
    const tmpData = {};
    Object.keys(formState).forEach((key) => {
      if (record.hasOwnProperty(key)) {
        tmpData[key] = record[key]
      }
    })
    Object.assign(formState, tmpData);
  });
}

/**
 * 提交数据
 */
async function submitForm() {
  await validate();
  confirmLoading.value = true;
  let httpurl = '';
  let method = '';
  //时间格式化
  let model = {
    id: formState.id,
    messageKey: formState.messageKey,
    messageSecret: formState.messageSecret,
    messageSign: formState.messageSign,
    serverSwitch: formState.serverSwitch,
  };
  httpurl += Api.edit;
  method = 'put';
  defHttp.request({
    url: httpurl,
    params: model,
    method: method,
  }, {isTransformResponse: false}).then((res) => {
    if (res.success) {
      createMessage.success(res.message);
      emits('ok');
    } else {
      createMessage.warning(res.message);
    }
  }).finally(() => {
    confirmLoading.value = false;
  });
}

defineExpose({
  add,
  edit,
  submitForm
});
</script>

<style scoped>
.antd-modal-form {
  padding: 24px 24px 24px 24px;
}
</style>
