<template>
  <a-time-picker
    :placeholder="placeholder"
    :value="value"
    format="HH:mm"
    :getPopupContainer="getParentContainer"
    @change="handleChange"
  />
</template>

<script>
  import {defineComponent, ref, watch} from 'vue';
  import dayjs from 'dayjs';

  /**
   * 用于时间-time组件的范围查询
   */
  export default defineComponent({
    name: "TimeSelect",
    props: ["value", "placeholder", 'disabled', 'size'],
    emits: ['change', 'update:value'],
    setup(props, {emit}) {
      const value = ref(undefined)
      const placeholder = ref("请选择开始时间")
      watch(() => props.value, (val) => {
        if (val) {
          value.value = dayjs(val, "HH:mm");
        } else {
          value.value = val
        }
      }, {immediate: true});

      function getParentContainer(node) {
        if (document.getElementsByClassName('full')[0]) {
          return document.getElementsByClassName('full')[0]
        } else {
          return document.getElementsByClassName('ant-scrolling-effect')[0]
        }
      }

      function handleChange(date, dateString) {
        value.value = dateString
        emit('change', dateString);
        emit('update:value', dateString);
      }

      return {
        value,
        placeholder,
        getParentContainer,
        handleChange
      }
    }
  });
</script>
