<!-- 故障规则配置 -->
<template>
    <a-card :bordered="false">
        <!--自定义查询区域-->
        <div class="jeecg-basic-table-form-container" @keyup.enter="searchQuery">
            <a-form ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
                <a-row :gutter="24">
                    <a-col :lg="6">
                        <a-form-item label="告警级别">
                            <JSelectMultiple @change="getSelectCategory" v-model:value="alarmLevel" placeholder="请选择告警级别"
                                :options="options3" mode="default" :triggerChange="false">
                            </JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :lg="6">
                        <a-form-item label="设备分类">
                            <JSelectMultiple @change="getSelectCategory" v-model:value="deviceCategory"
                                placeholder="请选择设备分类" :options="options" mode="default" :triggerChange="false">
                            </JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :lg="6">
                        <a-form-item label="设备类型">
                            <JSelectMultiple v-model:value="deviceType" placeholder="请选择设备类型" :options="options2"
                                mode="default" :triggerChange="false"></JSelectMultiple>
                        </a-form-item>
                    </a-col>
                    <a-col :lg="6">
                        <a-form-item label="告警码">
                            <a-input placeholder="请选择所属系统" v-model:value="alarmCode"></a-input>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </div>
        <!-- 操作按钮区域 -->
        <div class="table-operator">
            <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery"
                style="margin-right: 5px">查询</a-button>
            <a-button @click="handleAdd" type="primary" preIcon="ant-design:plus" style="margin-right: 5px">新增</a-button>
        </div>
        <!-- table区域-begin -->
        <JVxeTable ref="tableRef" bordered row-number keep-source resizable :maxHeight="484" :loading="loading"
            :dataSource="dataSource" :columns="columns" :pagination="pagination" style="margin-top: 8px" row-selection
            @pageChange="handlePageChange">
            <template #status="props">
                <span>{{ props.row.status == 0 ? '未启用' : '已启用' }}</span>
            </template>
            <template #action="props">
                <a @click="handleEdit(props.row)">编辑</a>
                <a-divider type="vertical" />
                <Popconfirm title="请确认是否删除？" @confirm="handleDel(props.row.id)">
                    <a>删除</a>
                </Popconfirm>
            </template>
        </JVxeTable>
    </a-card>
    <DeptModal ref="DeptModal1" @ok="handleSuccess" />
</template>
<!--巡检项次-->
<script lang="ts" setup>
import { onMounted, ref, reactive } from 'vue';
import { defHttp } from '/@/utils/http/axios';
import { Popconfirm } from 'ant-design-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { loadCategoryData } from '/@/api/common/api';
import { initDictOptions } from '/@/utils/dict';
import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
import DeptModal from './components/DeptModal.vue';

const DeptModal1 = ref();

//-----自定义查询----begin--------
const labelCol = reactive({
    xs: { span: 24 },
    sm: { span: 10 },
})
const wrapperCol = reactive({
    xs: { span: 24 },
    sm: { span: 14 },
})
const loading = ref<boolean>(false);
const dictOptions = ref<any>([]);
let alarmLevel = ref<string>('')
let deviceCategory = ref<string>('')
let deviceType = ref('')
let alarmCode = ref<string>('')

const options = ref<any>([])
const options2 = ref<any>([])
const options3 = ref<any>([])
//表头
const columns = ref([
    {
        title: '设备分类',
        key: 'deviceCategoryName',
        align: "center",
        minWidth: 150,
    },
    {
        title: '设备类型',
        key: 'deviceTypeName',
        minWidth: 150,
        align: "center",
    },
    {
        title: '告警码',
        key: 'alarmCode',
        minWidth: 150,
        align: "center",
    },
    {
        title: '告警级别',
        key: 'alarmLevel_dictText',
        minWidth: 100,
        align: "center",
    },
    {
        title: '告警描述',
        key: 'alarmDescription',
        minWidth: 150,
        align: "center",
    }, {
        title: '可能原因',
        key: 'possibleCauses',
        minWidth: 150,
        align: "center",
    },
    {
        title: '可能后果',
        key: 'possibleConsequences',
        minWidth: 220,
        align: "center",
    },
    {
        title: '建议措施',
        key: 'proposalMeasures',
        minWidth: 150,
        align: "center",
    },
    {
        title: '是否启用',
        key: 'status',
        minWidth: 100,
        align: "center",
        type: "slot",
        slotName: 'status',
    },
    {
        title: '操作',
        type: "slot",
        key: 'action',
        align: 'center',
        fixed: 'right',
        width: 150,
        slotName: 'action',
    },
]);
const dataSource = ref<any>([]);
const Api = reactive<any>({
    list: '/sys/dtAlarmBase/pageList',
    delete: '/sys/dtAlarmBase/delAlarmBase',
    initRoleAndUser: '/sys/dtUnit/initRoleAndUser',//初始化单位角色和账号
    initUserPassword: '/sys/dtUnit/initUserPassword',//初始化用户密码
    getDeviceCategoryList: '/sys/dtDeviceCategory/getDeviceCategoryList',
    getDeviceTypeList: '/sys/dtDeviceCategory/getDeviceTypeList',
    getAlarmCode: '/sys/dictItem/list',
    getAlarmLevel: '/sys/dict/getDictItemList',
});
onMounted(() => {
    defHttp.get({ url: Api.getAlarmLevel, params: { dictCode: 'alarmLevel' } }).then((e) => {
        options3.value = e
    });
    defHttp.get({ url: Api.getDeviceCategoryList }).then((e) => {
        e.forEach(i => {
            i.label = i.deviceCategory
            i.value = i.id
        });
        options.value = e
    });
    dictOptions.value['kaiguan'] = [
        { text: '是', value: '1' },
        { text: '否', value: '2' },
    ];
    //初始加载页面
    loadData();
    //初始化字典选项
    initDictConfig();
});

const getSelectCategory = (e: Object): void => {
    options2.length = 0
    defHttp.get({ url: Api.getDeviceTypeList, params: { parentId: e } }).then((res) => {
        if (res.length != 0) {
            res.forEach(i => {
                i.label = i.deviceType
                i.value = i.id
            });
            options2.value = res
        }
    });
}

const pagination = ref<any>({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20', '30'],
    showTotal: (total, range) => {
        return range[0] + '-' + range[1] + ' 共' + total + '条';
    },
    showQuickJumper: true,
    showSizeChanger: true,
    total: dataSource.value.length,
});

const selectedRowKeys = ref<any>([]);
const selectionRows = ref<any>([]);
const { createMessage } = useMessage();



// 当分页参数变化时触发的事件
function handlePageChange(event) {
    // 重新赋值
    pagination.value.current = event.current;
    pagination.value.pageSize = event.pageSize;
    // 查询数据
    loadData();
}
function getQueryParams() {
    console.log(alarmCode, deviceCategory);

    return {
        alarmCode: alarmCode.value,
        alarmLevel: alarmLevel.value,
        deviceCategory: deviceCategory.value,
        deviceType: deviceType.value,
        pageNo: pagination.value.current,
        pageSize: pagination.value.pageSize,
    }
}
/**
 * 初始化数据
 */
function loadData(arg) {
    if (arg === 1) {
        pagination.value.current = 1;
    }
    loading.value = true;
    defHttp.get({ url: Api.list, params: getQueryParams() }, { joinParamsToUrl: false }).then((res) => {
        if (!res.success) {
            console.log(res);

            dataSource.value = res.records;
            if (res && res.total) {
                pagination.value.total = res.total;
            } else {
                pagination.value.total = 0;
            }
        } else {
            createMessage.warning(res.message);
        }
    })
        .finally(() => {
            loading.value = false;
        });
}
//查询
function searchQuery() {
    loadData(1);
    selectedRowKeys.value = [];
    selectionRows.value = [];
}
function handleAdd() {
    DeptModal1.value.disableSubmit = false;
    DeptModal1.value.add();
}
function handleEdit(record) {
    DeptModal1.value.disableSubmit = false;
    DeptModal1.value.edit(record);
}
function handleDel(record) {
    defHttp.delete({ url: Api.delete, data: { id: record } }, {  isTransformResponse: false,joinParamsToUrl: true }).then((res) => {
       if(res.success){
           loadData();
           createMessage.success(res.message);
       }else {
           createMessage.warning(res.message);
       }

    });
}
/**
 * 初始化字典选项
 */
async function initDictConfig() {
}
/**
 * 保存表单后回调事件
 */
function handleSuccess() {
    selectedRowKeys.value = [];
    selectionRows.value = [];
    loadData(1);
}


</script>
<style lang="less" scoped>
.jeecg-basic-table-form-container {
    padding: 0px;

    .table-page-search-submitButtons {
        display: block;
        margin-bottom: 0;
        white-space: nowrap;
    }

    .ant-form-item {
        margin-bottom: 8px !important;
    }
}
</style>
