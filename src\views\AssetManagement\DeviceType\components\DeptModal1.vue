<template>
  <BasicModal :title="title" :width="width" :visible="visible" :height="600" @ok="handleOk" :okText="'确认'"
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" @cancel="handleCancel" cancelText="关闭">
    <JVxeTable :rowSelection="true" ref="tableRef" bordered row-number keep-source resizable :maxHeight="484"
      :dataSource="dataSource" :columns="columns" :pagination="pagination" style="margin-top: 8px"
      @pageChange="handlePageChange">
      <template #parameter1="props">
        <img src="@/assets/images/guaz.png" class="j-vxe-image" style="height: 25px;" />
      </template>
      <template #action="props">
        <a @click="handleEditType(props.row)">编辑</a>
        <a-divider type="vertical" />
        <Popconfirm title="删请确认是否删除？" @confirm="">
          <a>删除</a>
        </Popconfirm>
      </template>
    </JVxeTable>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, defineExpose, reactive, defineEmits } from 'vue';
import { BasicModal } from '/@/components/Modal';
import { defHttp } from '/@/utils/http/axios';

const title = ref<string>('类型创建2');
const width = ref<number>(800);
const visible = ref<boolean>(false);
const disableSubmit = ref<boolean>(false);
const realForm = ref();
const emit = defineEmits(['register', 'ok']);
// 当分页参数变化时触发的事件
function handlePageChange(event) {
  // 重新赋值
  pagination.value.current = event.current;
  pagination.value.pageSize = event.pageSize;
  // 查询数据
  loadData();
}
//表头
const columns = ref([
  {
    title: '图标',
    key: 'logoUrl',
    align: "center",
    type: "slot",
    slotName: 'logoUrl',
  },
  {
    title: '设备设施类型',
    key: 'deviceType',
    align: "center",
  },
  {
    title: '序值',
    key: 'dataSort',
    align: "center",
  }, {
    title: '操作',
    type: "slot",
    key: 'action',
    align: 'center',
    fixed: 'right',
    width: 150,
    slotName: 'action',
  },
]);

const dataSource = ref<any>([]);
const pagination = ref<any>({
  current: 1,
  pageSize: 10,
  pageSizeOptions: ['10', '20', '30'],
  showTotal: (total, range) => {
    return range[0] + '-' + range[1] + ' 共' + total + '条';
  },
  showQuickJumper: true,
  showSizeChanger: true,
  total: dataSource.length,
});
const Api = reactive({
  list: '/sys/dtDeviceCategory/getDeviceTypeList',
});
let ids = ref('')
function add(e) {
  ids = e.id
  visible.value = true;
  loadDatas(e.id)
}
function loadDatas(e) {
  dataSource.length = 0
  let params = {
    parentId: e
  }
  defHttp.get({ url: Api.list, params }).then((res) => {
    dataSource.value = res
  });
}
function edit(record) {
  // title.value = disableSubmit.value ? '详情' : '编辑';
  visible.value = true;
}
function handleSuccess() {
  loadDatas(ids)
}

function handleOk() {
  realForm.value.submitForm();
  loadDatas(ids)
}

function submitCallback() {
  handleCancel();
  emit('ok');
}

function handleCancel() {
  visible.value = false;
}

defineExpose({
  add,
  edit,
});
</script>

<style lang="less" scoped></style>
