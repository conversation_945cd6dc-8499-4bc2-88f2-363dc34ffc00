<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
    <title>点聚合</title>
    <link rel="stylesheet" href="https://a.amap.com/jsapi_demos/static/demo-center/css/demo-center.css"/>
    <style>
        html, body, #container {
            height: 100%;
            width: 100%;
        }
    </style>
</head>
<body>
<div id="container" class="map" tabindex="0"></div>
<script src="//a.amap.com/jsapi_demos/static/data/weight.js"></script>

<script src="//webapi.amap.com/maps?v=2.0&key=f321b651170b76b298d05d0914d8fe99&plugin=AMap.MarkerCluster"></script>

<script type="text/javascript">
    var map = new AMap.Map("container", {
        turboMode: false,
        showIndoorMap: false,
        defaultCursor: "pointer",
        showBuildingBlock: false,
        zooms: [14, 20],
        showLabel: false,
        zoom: 15,
        pitch: 55,
        rotation: -45,
        center: [121.44500211189806, 31.202250090600185],
        forceVector: true,
        mapStyle: "amap://styles/darkblue",
    });
    var count = points.length;
    // 数据中增加权重信息，以权重高的点为中心进行聚合
    // 本示例中北京等城市中心点权重较高
    // var points = [
    // { weight: 8, lnglat:["116.408032","39.909729"], "name": '北京'},
    // { weight: 8, lnglat:["121.461743","31.231584"], "name": '上海'},
    // { weight: 8, lnglat:["113.265942","23.08983"], "name": '广州'},
    // { weight: 8, lnglat:["104.059399","30.562253"], "name": '成都'},
    // { weight: 1, lnglat: ["108.939621", "34.343147"] },
    // { weight: 1, lnglat: ["112.985037", "23.15046"] },
    // ...
    // ]
    var _renderClusterMarker = function (context) {
        // 聚合中点个数
        var clusterCount = context.count;
        var div = document.createElement('div');
        // 聚合点配色
        var defaultColor = [
            '204,235,197',
            '168,221,181',
            '123,204,196',
            '78,179,211',
            '43,140,190',
        ]
        if(clusterCount >= 0 && clusterCount < 10) {
            bgColor = defaultColor[0];
        } else if(clusterCount >= 10 && clusterCount < 100){
            bgColor = defaultColor[1];
        } else if(clusterCount >= 100 && clusterCount < 1000) {
            bgColor = defaultColor[2];
        } else if(clusterCount >= 1000 && clusterCount < 10000) {
            bgColor = defaultColor[3];
        } else if(clusterCount >= 10000) {
            bgColor = defaultColor[4];
        }
        div.style.backgroundColor = 'rgba(' + bgColor + ',.5)';
        var size = Math.round(25 + Math.pow(clusterCount/count, 1 / 5) * 40);
        div.style.width = div.style.height = size + 'px';
        div.style.border = 'solid 1px rgba(' + bgColor + ',1)';
        div.style.borderRadius = size / 2 + 'px';
        div.innerHTML = context.count;
        div.style.lineHeight = size + 'px';
        div.style.color = '#ffffff';
        div.style.fontSize = '12px';
        div.style.textAlign = 'center';
        context.marker.setOffset(new AMap.Pixel(-size / 2, -size / 2));
        context.marker.setContent(div);
    };
    var _renderMarker = function(context) {
        var content = '<div style="background-color: rgba(255,255,178,.9); height: 18px; width: 18px; border: 1px solid rgba(255,255,178,1); border-radius: 12px; box-shadow: rgba(0, 0, 0, 1) 0px 0px 3px;"></div>';
        var offset = new AMap.Pixel(-9, -9);
        context.marker.setContent(content)
        context.marker.setOffset(offset)
    }
    var cluster = new AMap.MarkerCluster(map, points, {
        gridSize: 60, // 聚合网格像素大小
        renderClusterMarker: _renderClusterMarker, // 自定义聚合点样式
        renderMarker: _renderMarker,   // 自定义非聚合点样式
    });

</script>
</body>
</html>
