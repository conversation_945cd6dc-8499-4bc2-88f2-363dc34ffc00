<template>
  <Cascader :value="cascaderValue" :placeholder="placeholder" :options="getOptions" @change="handleChange"/>
</template>
<script lang="ts">
  import {defineComponent, PropType, ref, reactive, watchEffect, computed, unref, watch, onMounted} from 'vue';
  import {Cascader} from 'ant-design-vue';
  import {useRuleFormItem} from '/@/hooks/component/useFormItem';
  import {propTypes} from '/@/utils/propTypes';
  import {useAttrs} from '/@/hooks/core/useAttrs';
  import {loadTreeData} from '/src/views/system/category/category.api';

  export default defineComponent({
    name: 'TwoTypeSelect',
    components: {
      Cascader,
    },
    inheritAttrs: false,
    props: {
      value: propTypes.oneOfType([propTypes.object, propTypes.array]),
      pcode: propTypes.string,
      placeholder: propTypes.string,
    },
    emits: ['options-change', 'change'],
    setup(props, {emit, refs}) {
      const emitData = ref<any[]>([]);
      const attrs = useAttrs();
      const [state] = useRuleFormItem(props, 'value', 'change', emitData);
      const getOptions = ref<any[]>([]);
      const cascaderValue = ref([]);
      /**
       * 监听value变化
       */
      watchEffect(() => {
        if (!props.value) {
          cascaderValue.value = []
          initValue();
        } else {
          let n;
          let m=[];
          for(let i=0;i<2;i++){
            n = props.value.substring(0, props.value.length-3 * i)
            m.unshift(n)
          }
          cascaderValue.value=m;
        }
      });

      /**
       * 将字符串值转化为数组
       */
      function initValue() {
        let param = {
          pcode: !props.pcode ? '0' : props.pcode,
          async: false,
        };
        //根据字典Code, 初始化字典数组
        loadTreeData(param).then((res) => {
          if (res && res.length > 0) {
            addChildren(res)
            getOptions.value = res;
          }
        }).finally(() => {
        })
      }

      function addChildren(children) {
        if (children && children.length > 0) {
          for (let item of children) {
            item.value = item.code;
            item.label = item.title;
            if (item.children) {
              addChildren(item.children)
            }
          }
        }
      }

      function handleChange(array, ...args) {
        if(array){
          let value = array[array.length - 1];
          emit('change', value);
          emit('update:value', value);
        }else{
          emit('change', undefined);
          emit('update:value', undefined);
        }
      }

      return {
        state,
        attrs,
        getOptions,
        cascaderValue,
        handleChange,
      };
    },
  });
</script>
